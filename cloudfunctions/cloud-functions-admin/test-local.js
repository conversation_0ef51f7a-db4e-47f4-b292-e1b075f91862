/**
 * 测试管理端云函数本地模块
 */

async function testLocalModules() {
  try {
    console.log('🧪 开始测试管理端云函数本地模块...')

    // 测试工具模块
    console.log('📦 测试工具模块...')
    const { success, error, invalidParam } = require('./utils/response')
    
    const successResponse = success({ test: 'data' }, '测试成功')
    console.log('✅ 成功响应:', successResponse.success)
    
    const errorResponse = error('测试错误', 'TEST_ERROR')
    console.log('❌ 错误响应:', errorResponse.success)
    
    const paramResponse = invalidParam('testParam', '参数无效')
    console.log('⚠️ 参数错误响应:', paramResponse.success)

    // 测试数据库操作模块
    console.log('📦 测试数据库操作模块...')
    const BaseDB = require('./db/base')
    const announcementsDB = require('./db/announcements')
    const configDB = require('./db/config')
    const usersDB = require('./db/users')
    
    console.log('✅ BaseDB 类:', typeof BaseDB)
    console.log('✅ announcementsDB 实例:', typeof announcementsDB)
    console.log('✅ configDB 实例:', typeof configDB)
    console.log('✅ usersDB 实例:', typeof usersDB)

    // 测试数据库操作方法
    console.log('🔍 测试数据库操作方法...')
    console.log('✅ announcementsDB.getAnnouncementsAdmin:', typeof announcementsDB.getAnnouncementsAdmin)
    console.log('✅ announcementsDB.getAnnouncementStats:', typeof announcementsDB.getAnnouncementStats)
    console.log('✅ configDB.getAllConfigsAdmin:', typeof configDB.getAllConfigsAdmin)
    console.log('✅ configDB.getConfigStats:', typeof configDB.getConfigStats)
    console.log('✅ usersDB.getUserListAdmin:', typeof usersDB.getUserListAdmin)
    console.log('✅ usersDB.getUserStatsAdmin:', typeof usersDB.getUserStatsAdmin)

    // 测试API模块
    console.log('📦 测试API模块...')
    const { createAnnouncement, getAnnouncementListAdmin } = require('./api/announcements-admin')
    const { createConfig, getAllConfigsAdmin } = require('./api/config-admin')
    const { getUserList, getUserStats } = require('./api/user-admin')
    const { getFeedbackListAdmin, replyFeedback } = require('./api/feedback-admin')
    const { getPointsStatsAdmin, adjustUserPoints } = require('./api/points-admin')
    const { getSystemStats, getDashboardStats } = require('./api/system-admin')

    console.log('✅ createAnnouncement:', typeof createAnnouncement)
    console.log('✅ getAnnouncementListAdmin:', typeof getAnnouncementListAdmin)
    console.log('✅ createConfig:', typeof createConfig)
    console.log('✅ getAllConfigsAdmin:', typeof getAllConfigsAdmin)
    console.log('✅ getUserList:', typeof getUserList)
    console.log('✅ getUserStats:', typeof getUserStats)
    console.log('✅ getFeedbackListAdmin:', typeof getFeedbackListAdmin)
    console.log('✅ replyFeedback:', typeof replyFeedback)
    console.log('✅ getPointsStatsAdmin:', typeof getPointsStatsAdmin)
    console.log('✅ adjustUserPoints:', typeof adjustUserPoints)
    console.log('✅ getSystemStats:', typeof getSystemStats)
    console.log('✅ getDashboardStats:', typeof getDashboardStats)

    console.log('🎉 所有本地模块测试通过！')
    
  } catch (error) {
    console.error('❌ 模块测试失败:', error)
    console.error('错误详情:', error.stack)
  }
}

// 如果直接运行此文件，执行测试
if (require.main === module) {
  testLocalModules()
}

module.exports = testLocalModules
