/**
 * 管理端身份验证中间件
 * 实现SECRET_KEY验证机制
 */

const { error } = require('../utils/response')

/**
 * 验证SECRET_KEY
 * @param {Object} event - 云函数事件对象
 * @returns {Object} 验证结果
 */
function validateSecretKey(event) {
  try {
    const { secretKey } = event
    
    // 检查是否提供了密钥
    if (!secretKey) {
      return {
        success: false,
        message: '缺少访问密钥',
        code: 'MISSING_SECRET_KEY'
      }
    }
    
    // 从环境变量获取有效密钥列表
    const validKeysEnv = process.env.ADMIN_SECRET_KEYS
    if (!validKeysEnv) {
      console.error('[Auth] 未配置管理员密钥环境变量 ADMIN_SECRET_KEYS')
      return {
        success: false,
        message: '服务配置错误',
        code: 'CONFIG_ERROR'
      }
    }
    
    // 解析有效密钥列表（支持多个密钥，用逗号分隔）
    const validKeys = validKeysEnv.split(',').map(key => key.trim()).filter(key => key.length > 0)
    
    if (validKeys.length === 0) {
      console.error('[Auth] 管理员密钥配置为空')
      return {
        success: false,
        message: '服务配置错误',
        code: 'CONFIG_ERROR'
      }
    }
    
    // 验证密钥
    if (!validKeys.includes(secretKey)) {
      console.warn('[Auth] 无效的访问密钥尝试:', secretKey.substring(0, 8) + '***')
      return {
        success: false,
        message: '无效的访问密钥',
        code: 'INVALID_SECRET_KEY'
      }
    }
    
    console.log('[Auth] SECRET_KEY验证通过')
    return {
      success: true,
      message: '密钥验证通过'
    }
    
  } catch (err) {
    console.error('[Auth] SECRET_KEY验证失败:', err)
    return {
      success: false,
      message: '身份验证失败',
      code: 'AUTH_ERROR'
    }
  }
}

/**
 * 验证API调用权限
 * @param {string} apiType - API类型
 * @param {Object} event - 云函数事件对象
 * @returns {Object} 验证结果
 */
function validateApiPermission(apiType, event) {
  try {
    // 首先验证SECRET_KEY
    const keyValidation = validateSecretKey(event)
    if (!keyValidation.success) {
      return keyValidation
    }
    
    // SECRET_KEY验证通过，允许调用所有管理端API
    // 不再需要维护API白名单，简化权限管理
    
    console.log('[Auth] API权限验证通过:', apiType)
    return {
      success: true,
      message: 'API权限验证通过'
    }
    
  } catch (err) {
    console.error('[Auth] API权限验证失败:', err)
    return {
      success: false,
      message: '权限验证失败',
      code: 'PERMISSION_ERROR'
    }
  }
}

/**
 * 记录管理操作日志
 * @param {string} apiType - API类型
 * @param {Object} data - 操作数据
 * @param {string} secretKey - 使用的密钥（脱敏）
 */
function logAdminOperation(apiType, data, secretKey) {
  try {
    const logData = {
      timestamp: new Date().toISOString(),
      apiType,
      secretKeyPrefix: secretKey ? secretKey.substring(0, 8) + '***' : 'unknown',
      dataKeys: data ? Object.keys(data) : [],
      ip: process.env.REMOTE_ADDR || 'unknown'
    }
    
    console.log('[AdminLog]', JSON.stringify(logData))
  } catch (err) {
    console.error('[AdminLog] 记录操作日志失败:', err)
  }
}

/**
 * 中间件包装器 - 为API函数添加身份验证
 * @param {Function} apiFunction - API函数
 * @returns {Function} 包装后的函数
 */
function withAuth(apiFunction) {
  return async (event) => {
    try {
      // 验证SECRET_KEY
      const authResult = validateSecretKey(event)
      if (!authResult.success) {
        return authResult
      }
      
      // 记录操作日志
      logAdminOperation(event.type, event.data, event.secretKey)
      
      // 执行API函数
      return await apiFunction(event.data || {})
      
    } catch (err) {
      console.error('[Auth] API执行失败:', err)
      return error(err.message || 'API执行失败', 'API_ERROR')
    }
  }
}

/**
 * 高级中间件包装器 - 支持API类型验证
 * @param {Function} apiFunction - API函数
 * @param {string} requiredApiType - 需要的API类型
 * @returns {Function} 包装后的函数
 */
function withApiAuth(apiFunction, requiredApiType) {
  return async (event) => {
    try {
      // 验证API权限
      const permissionResult = validateApiPermission(requiredApiType, event)
      if (!permissionResult.success) {
        return permissionResult
      }
      
      // 记录操作日志
      logAdminOperation(requiredApiType, event.data, event.secretKey)
      
      // 执行API函数
      return await apiFunction(event.data || {})
      
    } catch (err) {
      console.error('[Auth] API执行失败:', err)
      return error(err.message || 'API执行失败', 'API_ERROR')
    }
  }
}

module.exports = {
  validateSecretKey,
  validateApiPermission,
  logAdminOperation,
  withAuth,
  withApiAuth
}
