/**
 * 用户管理API（管理端）
 * 提供完整的用户管理功能
 */

const { success, error, paginated, statsData, invalidParam, notFound, exportData } = require('../utils/response')
const { wrapAsync } = require('../utils/async-wrapper')
const { validateRequired, validatePagination, validateSorting, validateDateRange, validateId, validateEnum } = require('../utils/validators')

// 使用本地的数据库操作类
const usersDB = require('../db/users')

/**
 * 获取用户列表（管理端）
 */
exports.getUserList = wrapAsync(async (params = {}) => {
  const {
    status = 'all',
    vipStatus = 'all',
    isAdmin = 'all',
    page = 1,
    pageSize = 20,
    sortBy = 'createTime',
    sortOrder = 'desc',
    keyword = '',
    startDate,
    endDate
  } = params

  // 验证分页参数
  const paginationValidation = validatePagination(params, 100)
  if (!paginationValidation.success) {
    return paginationValidation
  }

  // 验证排序参数
  const allowedSortFields = ['createTime', 'updateTime', 'lastLoginTime', 'loginCount', 'nickname']
  const sortingValidation = validateSorting(params, allowedSortFields)
  if (!sortingValidation.success) {
    return sortingValidation
  }

  // 验证日期范围
  const dateValidation = validateDateRange(params)
  if (!dateValidation.success) {
    return dateValidation
  }

  try {
    const result = await usersDB.getUserListAdmin({
      status,
      vipStatus,
      isAdmin,
      keyword,
      startDate: dateValidation.data.startDate,
      endDate: dateValidation.data.endDate,
      sortBy,
      sortOrder,
      limit: paginationValidation.data.limit,
      skip: paginationValidation.data.skip
    })

    if (!result.success) {
      return error(result.message || '获取用户列表失败')
    }

    return paginated(
      result.data.list,
      result.data.total,
      page,
      pageSize,
      '获取用户列表成功'
    )
  } catch (err) {
    console.error('获取用户列表失败:', err)
    return error('获取用户列表失败')
  }
})

/**
 * 获取用户详情（管理端）
 */
exports.getUserDetail = wrapAsync(async (params = {}) => {
  // 验证必填参数
  const validation = validateRequired(params, ['userId'])
  if (!validation.success) {
    return validation
  }

  const { userId } = params

  // 验证用户ID
  const idValidation = validateId(userId, 'userId')
  if (!idValidation.success) {
    return idValidation
  }

  try {
    const result = await usersDB.getUserDetailAdmin(userId)
    
    if (!result.success) {
      return error(result.message || '获取用户详情失败')
    }

    if (!result.data) {
      return notFound('用户')
    }

    return success(result.data, '获取用户详情成功')
  } catch (err) {
    console.error('获取用户详情失败:', err)
    return error('获取用户详情失败')
  }
})

/**
 * 更新用户状态（管理端）
 */
exports.updateUserStatus = wrapAsync(async (params = {}) => {
  // 验证必填参数
  const validation = validateRequired(params, ['userId'])
  if (!validation.success) {
    return validation
  }

  const { userId, status, isAdmin, vipStatus, vipExpireTime, tags, notes } = params

  // 验证用户ID
  const idValidation = validateId(userId, 'userId')
  if (!idValidation.success) {
    return idValidation
  }

  try {
    // 检查用户是否存在
    const existingResult = await usersDB.getUserDetailAdmin(userId)
    if (!existingResult.success || !existingResult.data) {
      return notFound('用户')
    }

    // 构建更新数据
    const updateData = {}
    
    if (status !== undefined) {
      const validStatuses = ['active', 'inactive', 'banned']
      const statusValidation = validateEnum(status, 'status', validStatuses)
      if (!statusValidation.success) {
        return statusValidation
      }
      updateData.status = status
    }
    
    if (isAdmin !== undefined) {
      if (typeof isAdmin !== 'boolean') {
        return invalidParam('isAdmin', '管理员状态必须是布尔值')
      }
      updateData.isAdmin = isAdmin
    }
    
    if (vipStatus !== undefined) {
      if (typeof vipStatus !== 'boolean') {
        return invalidParam('vipStatus', 'VIP状态必须是布尔值')
      }
      updateData['vip.status'] = vipStatus
      
      if (vipStatus && vipExpireTime) {
        const expireDate = new Date(vipExpireTime)
        if (isNaN(expireDate.getTime())) {
          return invalidParam('vipExpireTime', 'VIP过期时间格式无效')
        }
        updateData['vip.expireTime'] = expireDate
      }
    }
    
    if (tags !== undefined) {
      if (!Array.isArray(tags)) {
        return invalidParam('tags', '标签必须是数组')
      }
      updateData.tags = tags
    }
    
    if (notes !== undefined) {
      if (typeof notes !== 'string') {
        return invalidParam('notes', '备注必须是字符串')
      }
      if (notes.length > 1000) {
        return invalidParam('notes', '备注不能超过1000字符')
      }
      updateData.adminNotes = notes
    }

    const result = await usersDB.updateUserStatusAdmin(userId, updateData)
    
    if (!result.success) {
      return error(result.message || '更新用户状态失败')
    }

    return success(result.data, '用户状态更新成功')
  } catch (err) {
    console.error('更新用户状态失败:', err)
    return error('更新用户状态失败')
  }
})

/**
 * 获取用户统计（管理端）
 */
exports.getUserStats = wrapAsync(async (params = {}) => {
  const { period = '30d' } = params

  // 验证时间周期
  const validPeriods = ['7d', '30d', '90d', '1y', 'all']
  const periodValidation = validateEnum(period, 'period', validPeriods)
  if (!periodValidation.success) {
    return periodValidation
  }

  try {
    const result = await usersDB.getUserStatsAdmin(period)
    
    if (!result.success) {
      return error(result.message || '获取用户统计失败')
    }

    return statsData(result.data, '获取用户统计成功')
  } catch (err) {
    console.error('获取用户统计失败:', err)
    return error('获取用户统计失败')
  }
})

/**
 * 导出用户数据（管理端）
 */
exports.exportUserData = wrapAsync(async (params = {}) => {
  const {
    format = 'json',
    status = 'all',
    vipStatus = 'all',
    isAdmin = 'all',
    fields = 'basic',
    startDate,
    endDate,
    limit = 1000
  } = params

  // 验证导出格式
  const validFormats = ['json', 'csv']
  const formatValidation = validateEnum(format, 'format', validFormats)
  if (!formatValidation.success) {
    return formatValidation
  }

  // 验证字段选择
  const validFields = ['basic', 'detailed', 'all']
  const fieldsValidation = validateEnum(fields, 'fields', validFields)
  if (!fieldsValidation.success) {
    return fieldsValidation
  }

  // 验证导出数量限制
  if (limit > 10000) {
    return invalidParam('limit', '单次导出不能超过10000条记录')
  }

  // 验证日期范围
  const dateValidation = validateDateRange(params)
  if (!dateValidation.success) {
    return dateValidation
  }

  try {
    const result = await usersDB.exportUserDataAdmin({
      status,
      vipStatus,
      isAdmin,
      fields,
      startDate: dateValidation.data.startDate,
      endDate: dateValidation.data.endDate,
      limit
    })

    if (!result.success) {
      return error(result.message || '导出用户数据失败')
    }

    // 生成文件名
    const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-')
    const filename = `users_export_${timestamp}.${format}`

    return exportData(result.data, filename, format, '用户数据导出成功')
  } catch (err) {
    console.error('导出用户数据失败:', err)
    return error('导出用户数据失败')
  }
})

/**
 * 获取VIP用户列表（管理端）
 */
exports.getVipUsersAdmin = wrapAsync(async (params = {}) => {
  const {
    status = 'all', // all, active, expired
    page = 1,
    pageSize = 20,
    sortBy = 'vipExpireAt',
    sortOrder = 'desc',
    keyword = '',
    startDate,
    endDate
  } = params

  // 验证分页参数
  const paginationValidation = validatePagination(params, 100)
  if (!paginationValidation.success) {
    return paginationValidation
  }

  try {
    const result = await usersDB.getVipUsersAdmin({
      status,
      page: parseInt(page),
      pageSize: parseInt(pageSize),
      sortBy,
      sortOrder,
      keyword,
      startDate,
      endDate
    })

    return paginated(result.list, result.total, parseInt(page), parseInt(pageSize), '获取VIP用户列表成功')
  } catch (err) {
    console.error('获取VIP用户列表失败:', err)
    return error('获取VIP用户列表失败', 'GET_VIP_USERS_ERROR', err.message)
  }
})

/**
 * 获取VIP记录列表（管理端）
 */
exports.getVipRecordsAdmin = wrapAsync(async (params = {}) => {
  const {
    userId,
    type = 'all', // all, grant, renew, expire
    page = 1,
    pageSize = 20,
    sortBy = 'createTime',
    sortOrder = 'desc',
    startDate,
    endDate
  } = params

  // 验证分页参数
  const paginationValidation = validatePagination(params, 100)
  if (!paginationValidation.success) {
    return paginationValidation
  }

  try {
    const result = await usersDB.getVipRecordsAdmin({
      userId,
      type,
      page: parseInt(page),
      pageSize: parseInt(pageSize),
      sortBy,
      sortOrder,
      startDate,
      endDate
    })

    return paginated(result.list, result.total, parseInt(page), parseInt(pageSize), '获取VIP记录列表成功')
  } catch (err) {
    console.error('获取VIP记录列表失败:', err)
    return error('获取VIP记录列表失败', 'GET_VIP_RECORDS_ERROR', err.message)
  }
})

/**
 * 获取VIP统计数据（管理端）
 */
exports.getVipStatsAdmin = wrapAsync(async (params = {}) => {
  const {
    period = '30d' // 7d, 30d, 90d, 1y
  } = params

  try {
    const stats = await usersDB.getVipStatsAdmin({ period })

    return statsData(stats, '获取VIP统计数据成功')
  } catch (err) {
    console.error('获取VIP统计数据失败:', err)
    return error('获取VIP统计数据失败', 'GET_VIP_STATS_ERROR', err.message)
  }
})

/**
 * 赠送VIP（管理端）
 */
exports.grantVipAdmin = wrapAsync(async (params = {}) => {
  const { userId, duration, reason = '管理员赠送' } = params

  // 验证必需参数
  const requiredValidation = validateRequired(params, ['userId', 'duration'])
  if (!requiredValidation.success) {
    return requiredValidation
  }

  // 验证用户ID
  const userIdValidation = validateId(userId, 'userId')
  if (!userIdValidation.success) {
    return userIdValidation
  }

  // 验证持续时间
  if (!Number.isInteger(duration) || duration <= 0) {
    return invalidParam('duration', '持续时间必须是正整数（天数）')
  }

  try {
    const result = await usersDB.grantVipAdmin({
      userId,
      duration: parseInt(duration),
      reason
    })

    if (!result.success) {
      return error(result.message, 'GRANT_VIP_ERROR')
    }

    return success(result.data, '赠送VIP成功')
  } catch (err) {
    console.error('赠送VIP失败:', err)
    return error('赠送VIP失败', 'GRANT_VIP_ERROR', err.message)
  }
})

/**
 * 取消VIP（管理端）
 */
exports.revokeVipAdmin = wrapAsync(async (params = {}) => {
  const { userId, reason = '管理员取消' } = params

  // 验证必需参数
  const requiredValidation = validateRequired(params, ['userId'])
  if (!requiredValidation.success) {
    return requiredValidation
  }

  // 验证用户ID
  const userIdValidation = validateId(userId, 'userId')
  if (!userIdValidation.success) {
    return userIdValidation
  }

  try {
    const result = await usersDB.revokeVipAdmin({
      userId,
      reason
    })

    if (!result.success) {
      return error(result.message, 'REVOKE_VIP_ERROR')
    }

    return success(result.data, '取消VIP成功')
  } catch (err) {
    console.error('取消VIP失败:', err)
    return error('取消VIP失败', 'REVOKE_VIP_ERROR', err.message)
  }
})



/**
 * 获取用户积分历史（管理端）
 */
exports.getUserPointsHistoryAdmin = wrapAsync(async (params = {}) => {
  console.log('[getUserPointsHistoryAdmin] 接收到的参数:', JSON.stringify(params))

  const {
    userId,
    type = 'all',
    page = 1,
    pageSize = 20,
    startDate,
    endDate
  } = params

  // 验证必需参数
  const requiredValidation = validateRequired(params, ['userId'])
  if (!requiredValidation.success) {
    console.log('[getUserPointsHistoryAdmin] 参数验证失败:', requiredValidation)
    return requiredValidation
  }

  // 验证用户ID
  const userIdValidation = validateId(userId, 'userId')
  if (!userIdValidation.success) {
    return userIdValidation
  }

  // 验证分页参数
  const paginationValidation = validatePagination(params, 100)
  if (!paginationValidation.success) {
    return paginationValidation
  }

  try {
    const result = await usersDB.getUserPointsHistoryAdmin({
      userId,
      type,
      page: parseInt(page),
      pageSize: parseInt(pageSize),
      startDate,
      endDate
    })

    return paginated(result.list, result.total, parseInt(page), parseInt(pageSize), '获取用户积分历史成功')
  } catch (err) {
    console.error('获取用户积分历史失败:', err)
    return error('获取用户积分历史失败', 'GET_USER_POINTS_HISTORY_ERROR', err.message)
  }
})
