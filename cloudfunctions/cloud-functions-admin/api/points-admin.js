/**
 * 积分管理API（管理端）
 * 提供完整的积分管理功能
 */

const { success, error, paginated, statsData, invalidParam, notFound, exportData } = require('../utils/response')
const { wrapAsync } = require('../utils/async-wrapper')
const { validateRequired, validatePagination, validateSorting, validateDateRange, validateId, validateEnum, validateNumberRange } = require('../utils/validators')

// 使用本地的数据库操作类
const pointsDB = require('../db/points')

/**
 * 获取积分统计（管理端）
 */
exports.getPointsStatsAdmin = wrapAsync(async (params = {}) => {
  const { period = '30d' } = params

  // 验证时间周期
  const validPeriods = ['7d', '30d', '90d', '1y', 'all']
  const periodValidation = validateEnum(period, 'period', validPeriods)
  if (!periodValidation.success) {
    return periodValidation
  }

  try {
    const result = await pointsDB.getPointsStatsAdmin(period)
    
    if (!result.success) {
      return error(result.message || '获取积分统计失败')
    }

    return statsData(result.data, '获取积分统计成功')
  } catch (err) {
    console.error('获取积分统计失败:', err)
    return error('获取积分统计失败')
  }
})

/**
 * 手动调整用户积分（管理端）
 */
exports.adjustUserPoints = wrapAsync(async (params = {}) => {
  // 验证必填参数
  const validation = validateRequired(params, ['userId', 'points', 'reason'])
  if (!validation.success) {
    return validation
  }

  const { userId, points, reason, adminId = 'system' } = params

  // 验证用户ID
  const idValidation = validateId(userId, 'userId')
  if (!idValidation.success) {
    return idValidation
  }

  // 验证积分数量
  const pointsValidation = validateNumberRange(points, 'points', -999999, 999999)
  if (!pointsValidation.success) {
    return pointsValidation
  }

  // 验证原因
  if (reason.length > 200) {
    return invalidParam('reason', '调整原因不能超过200字符')
  }

  try {
    const adjustData = {
      userId,
      points,
      reason: reason.trim(),
      adminId,
      type: points > 0 ? 'admin_add' : 'admin_deduct'
    }

    const result = await pointsDB.adjustUserPoints(adjustData)
    
    if (!result.success) {
      return error(result.message || '调整用户积分失败')
    }

    return success(result.data, '用户积分调整成功')
  } catch (err) {
    console.error('调整用户积分失败:', err)
    return error('调整用户积分失败')
  }
})

/**
 * 获取积分记录（管理端）
 */
exports.getPointsRecordsAdmin = wrapAsync(async (params = {}) => {
  const {
    userId = 'all',
    type = 'all',
    page = 1,
    pageSize = 20,
    sortBy = 'createTime',
    sortOrder = 'desc',
    startDate,
    endDate
  } = params

  // 验证分页参数
  const paginationValidation = validatePagination(params, 100)
  if (!paginationValidation.success) {
    return paginationValidation
  }

  // 验证排序参数
  const allowedSortFields = ['createTime', 'points', 'type']
  const sortingValidation = validateSorting(params, allowedSortFields)
  if (!sortingValidation.success) {
    return sortingValidation
  }

  // 验证日期范围
  const dateValidation = validateDateRange(params)
  if (!dateValidation.success) {
    return dateValidation
  }

  try {
    const result = await pointsDB.getPointsRecordsAdmin({
      userId,
      type,
      startDate: dateValidation.data.startDate,
      endDate: dateValidation.data.endDate,
      sortBy,
      sortOrder,
      limit: paginationValidation.data.limit,
      skip: paginationValidation.data.skip
    })

    if (!result.success) {
      return error(result.message || '获取积分记录失败')
    }

    return paginated(
      result.data.list,
      result.data.total,
      page,
      pageSize,
      '获取积分记录成功'
    )
  } catch (err) {
    console.error('获取积分记录失败:', err)
    return error('获取积分记录失败')
  }
})

/**
 * 导出积分数据（管理端）
 */
exports.exportPointsData = wrapAsync(async (params = {}) => {
  const {
    format = 'json',
    userId = 'all',
    type = 'all',
    startDate,
    endDate,
    limit = 1000
  } = params

  // 验证导出格式
  const validFormats = ['json', 'csv']
  const formatValidation = validateEnum(format, 'format', validFormats)
  if (!formatValidation.success) {
    return formatValidation
  }

  // 验证导出数量限制
  if (limit > 10000) {
    return invalidParam('limit', '单次导出不能超过10000条记录')
  }

  // 验证日期范围
  const dateValidation = validateDateRange(params)
  if (!dateValidation.success) {
    return dateValidation
  }

  try {
    const result = await pointsDB.exportPointsDataAdmin({
      userId,
      type,
      startDate: dateValidation.data.startDate,
      endDate: dateValidation.data.endDate,
      limit
    })

    if (!result.success) {
      return error(result.message || '导出积分数据失败')
    }

    // 生成文件名
    const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-')
    const filename = `points_export_${timestamp}.${format}`

    return exportData(result.data, filename, format, '积分数据导出成功')
  } catch (err) {
    console.error('导出积分数据失败:', err)
    return error('导出积分数据失败')
  }
})

/**
 * 获取用户积分排行榜（管理端）
 */
exports.getPointsLeaderboard = wrapAsync(async (params = {}) => {
  const {
    period = '30d',
    limit = 50
  } = params

  // 验证时间周期
  const validPeriods = ['7d', '30d', '90d', '1y', 'all']
  const periodValidation = validateEnum(period, 'period', validPeriods)
  if (!periodValidation.success) {
    return periodValidation
  }

  // 验证排行榜数量
  if (limit > 100) {
    return invalidParam('limit', '排行榜数量不能超过100')
  }

  try {
    const result = await pointsDB.getPointsLeaderboard({ period, limit })

    if (!result.success) {
      return error(result.message || '获取积分排行榜失败')
    }

    return success(result.data, '获取积分排行榜成功')
  } catch (err) {
    console.error('获取积分排行榜失败:', err)
    return error('获取积分排行榜失败')
  }
})

/**
 * 批量调整积分（管理端）
 */
exports.batchAdjustPoints = wrapAsync(async (params = {}) => {
  // 验证必填参数
  const validation = validateRequired(params, ['adjustments', 'reason'])
  if (!validation.success) {
    return validation
  }

  const { adjustments, reason, adminId = 'system' } = params

  // 验证调整列表
  if (!Array.isArray(adjustments) || adjustments.length === 0) {
    return invalidParam('adjustments', '调整列表不能为空')
  }

  if (adjustments.length > 100) {
    return invalidParam('adjustments', '单次批量调整不能超过100个用户')
  }

  // 验证每个调整项
  for (const adjustment of adjustments) {
    if (!adjustment.userId || typeof adjustment.points !== 'number') {
      return invalidParam('adjustments', '调整项必须包含userId和points字段')
    }
  }

  try {
    const result = await pointsDB.batchAdjustPoints(adjustments, reason, adminId)
    
    if (!result.success) {
      return error(result.message || '批量调整积分失败')
    }

    return success(result.data, '批量调整积分成功')
  } catch (err) {
    console.error('批量调整积分失败:', err)
    return error('批量调整积分失败')
  }
})

/**
 * 获取积分配置（管理端）
 */
exports.getPointsConfig = wrapAsync(async (params = {}) => {
  try {
    const result = await pointsDB.getPointsConfig()
    
    if (!result.success) {
      return error(result.message || '获取积分配置失败')
    }

    return success(result.data, '获取积分配置成功')
  } catch (err) {
    console.error('获取积分配置失败:', err)
    return error('获取积分配置失败')
  }
})

/**
 * 更新积分配置（管理端）
 */
exports.updatePointsConfig = wrapAsync(async (params = {}) => {
  // 验证必填参数
  const validation = validateRequired(params, ['config'])
  if (!validation.success) {
    return validation
  }

  const { config } = params

  try {
    const result = await pointsDB.updatePointsConfig(config)
    
    if (!result.success) {
      return error(result.message || '更新积分配置失败')
    }

    return success(result.data, '积分配置更新成功')
  } catch (err) {
    console.error('更新积分配置失败:', err)
    return error('更新积分配置失败')
  }
})
