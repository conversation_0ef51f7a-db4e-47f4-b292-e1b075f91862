/**
 * 友情应用管理API（管理端）
 */

const cloud = require('wx-server-sdk')
const { wrapAsync } = require('../utils/async-wrapper')
const { error, success, invalidParam } = require('../utils/response')

// 初始化数据库
const db = cloud.database()

/**
 * 创建友情应用
 */
exports.createFriendApp = wrapAsync(async (params = {}) => {
  const { name, description, url, icon, category, sortOrder = 0, isVisible = true } = params

  // 参数验证
  if (!name || !name.trim()) {
    return invalidParam('name', '应用名称不能为空')
  }
  if (!description || !description.trim()) {
    return invalidParam('description', '应用描述不能为空')
  }
  if (!url || !url.trim()) {
    return invalidParam('url', '应用链接不能为空')
  }

  try {
    const friendApp = {
      name: name.trim(),
      description: description.trim(),
      url: url.trim(),
      icon: icon || '',
      category: category || 'other',
      sortOrder: parseInt(sortOrder) || 0,
      isVisible: Boolean(isVisible),
      clickCount: 0,
      createTime: new Date().toISOString(),
      updateTime: new Date().toISOString()
    }

    const result = await db.collection('friend-apps').add({
      data: friendApp
    })

    console.log('[FriendAppsAdmin] 友情应用创建成功:', result._id)

    return success({ id: result._id, ...friendApp }, '友情应用创建成功')
  } catch (err) {
    console.error('创建友情应用失败:', err)
    return error('创建友情应用失败')
  }
})

/**
 * 更新友情应用
 */
exports.updateFriendApp = wrapAsync(async (params = {}) => {
  const { id, name, description, url, icon, category, sortOrder, isVisible } = params

  if (!id) {
    return invalidParam('id', '应用ID不能为空')
  }

  try {
    const updateData = {
      updateTime: new Date().toISOString()
    }

    if (name !== undefined) updateData.name = name.trim()
    if (description !== undefined) updateData.description = description.trim()
    if (url !== undefined) updateData.url = url.trim()
    if (icon !== undefined) updateData.icon = icon
    if (category !== undefined) updateData.category = category
    if (sortOrder !== undefined) updateData.sortOrder = parseInt(sortOrder) || 0
    if (isVisible !== undefined) updateData.isVisible = Boolean(isVisible)

    await db.collection('friend-apps').doc(id).update({
      data: updateData
    })

    console.log('[FriendAppsAdmin] 友情应用更新成功:', id)

    return success(null, '友情应用更新成功')
  } catch (err) {
    console.error('更新友情应用失败:', err)
    return error('更新友情应用失败')
  }
})

/**
 * 删除友情应用
 */
exports.deleteFriendApp = wrapAsync(async (params = {}) => {
  const { id } = params

  if (!id) {
    return invalidParam('id', '应用ID不能为空')
  }

  try {
    await db.collection('friend-apps').doc(id).remove()

    console.log('[FriendAppsAdmin] 友情应用删除成功:', id)

    return success(null, '友情应用删除成功')
  } catch (err) {
    console.error('删除友情应用失败:', err)
    return error('删除友情应用失败')
  }
})

/**
 * 获取友情应用列表（管理端）
 */
exports.getFriendAppListAdmin = wrapAsync(async (params = {}) => {
  const { page = 1, pageSize = 20, category, isVisible, keyword } = params

  try {
    const skip = (page - 1) * pageSize
    let query = db.collection('friend-apps')

    // 添加筛选条件
    const whereConditions = {}
    if (category && category !== 'all') {
      whereConditions.category = category
    }
    if (isVisible !== undefined && isVisible !== 'all') {
      whereConditions.isVisible = isVisible === 'true' || isVisible === true
    }

    if (Object.keys(whereConditions).length > 0) {
      query = query.where(whereConditions)
    }

    // 获取总数
    const countResult = await query.count()
    const total = countResult.total

    // 获取数据
    let dataQuery = query.orderBy('sortOrder', 'desc').orderBy('createTime', 'desc')

    if (skip > 0) {
      dataQuery = dataQuery.skip(skip)
    }
    dataQuery = dataQuery.limit(pageSize)

    const result = await dataQuery.get()
    let apps = result.data.map(item => ({
      _id: item._id,
      ...item
    }))

    // 关键词搜索（前端过滤）
    if (keyword && keyword.trim()) {
      const searchTerm = keyword.trim().toLowerCase()
      apps = apps.filter(app =>
        (app.name && app.name.toLowerCase().includes(searchTerm)) ||
        (app.description && app.description.toLowerCase().includes(searchTerm))
      )
    }

    console.log(`[FriendAppsAdmin] 获取友情应用列表成功，共 ${apps.length} 条记录`)

    return success({
      list: apps,
      total: keyword ? apps.length : total,
      hasMore: skip + apps.length < total
    }, '获取友情应用列表成功')
  } catch (err) {
    console.error('获取友情应用列表失败:', err)
    return error('获取友情应用列表失败')
  }
})

/**
 * 获取友情应用统计
 */
exports.getFriendAppStatsAdmin = wrapAsync(async (params = {}) => {
  const { period = '30d' } = params

  try {
    // 获取总数统计
    const totalResult = await db.collection('friend-apps').count()
    const visibleResult = await db.collection('friend-apps').where({
      isVisible: true
    }).count()

    // 获取点击统计
    const appsResult = await db.collection('friend-apps').get()
    const totalClicks = appsResult.data.reduce((sum, app) => sum + (app.clickCount || 0), 0)

    // 按分类统计
    const categoryStats = {}
    appsResult.data.forEach(app => {
      const category = app.category || 'other'
      if (!categoryStats[category]) {
        categoryStats[category] = { count: 0, clicks: 0 }
      }
      categoryStats[category].count++
      categoryStats[category].clicks += app.clickCount || 0
    })

    const stats = {
      total: totalResult.total,
      visible: visibleResult.total,
      hidden: totalResult.total - visibleResult.total,
      totalClicks,
      averageClicks: totalResult.total > 0 ? Math.round(totalClicks / totalResult.total) : 0,
      categoryStats,
      period
    }

    console.log('[FriendAppsAdmin] 获取友情应用统计成功:', stats)

    return success(stats, '获取友情应用统计成功')
  } catch (err) {
    console.error('获取友情应用统计失败:', err)
    return error('获取友情应用统计失败')
  }
})

/**
 * 切换友情应用状态
 */
exports.toggleFriendAppStatus = wrapAsync(async (params = {}) => {
  const { id, isVisible } = params

  if (!id) {
    return invalidParam('id', '应用ID不能为空')
  }

  try {
    await db.collection('friend-apps').doc(id).update({
      data: {
        isVisible: Boolean(isVisible),
        updateTime: new Date().toISOString()
      }
    })

    console.log('[FriendAppsAdmin] 友情应用状态切换成功:', id, isVisible)

    return success(null, '应用状态切换成功')
  } catch (err) {
    console.error('切换友情应用状态失败:', err)
    return error('切换应用状态失败')
  }
})

/**
 * 记录友情应用点击
 */
exports.recordFriendAppClick = wrapAsync(async (params = {}) => {
  const { id } = params

  if (!id) {
    return invalidParam('id', '应用ID不能为空')
  }

  try {
    // 增加点击计数
    await db.collection('friend-apps').doc(id).update({
      data: {
        clickCount: db.command.inc(1),
        lastClickTime: new Date().toISOString()
      }
    })

    console.log('[FriendAppsAdmin] 友情应用点击记录成功:', id)

    return success(null, '点击记录成功')
  } catch (err) {
    console.error('记录友情应用点击失败:', err)
    return error('记录点击失败')
  }
})

/**
 * 批量更新友情应用排序
 */
exports.batchUpdateFriendAppOrder = wrapAsync(async (params = {}) => {
  const { apps } = params

  if (!Array.isArray(apps) || apps.length === 0) {
    return invalidParam('apps', '应用列表不能为空')
  }

  try {
    const batch = db.batch()

    apps.forEach((app, index) => {
      if (app.id) {
        const docRef = db.collection('friend-apps').doc(app.id)
        batch.update(docRef, {
          data: {
            sortOrder: index,
            updateTime: new Date().toISOString()
          }
        })
      }
    })

    await batch.commit()

    console.log('[FriendAppsAdmin] 批量更新友情应用排序成功')

    return success(null, '排序更新成功')
  } catch (err) {
    console.error('批量更新友情应用排序失败:', err)
    return error('排序更新失败')
  }
})
