/**
 * 用户数据管理API（管理端）
 * 提供用户数据统计、清理和导出功能
 */

const { success, error, paginated, statsData, invalidParam, exportData } = require('../utils/response')
const { wrapAsync } = require('../utils/async-wrapper')
const { validateRequired, validatePagination, validateSorting, validateDateRange, validateEnum } = require('../utils/validators')

// 使用本地的数据库操作类
const BaseDB = require('../db/base')

// 用户数据管理类
class UserDataDB extends BaseDB {
  constructor() {
    super('user_data')
  }

  /**
   * 获取所有用户数据（管理端）
   * @param {Object} options - 查询选项
   * @returns {Promise<Object>} 查询结果
   */
  async getAllUserDataAdmin(options = {}) {
    try {
      const {
        dataType = 'all',
        userId = 'all',
        keyword = '',
        startDate,
        endDate,
        sortBy = 'createTime',
        sortOrder = 'desc',
        limit = 20,
        skip = 0
      } = options

      console.log(`[UserDataDB] 管理端获取用户数据: dataType=${dataType}, userId=${userId}`)

      // 构建查询条件
      const where = {}
      
      if (dataType !== 'all') {
        where.type = dataType
      }
      
      if (userId !== 'all') {
        where.userId = userId
      }
      
      if (keyword) {
        where.$or = [
          { key: new RegExp(keyword, 'i') },
          { description: new RegExp(keyword, 'i') }
        ]
      }
      
      if (startDate || endDate) {
        where.createTime = {}
        if (startDate) {
          where.createTime.$gte = startDate
        }
        if (endDate) {
          where.createTime.$lte = endDate
        }
      }

      // 查询总数
      const countResult = await this.count(where)
      const total = countResult.success ? countResult.data : 0

      // 查询数据
      const dataResult = await this.find(where, {
        limit,
        skip,
        orderBy: sortBy,
        order: sortOrder
      })

      if (!dataResult.success) {
        return dataResult
      }

      console.log(`[UserDataDB] 管理端用户数据查询成功，共 ${dataResult.data.length} 条记录，总计 ${total} 条`)

      return {
        success: true,
        data: {
          list: dataResult.data,
          total,
          hasMore: skip + dataResult.data.length < total
        }
      }
    } catch (error) {
      console.error('[UserDataDB] 管理端获取用户数据失败:', error)
      return { 
        success: false, 
        message: error.message 
      }
    }
  }

  /**
   * 获取用户数据统计信息（管理端）
   * @param {string} period - 统计周期
   * @returns {Promise<Object>} 统计结果
   */
  async getUserDataStatsAdmin(period = '30d') {
    try {
      console.log(`[UserDataDB] 获取用户数据统计信息: ${period}`)

      // 计算时间范围
      let startDate = null
      const now = new Date()
      
      switch (period) {
        case '7d':
          startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
          break
        case '30d':
          startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
          break
        case '90d':
          startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000)
          break
        case '1y':
          startDate = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000)
          break
        case 'all':
        default:
          startDate = null
          break
      }

      const baseWhere = {}
      if (startDate) {
        baseWhere.createTime = { $gte: startDate }
      }

      // 总数据条数
      const totalResult = await this.count(baseWhere)
      const total = totalResult.success ? totalResult.data : 0

      // 今日新增数据
      const today = new Date()
      today.setHours(0, 0, 0, 0)
      const todayResult = await this.count({ 
        createTime: { $gte: today }
      })
      const todayCount = todayResult.success ? todayResult.data : 0

      const stats = {
        total,
        todayCount,
        averageDaily: period !== 'all' ? Math.round(total / parseInt(period)) : 0,
        period,
        generatedAt: new Date().toISOString()
      }

      console.log('[UserDataDB] 用户数据统计信息获取成功')

      return {
        success: true,
        data: stats
      }
    } catch (error) {
      console.error('[UserDataDB] 获取用户数据统计失败:', error)
      return { 
        success: false, 
        message: error.message 
      }
    }
  }

  /**
   * 清理用户数据（管理端）
   * @param {Object} options - 清理选项
   * @returns {Promise<Object>} 清理结果
   */
  async cleanupUserDataAdmin(options = {}) {
    try {
      const {
        dataType = 'all',
        olderThan = 90, // 天数
        dryRun = true // 是否为试运行
      } = options

      console.log(`[UserDataDB] 清理用户数据: dataType=${dataType}, olderThan=${olderThan}天, dryRun=${dryRun}`)

      // 计算清理时间点
      const cleanupDate = new Date()
      cleanupDate.setDate(cleanupDate.getDate() - olderThan)

      // 构建查询条件
      const where = {
        createTime: { $lt: cleanupDate }
      }
      
      if (dataType !== 'all') {
        where.type = dataType
      }

      if (dryRun) {
        // 试运行，只统计数量
        const countResult = await this.count(where)
        const count = countResult.success ? countResult.data : 0

        return {
          success: true,
          data: {
            dryRun: true,
            wouldDelete: count,
            cleanupDate: cleanupDate.toISOString()
          }
        }
      } else {
        // 实际删除
        const result = await this.delete(where)
        
        return {
          success: true,
          data: {
            dryRun: false,
            deleted: result.success ? result.data.stats?.removed || 0 : 0,
            cleanupDate: cleanupDate.toISOString()
          }
        }
      }
    } catch (error) {
      console.error('[UserDataDB] 清理用户数据失败:', error)
      return { 
        success: false, 
        message: error.message 
      }
    }
  }

  /**
   * 导出所有数据（管理端）
   * @param {Object} options - 导出选项
   * @returns {Promise<Object>} 导出结果
   */
  async exportAllDataAdmin(options = {}) {
    try {
      const {
        dataType = 'all',
        startDate,
        endDate,
        limit = 1000
      } = options

      console.log(`[UserDataDB] 导出所有数据: dataType=${dataType}, limit=${limit}`)

      // 构建查询条件
      const where = {}
      
      if (dataType !== 'all') {
        where.type = dataType
      }
      
      if (startDate || endDate) {
        where.createTime = {}
        if (startDate) {
          where.createTime.$gte = startDate
        }
        if (endDate) {
          where.createTime.$lte = endDate
        }
      }

      // 查询数据
      const result = await this.find(where, {
        orderBy: 'createTime',
        order: 'desc',
        limit
      })

      if (!result.success) {
        return result
      }

      console.log(`[UserDataDB] 数据导出成功，共 ${result.data.length} 条记录`)

      return {
        success: true,
        data: result.data
      }
    } catch (error) {
      console.error('[UserDataDB] 导出数据失败:', error)
      return { 
        success: false, 
        message: error.message 
      }
    }
  }
}

const userDataDB = new UserDataDB()

/**
 * 获取所有用户数据（管理端）
 */
exports.getAllUserDataAdmin = wrapAsync(async (params = {}) => {
  const {
    dataType = 'all',
    userId = 'all',
    keyword = '',
    startDate,
    endDate,
    page = 1,
    pageSize = 20,
    sortBy = 'createTime',
    sortOrder = 'desc'
  } = params

  // 验证分页参数
  const paginationValidation = validatePagination(params, 100)
  if (!paginationValidation.success) {
    return paginationValidation
  }

  // 验证排序参数
  const allowedSortFields = ['createTime', 'updateTime', 'userId', 'type']
  const sortingValidation = validateSorting(params, allowedSortFields)
  if (!sortingValidation.success) {
    return sortingValidation
  }

  // 验证日期范围
  const dateValidation = validateDateRange(params)
  if (!dateValidation.success) {
    return dateValidation
  }

  try {
    const result = await userDataDB.getAllUserDataAdmin({
      dataType,
      userId,
      keyword,
      startDate: dateValidation.data.startDate,
      endDate: dateValidation.data.endDate,
      sortBy,
      sortOrder,
      limit: paginationValidation.data.limit,
      skip: paginationValidation.data.skip
    })

    if (!result.success) {
      return error(result.message || '获取用户数据失败')
    }

    return paginated(
      result.data.list,
      result.data.total,
      page,
      pageSize,
      '获取用户数据成功'
    )
  } catch (err) {
    console.error('获取用户数据失败:', err)
    return error('获取用户数据失败')
  }
})

/**
 * 获取用户数据统计（管理端）
 */
exports.getUserDataStatsAdmin = wrapAsync(async (params = {}) => {
  const { period = '30d' } = params

  // 验证时间周期
  const validPeriods = ['7d', '30d', '90d', '1y', 'all']
  const periodValidation = validateEnum(period, 'period', validPeriods)
  if (!periodValidation.success) {
    return periodValidation
  }

  try {
    const result = await userDataDB.getUserDataStatsAdmin(period)
    
    if (!result.success) {
      return error(result.message || '获取用户数据统计失败')
    }

    return statsData(result.data, '获取用户数据统计成功')
  } catch (err) {
    console.error('获取用户数据统计失败:', err)
    return error('获取用户数据统计失败')
  }
})

/**
 * 清理用户数据（管理端）
 */
exports.cleanupUserDataAdmin = wrapAsync(async (params = {}) => {
  const {
    dataType = 'all',
    olderThan = 90,
    dryRun = true
  } = params

  // 验证清理天数
  if (olderThan < 1 || olderThan > 365) {
    return invalidParam('olderThan', '清理天数必须在1-365之间')
  }

  try {
    const result = await userDataDB.cleanupUserDataAdmin({
      dataType,
      olderThan,
      dryRun
    })
    
    if (!result.success) {
      return error(result.message || '清理用户数据失败')
    }

    return success(result.data, dryRun ? '数据清理预览成功' : '用户数据清理成功')
  } catch (err) {
    console.error('清理用户数据失败:', err)
    return error('清理用户数据失败')
  }
})

/**
 * 导出所有数据（管理端）
 */
exports.exportAllDataAdmin = wrapAsync(async (params = {}) => {
  const {
    format = 'json',
    dataType = 'all',
    startDate,
    endDate,
    limit = 1000
  } = params

  // 验证导出格式
  const validFormats = ['json', 'csv']
  const formatValidation = validateEnum(format, 'format', validFormats)
  if (!formatValidation.success) {
    return formatValidation
  }

  // 验证导出数量限制
  if (limit > 10000) {
    return invalidParam('limit', '单次导出不能超过10000条记录')
  }

  // 验证日期范围
  const dateValidation = validateDateRange(params)
  if (!dateValidation.success) {
    return dateValidation
  }

  try {
    const result = await userDataDB.exportAllDataAdmin({
      dataType,
      startDate: dateValidation.data.startDate,
      endDate: dateValidation.data.endDate,
      limit
    })

    if (!result.success) {
      return error(result.message || '导出数据失败')
    }

    // 生成文件名
    const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-')
    const filename = `userdata_export_${timestamp}.${format}`

    return exportData(result.data, filename, format, '数据导出成功')
  } catch (err) {
    console.error('导出数据失败:', err)
    return error('导出数据失败')
  }
})
