/**
 * 商店管理API（管理端）
 * 提供完整的商店和兑换码管理功能
 */

const { success, error, paginated, statsData, invalidParam, notFound } = require('../utils/response')
const { wrapAsync } = require('../utils/async-wrapper')
const { validateRequired, validatePagination, validateSorting, validateDateRange, validateId, validateEnum } = require('../utils/validators')

// 使用本地的数据库操作类
const { storeDB, redemptionCodeDB } = require('../db/store')

/**
 * 创建商品（管理端）
 */
exports.createStoreItem = wrapAsync(async (params = {}) => {
  // 验证必填参数
  const validation = validateRequired(params, ['name', 'price', 'category'])
  if (!validation.success) {
    return validation
  }

  const { 
    name, 
    description = '', 
    price, 
    originalPrice, 
    category, 
    type = 'virtual',
    stock = 0,
    images = [],
    tags = [],
    status = 'active',
    isVisible = true
  } = params

  // 验证价格
  if (price < 0) {
    return invalidParam('price', '价格不能为负数')
  }

  if (originalPrice && originalPrice < price) {
    return invalidParam('originalPrice', '原价不能低于现价')
  }

  try {
    const itemData = {
      name: name.trim(),
      description: description.trim(),
      price,
      originalPrice,
      category,
      type,
      stock,
      images,
      tags,
      status,
      isVisible
    }

    const result = await storeDB.createStoreItem(itemData)
    
    if (!result.success) {
      return error(result.message || '创建商品失败')
    }

    return success(result.data, '商品创建成功')
  } catch (err) {
    console.error('创建商品失败:', err)
    return error('创建商品失败')
  }
})

/**
 * 更新商品（管理端）
 */
exports.updateStoreItem = wrapAsync(async (params = {}) => {
  // 验证必填参数
  const validation = validateRequired(params, ['id'])
  if (!validation.success) {
    return validation
  }

  const { id, ...updateData } = params

  // 验证商品ID
  const idValidation = validateId(id, 'id')
  if (!idValidation.success) {
    return idValidation
  }

  try {
    // 检查商品是否存在
    const existingResult = await storeDB.findById(id)
    if (!existingResult.success || !existingResult.data) {
      return notFound('商品')
    }

    const result = await storeDB.updateStoreItem(id, updateData)
    
    if (!result.success) {
      return error(result.message || '更新商品失败')
    }

    return success(result.data, '商品更新成功')
  } catch (err) {
    console.error('更新商品失败:', err)
    return error('更新商品失败')
  }
})

/**
 * 删除商品（管理端）
 */
exports.deleteStoreItem = wrapAsync(async (params = {}) => {
  // 验证必填参数
  const validation = validateRequired(params, ['id'])
  if (!validation.success) {
    return validation
  }

  const { id } = params

  // 验证商品ID
  const idValidation = validateId(id, 'id')
  if (!idValidation.success) {
    return idValidation
  }

  try {
    // 检查商品是否存在
    const existingResult = await storeDB.findById(id)
    if (!existingResult.success || !existingResult.data) {
      return notFound('商品')
    }

    const result = await storeDB.deleteStoreItem(id)
    
    if (!result.success) {
      return error(result.message || '删除商品失败')
    }

    return success(result.data, '商品删除成功')
  } catch (err) {
    console.error('删除商品失败:', err)
    return error('删除商品失败')
  }
})

/**
 * 获取商品列表（管理端）
 */
exports.getStoreItemListAdmin = wrapAsync(async (params = {}) => {
  const {
    category = 'all',
    status = 'all',
    keyword = '',
    page = 1,
    pageSize = 20,
    sortBy = 'createTime',
    sortOrder = 'desc'
  } = params

  // 验证分页参数
  const paginationValidation = validatePagination(params, 100)
  if (!paginationValidation.success) {
    return paginationValidation
  }

  // 验证排序参数
  const allowedSortFields = ['createTime', 'updateTime', 'name', 'price', 'stock', 'soldCount']
  const sortingValidation = validateSorting(params, allowedSortFields)
  if (!sortingValidation.success) {
    return sortingValidation
  }

  try {
    const result = await storeDB.getStoreItemListAdmin({
      category,
      status,
      keyword,
      sortBy,
      sortOrder,
      limit: paginationValidation.data.limit,
      skip: paginationValidation.data.skip
    })

    if (!result.success) {
      return error(result.message || '获取商品列表失败')
    }

    return paginated(
      result.data.list,
      result.data.total,
      page,
      pageSize,
      '获取商品列表成功'
    )
  } catch (err) {
    console.error('获取商品列表失败:', err)
    return error('获取商品列表失败')
  }
})

/**
 * 获取商店统计（管理端）
 */
exports.getStoreStatsAdmin = wrapAsync(async (params = {}) => {
  const { period = '30d' } = params

  // 验证时间周期
  const validPeriods = ['7d', '30d', '90d', '1y', 'all']
  const periodValidation = validateEnum(period, 'period', validPeriods)
  if (!periodValidation.success) {
    return periodValidation
  }

  try {
    const result = await storeDB.getStoreStatsAdmin(period)
    
    if (!result.success) {
      return error(result.message || '获取商店统计失败')
    }

    return statsData(result.data, '获取商店统计成功')
  } catch (err) {
    console.error('获取商店统计失败:', err)
    return error('获取商店统计失败')
  }
})

/**
 * 创建兑换码（管理端）
 */
exports.createRedemptionCode = wrapAsync(async (params = {}) => {
  // 验证必填参数
  const validation = validateRequired(params, ['code', 'type', 'value'])
  if (!validation.success) {
    return validation
  }

  const { 
    code, 
    type, 
    value, 
    description = '',
    maxUses = 1,
    expiryTime = null
  } = params

  // 验证兑换码类型
  const validTypes = ['points', 'item', 'discount']
  const typeValidation = validateEnum(type, 'type', validTypes)
  if (!typeValidation.success) {
    return typeValidation
  }

  // 验证兑换码
  if (code.length < 4 || code.length > 20) {
    return invalidParam('code', '兑换码长度必须在4-20字符之间')
  }

  try {
    const codeData = {
      code: code.trim().toUpperCase(),
      type,
      value,
      description: description.trim(),
      maxUses,
      expiryTime
    }

    const result = await redemptionCodeDB.createRedemptionCode(codeData)
    
    if (!result.success) {
      return error(result.message || '创建兑换码失败')
    }

    return success(result.data, '兑换码创建成功')
  } catch (err) {
    console.error('创建兑换码失败:', err)
    return error('创建兑换码失败')
  }
})

/**
 * 获取兑换码列表（管理端）
 */
exports.getRedemptionCodeListAdmin = wrapAsync(async (params = {}) => {
  const {
    status = 'all',
    type = 'all',
    keyword = '',
    page = 1,
    pageSize = 20,
    sortBy = 'createTime',
    sortOrder = 'desc'
  } = params

  // 验证分页参数
  const paginationValidation = validatePagination(params, 100)
  if (!paginationValidation.success) {
    return paginationValidation
  }

  // 验证排序参数
  const allowedSortFields = ['createTime', 'updateTime', 'code', 'type', 'usedCount']
  const sortingValidation = validateSorting(params, allowedSortFields)
  if (!sortingValidation.success) {
    return sortingValidation
  }

  try {
    const result = await redemptionCodeDB.getRedemptionCodeListAdmin({
      status,
      type,
      keyword,
      sortBy,
      sortOrder,
      limit: paginationValidation.data.limit,
      skip: paginationValidation.data.skip
    })

    if (!result.success) {
      return error(result.message || '获取兑换码列表失败')
    }

    return paginated(
      result.data.list,
      result.data.total,
      page,
      pageSize,
      '获取兑换码列表成功'
    )
  } catch (err) {
    console.error('获取兑换码列表失败:', err)
    return error('获取兑换码列表失败')
  }
})
