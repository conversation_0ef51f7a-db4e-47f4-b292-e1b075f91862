/**
 * 摸鱼状态管理API（管理端）
 */

const cloud = require('wx-server-sdk')
const { wrapAsync } = require('../utils/async-wrapper')
const { error, success, invalidParam } = require('../utils/response')

// 初始化数据库
const db = cloud.database()

/**
 * 获取摸鱼统计
 */
exports.getFishingStatsAdmin = wrapAsync(async (params = {}) => {
  const { period = '7d' } = params

  try {
    // 计算时间范围
    const now = new Date()
    let startDate = null
    
    switch (period) {
      case '7d':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
        break
      case '30d':
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
        break
      case '90d':
        startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000)
        break
      default:
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
        break
    }

    // 获取摸鱼记录统计
    const fishingResult = await db.collection('fishing-status').where({
      startTime: db.command.gte(startDate.toISOString())
    }).get()

    const fishingRecords = fishingResult.data || []

    // 计算统计数据
    let totalSessions = fishingRecords.length
    let totalDuration = 0
    let activeFishers = new Set()
    let completedSessions = 0

    fishingRecords.forEach(record => {
      if (record.userId) {
        activeFishers.add(record.userId)
      }
      
      if (record.endTime) {
        completedSessions++
        const start = new Date(record.startTime)
        const end = new Date(record.endTime)
        totalDuration += (end.getTime() - start.getTime()) / 1000 / 60 // 转换为分钟
      }
    })

    const averageDuration = completedSessions > 0 ? Math.round(totalDuration / completedSessions) : 0

    const stats = {
      totalSessions,
      completedSessions,
      activeFishers: activeFishers.size,
      totalDuration: Math.round(totalDuration),
      averageDuration,
      period
    }

    console.log('[FishingAdmin] 获取摸鱼统计成功:', stats)

    return success(stats, '获取摸鱼统计成功')
  } catch (err) {
    console.error('获取摸鱼统计失败:', err)
    return error('获取摸鱼统计失败')
  }
})

/**
 * 获取摸鱼排行榜
 */
exports.getFishingLeaderboard = wrapAsync(async (params = {}) => {
  const { period = '7d', limit = 10 } = params

  try {
    // 计算时间范围
    const now = new Date()
    let startDate = null
    
    switch (period) {
      case '7d':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
        break
      case '30d':
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
        break
      case '90d':
        startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000)
        break
      default:
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
        break
    }

    // 获取摸鱼记录
    const fishingResult = await db.collection('fishing-status').where({
      startTime: db.command.gte(startDate.toISOString())
    }).get()

    const fishingRecords = fishingResult.data || []

    // 按用户统计摸鱼数据
    const userStats = {}
    
    fishingRecords.forEach(record => {
      if (!record.userId) return
      
      if (!userStats[record.userId]) {
        userStats[record.userId] = {
          userId: record.userId,
          sessions: 0,
          totalDuration: 0,
          completedSessions: 0
        }
      }
      
      userStats[record.userId].sessions++
      
      if (record.endTime) {
        userStats[record.userId].completedSessions++
        const start = new Date(record.startTime)
        const end = new Date(record.endTime)
        const duration = (end.getTime() - start.getTime()) / 1000 / 60 // 分钟
        userStats[record.userId].totalDuration += duration
      }
    })

    // 转换为数组并排序
    const leaderboard = Object.values(userStats)
      .map(user => ({
        ...user,
        averageDuration: user.completedSessions > 0 ? Math.round(user.totalDuration / user.completedSessions) : 0,
        totalDuration: Math.round(user.totalDuration)
      }))
      .sort((a, b) => b.totalDuration - a.totalDuration)
      .slice(0, limit)

    // 获取用户信息
    if (leaderboard.length > 0) {
      const userIds = leaderboard.map(item => item.userId)
      const usersResult = await db.collection('users').where({
        _id: db.command.in(userIds)
      }).get()

      const usersMap = {}
      usersResult.data.forEach(user => {
        usersMap[user._id] = user
      })

      // 补充用户信息
      leaderboard.forEach(item => {
        const user = usersMap[item.userId]
        if (user) {
          item.nickname = user.nickname || '未知用户'
          item.avatar = user.avatar || ''
        } else {
          item.nickname = '未知用户'
          item.avatar = ''
        }
      })
    }

    console.log(`[FishingAdmin] 获取摸鱼排行榜成功，共 ${leaderboard.length} 条记录`)

    return success(leaderboard, '获取摸鱼排行榜成功')
  } catch (err) {
    console.error('获取摸鱼排行榜失败:', err)
    return error('获取摸鱼排行榜失败')
  }
})

/**
 * 获取摸鱼记录列表
 */
exports.getFishingRecordsAdmin = wrapAsync(async (params = {}) => {
  const { page = 1, pageSize = 20, userId, status, startDate, endDate } = params

  try {
    const skip = (page - 1) * pageSize
    let query = db.collection('fishing-status')

    // 构建查询条件
    const whereConditions = {}
    
    if (userId) {
      whereConditions.userId = userId
    }
    
    if (status) {
      if (status === 'active') {
        whereConditions.endTime = db.command.exists(false)
      } else if (status === 'completed') {
        whereConditions.endTime = db.command.exists(true)
      }
    }
    
    if (startDate) {
      whereConditions.startTime = db.command.gte(startDate)
    }
    
    if (endDate) {
      if (whereConditions.startTime) {
        whereConditions.startTime = db.command.and([
          whereConditions.startTime,
          db.command.lte(endDate)
        ])
      } else {
        whereConditions.startTime = db.command.lte(endDate)
      }
    }

    if (Object.keys(whereConditions).length > 0) {
      query = query.where(whereConditions)
    }

    // 获取总数
    const countResult = await query.count()
    const total = countResult.total

    // 获取数据
    const result = await query
      .orderBy('startTime', 'desc')
      .skip(skip)
      .limit(pageSize)
      .get()

    const records = result.data.map(record => {
      const duration = record.endTime 
        ? Math.round((new Date(record.endTime).getTime() - new Date(record.startTime).getTime()) / 1000 / 60)
        : null

      return {
        _id: record._id,
        userId: record.userId,
        startTime: record.startTime,
        endTime: record.endTime,
        duration,
        status: record.endTime ? 'completed' : 'active',
        reason: record.reason || '',
        createTime: record.createTime || record.startTime
      }
    })

    // 获取用户信息
    if (records.length > 0) {
      const userIds = [...new Set(records.map(r => r.userId).filter(Boolean))]
      if (userIds.length > 0) {
        const usersResult = await db.collection('users').where({
          _id: db.command.in(userIds)
        }).get()

        const usersMap = {}
        usersResult.data.forEach(user => {
          usersMap[user._id] = user
        })

        // 补充用户信息
        records.forEach(record => {
          const user = usersMap[record.userId]
          if (user) {
            record.nickname = user.nickname || '未知用户'
            record.avatar = user.avatar || ''
          } else {
            record.nickname = '未知用户'
            record.avatar = ''
          }
        })
      }
    }

    console.log(`[FishingAdmin] 获取摸鱼记录成功，共 ${records.length} 条记录`)

    return success({
      list: records,
      total,
      hasMore: skip + records.length < total
    }, '获取摸鱼记录成功')
  } catch (err) {
    console.error('获取摸鱼记录失败:', err)
    return error('获取摸鱼记录失败')
  }
})

/**
 * 清理过期摸鱼状态
 */
exports.cleanupExpiredFishing = wrapAsync(async (params = {}) => {
  const { hours = 24 } = params

  try {
    const expireTime = new Date(Date.now() - hours * 60 * 60 * 1000)

    // 查找过期的活跃摸鱼状态
    const expiredResult = await db.collection('fishing-status').where({
      startTime: db.command.lt(expireTime.toISOString()),
      endTime: db.command.exists(false)
    }).get()

    const expiredRecords = expiredResult.data || []

    if (expiredRecords.length === 0) {
      return success({ cleaned: 0 }, '没有需要清理的过期摸鱼状态')
    }

    // 批量更新过期记录
    const batch = db.batch()
    const now = new Date().toISOString()

    expiredRecords.forEach(record => {
      const docRef = db.collection('fishing-status').doc(record._id)
      batch.update(docRef, {
        data: {
          endTime: now,
          autoEnded: true,
          updateTime: now
        }
      })
    })

    await batch.commit()

    console.log(`[FishingAdmin] 清理过期摸鱼状态成功，共清理 ${expiredRecords.length} 条记录`)

    return success({ cleaned: expiredRecords.length }, `成功清理 ${expiredRecords.length} 条过期摸鱼状态`)
  } catch (err) {
    console.error('清理过期摸鱼状态失败:', err)
    return error('清理过期摸鱼状态失败')
  }
})
