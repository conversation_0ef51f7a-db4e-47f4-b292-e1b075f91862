/**
 * 系统管理API（管理端）
 * 提供系统统计、状态监控等功能
 */

const { success, error, statsData } = require('../utils/response')
const { wrapAsync } = require('../utils/async-wrapper')

// 引入各个数据库操作类
const announcementsDB = require('../db/announcements')
const configDB = require('../db/config')
const feedbackDB = require('../db/feedback')
const pointsDB = require('../db/points')
const usersDB = require('../db/users')

/**
 * 获取系统统计信息
 */
exports.getSystemStats = wrapAsync(async (params = {}) => {
  try {
    const stats = {
      timestamp: new Date().toISOString(),
      environment: 'production',
      version: '1.0.0',
      uptime: process.uptime(),
      memory: process.memoryUsage()
    }

    return success(stats, '获取系统统计成功')
  } catch (err) {
    console.error('获取系统统计失败:', err)
    return error('获取系统统计失败')
  }
})

/**
 * 获取仪表板统计数据
 */
exports.getDashboardStats = wrapAsync(async (params = {}) => {
  const { period = '30d' } = params

  try {
    console.log('[SystemAdmin] 获取仪表板统计数据:', period)

    // 并行获取各模块统计数据
    const [
      announcementStats,
      configStats,
      feedbackStats,
      pointsStats,
      userStats
    ] = await Promise.allSettled([
      announcementsDB.getAnnouncementStats(period),
      configDB.getConfigStats(),
      feedbackDB.getFeedbackStatsAdmin(period),
      pointsDB.getPointsStatsAdmin(period),
      usersDB.getUserStatsAdmin(period)
    ])

    // 整理统计数据
    const dashboardData = {
      announcements: announcementStats.status === 'fulfilled' && announcementStats.value.success 
        ? announcementStats.value.data.total || 0 : 0,
      
      configs: configStats.status === 'fulfilled' && configStats.value.success 
        ? configStats.value.data.total || 0 : 0,
      
      feedback: feedbackStats.status === 'fulfilled' && feedbackStats.value.success 
        ? feedbackStats.value.data.total || 0 : 0,
      
      points: pointsStats.status === 'fulfilled' && pointsStats.value.success 
        ? pointsStats.value.data.totalPoints || 0 : 0,
      
      users: userStats.status === 'fulfilled' && userStats.value.success 
        ? userStats.value.data.total || 0 : 0,

      period,
      generatedAt: new Date().toISOString()
    }

    console.log('[SystemAdmin] 仪表板统计数据获取成功:', dashboardData)

    return statsData(dashboardData, '获取仪表板统计成功')
  } catch (err) {
    console.error('获取仪表板统计失败:', err)
    return error('获取仪表板统计失败')
  }
})

/**
 * 获取趋势数据
 */
exports.getTrendData = wrapAsync(async (params = {}) => {
  const { period = '7d' } = params

  try {
    console.log('[SystemAdmin] 获取趋势数据:', period)

    // 计算当前周期和上一周期的时间范围
    const periodDays = parseInt(period.replace('d', '')) || 7
    const now = new Date()
    const currentStart = new Date(now.getTime() - periodDays * 24 * 60 * 60 * 1000)
    const previousStart = new Date(currentStart.getTime() - periodDays * 24 * 60 * 60 * 1000)

    // 并行获取当前周期和上一周期的统计数据
    const [currentStats, previousStats] = await Promise.allSettled([
      Promise.all([
        usersDB.getUserStatsAdmin(period),
        feedbackDB.getFeedbackStatsAdmin(period),
        pointsDB.getPointsStatsAdmin(period),
        announcementsDB.getAnnouncementStats(period)
      ]),
      Promise.all([
        usersDB.getUserStatsByDateRange(previousStart, currentStart),
        feedbackDB.getFeedbackStatsByDateRange(previousStart, currentStart),
        pointsDB.getPointsStatsByDateRange(previousStart, currentStart),
        announcementsDB.getAnnouncementStatsByDateRange(previousStart, currentStart)
      ])
    ])

    // 处理当前周期数据
    const current = {
      users: 0,
      feedback: 0,
      points: 0,
      announcements: 0
    }

    if (currentStats.status === 'fulfilled') {
      const [userStats, feedbackStats, pointsStats, announcementStats] = currentStats.value
      current.users = userStats.success ? userStats.data.total || 0 : 0
      current.feedback = feedbackStats.success ? feedbackStats.data.total || 0 : 0
      current.points = pointsStats.success ? pointsStats.data.totalPoints || 0 : 0
      current.announcements = announcementStats.success ? announcementStats.data.total || 0 : 0
    }

    // 处理上一周期数据
    const previous = {
      users: 0,
      feedback: 0,
      points: 0,
      announcements: 0
    }

    if (previousStats.status === 'fulfilled') {
      const [userStats, feedbackStats, pointsStats, announcementStats] = previousStats.value
      previous.users = userStats.success ? userStats.data.total || 0 : 0
      previous.feedback = feedbackStats.success ? feedbackStats.data.total || 0 : 0
      previous.points = pointsStats.success ? pointsStats.data.totalPoints || 0 : 0
      previous.announcements = announcementStats.success ? announcementStats.data.total || 0 : 0
    }

    // 计算变化百分比
    function calculateChange(current, previous) {
      if (previous === 0) {
        return current > 0 ? '+100%' : '0%'
      }
      const change = ((current - previous) / previous * 100).toFixed(1)
      return change >= 0 ? `+${change}%` : `${change}%`
    }

    function getTrend(current, previous) {
      if (current > previous) return 'up'
      if (current < previous) return 'down'
      return 'stable'
    }

    const trendData = {
      users: {
        current: current.users,
        change: calculateChange(current.users, previous.users),
        trend: getTrend(current.users, previous.users)
      },
      feedback: {
        current: current.feedback,
        change: calculateChange(current.feedback, previous.feedback),
        trend: getTrend(current.feedback, previous.feedback)
      },
      points: {
        current: current.points,
        change: calculateChange(current.points, previous.points),
        trend: getTrend(current.points, previous.points)
      },
      announcements: {
        current: current.announcements,
        change: calculateChange(current.announcements, previous.announcements),
        trend: getTrend(current.announcements, previous.announcements)
      }
    }

    console.log('[SystemAdmin] 趋势数据获取成功:', trendData)

    return success(trendData, '获取趋势数据成功')
  } catch (err) {
    console.error('获取趋势数据失败:', err)
    return error('获取趋势数据失败')
  }
})

/**
 * 获取详细统计信息
 */
exports.getDetailedStats = wrapAsync(async (params = {}) => {
  const { period = '30d' } = params

  try {
    console.log('[SystemAdmin] 获取详细统计信息:', period)

    // 并行获取各模块详细统计
    const [
      announcementStats,
      configStats,
      feedbackStats,
      pointsStats,
      userStats
    ] = await Promise.allSettled([
      announcementsDB.getAnnouncementStats(period),
      configDB.getConfigStats(),
      feedbackDB.getFeedbackStatsAdmin(period),
      pointsDB.getPointsStatsAdmin(period),
      usersDB.getUserStatsAdmin(period)
    ])

    const detailedStats = {
      announcements: announcementStats.status === 'fulfilled' && announcementStats.value.success 
        ? announcementStats.value.data : null,
      
      configs: configStats.status === 'fulfilled' && configStats.value.success 
        ? configStats.value.data : null,
      
      feedback: feedbackStats.status === 'fulfilled' && feedbackStats.value.success 
        ? feedbackStats.value.data : null,
      
      points: pointsStats.status === 'fulfilled' && pointsStats.value.success 
        ? pointsStats.value.data : null,
      
      users: userStats.status === 'fulfilled' && userStats.value.success 
        ? userStats.value.data : null,

      period,
      generatedAt: new Date().toISOString()
    }

    console.log('[SystemAdmin] 详细统计信息获取成功')

    return statsData(detailedStats, '获取详细统计成功')
  } catch (err) {
    console.error('获取详细统计失败:', err)
    return error('获取详细统计失败')
  }
})

/**
 * 获取系统健康状态
 */
exports.getSystemHealth = wrapAsync(async (params = {}) => {
  try {
    const health = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      checks: {
        database: 'ok',
        memory: process.memoryUsage().heapUsed < 100 * 1024 * 1024 ? 'ok' : 'warning',
        uptime: process.uptime() > 0 ? 'ok' : 'error'
      },
      metrics: {
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        version: '1.0.0'
      }
    }

    // 检查整体健康状态
    const hasError = Object.values(health.checks).includes('error')
    const hasWarning = Object.values(health.checks).includes('warning')
    
    if (hasError) {
      health.status = 'unhealthy'
    } else if (hasWarning) {
      health.status = 'degraded'
    }

    return success(health, '获取系统健康状态成功')
  } catch (err) {
    console.error('获取系统健康状态失败:', err)
    return error('获取系统健康状态失败')
  }
})

/**
 * 清理系统缓存
 */
exports.clearSystemCache = wrapAsync(async (params = {}) => {
  try {
    // 这里可以实现缓存清理逻辑
    // 例如清理临时文件、重置缓存等
    
    const result = {
      clearedAt: new Date().toISOString(),
      cacheTypes: ['memory', 'temp'],
      status: 'success'
    }

    console.log('[SystemAdmin] 系统缓存清理完成')

    return success(result, '系统缓存清理成功')
  } catch (err) {
    console.error('清理系统缓存失败:', err)
    return error('清理系统缓存失败')
  }
})

/**
 * 获取系统日志
 */
exports.getSystemLogs = wrapAsync(async (params = {}) => {
  const { 
    level = 'all',
    startDate,
    endDate,
    limit = 100 
  } = params

  try {
    // 这里应该实现真实的日志查询
    // 暂时返回模拟数据
    const logs = []
    for (let i = 0; i < Math.min(limit, 10); i++) {
      logs.push({
        id: `log_${i}`,
        level: ['info', 'warn', 'error'][i % 3],
        message: `系统日志消息 ${i + 1}`,
        timestamp: new Date(Date.now() - i * 60000).toISOString(),
        source: 'system'
      })
    }

    const result = {
      logs,
      total: logs.length,
      level,
      period: { startDate, endDate },
      generatedAt: new Date().toISOString()
    }

    return success(result, '获取系统日志成功')
  } catch (err) {
    console.error('获取系统日志失败:', err)
    return error('获取系统日志失败')
  }
})
