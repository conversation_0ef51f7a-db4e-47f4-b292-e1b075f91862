/**
 * 反馈管理API（管理端）
 * 提供完整的反馈管理功能
 */

const { success, error, paginated, statsData, invalidParam, notFound, exportData } = require('../utils/response')
const { wrapAsync } = require('../utils/async-wrapper')
const { validateRequired, validatePagination, validateSorting, validateDateRange, validateId, validateEnum } = require('../utils/validators')

// 使用本地的数据库操作类
const feedbackDB = require('../db/feedback')

/**
 * 获取反馈列表（管理端）
 */
exports.getFeedbackListAdmin = wrapAsync(async (params = {}) => {
  const {
    status = 'all',
    type = 'all',
    page = 1,
    pageSize = 20,
    sortBy = 'createTime',
    sortOrder = 'desc',
    keyword = '',
    startDate,
    endDate
  } = params

  // 验证分页参数
  const paginationValidation = validatePagination(params, 100)
  if (!paginationValidation.success) {
    return paginationValidation
  }

  // 验证排序参数
  const allowedSortFields = ['createTime', 'updateTime', 'status', 'type', 'priority']
  const sortingValidation = validateSorting(params, allowedSortFields)
  if (!sortingValidation.success) {
    return sortingValidation
  }

  // 验证日期范围
  const dateValidation = validateDateRange(params)
  if (!dateValidation.success) {
    return dateValidation
  }

  try {
    const result = await feedbackDB.getFeedbackListAdmin({
      status,
      type,
      keyword,
      startDate: dateValidation.data.startDate,
      endDate: dateValidation.data.endDate,
      sortBy,
      sortOrder,
      limit: paginationValidation.data.limit,
      skip: paginationValidation.data.skip
    })

    if (!result.success) {
      return error(result.message || '获取反馈列表失败')
    }

    return paginated(
      result.data.list,
      result.data.total,
      page,
      pageSize,
      '获取反馈列表成功'
    )
  } catch (err) {
    console.error('获取反馈列表失败:', err)
    return error('获取反馈列表失败')
  }
})

/**
 * 回复反馈（管理端）
 */
exports.replyFeedback = wrapAsync(async (params = {}) => {
  // 验证必填参数
  const validation = validateRequired(params, ['id', 'reply'])
  if (!validation.success) {
    return validation
  }

  const { id, reply, adminId = 'system' } = params

  // 验证反馈ID
  const idValidation = validateId(id, 'id')
  if (!idValidation.success) {
    return idValidation
  }

  // 验证回复内容
  if (reply.length > 1000) {
    return invalidParam('reply', '回复内容不能超过1000字符')
  }

  try {
    // 检查反馈是否存在
    const existingResult = await feedbackDB.getFeedbackById(id)
    if (!existingResult.success || !existingResult.data) {
      return notFound('反馈')
    }

    const replyData = {
      reply: reply.trim(),
      adminId,
      replyTime: new Date(),
      status: 'replied'
    }

    const result = await feedbackDB.replyFeedback(id, replyData)
    
    if (!result.success) {
      return error(result.message || '回复反馈失败')
    }

    return success(result.data, '反馈回复成功')
  } catch (err) {
    console.error('回复反馈失败:', err)
    return error('回复反馈失败')
  }
})

/**
 * 更新反馈状态（管理端）
 */
exports.updateFeedbackStatus = wrapAsync(async (params = {}) => {
  // 验证必填参数
  const validation = validateRequired(params, ['id', 'status'])
  if (!validation.success) {
    return validation
  }

  const { id, status, priority, tags, adminNotes } = params

  // 验证反馈ID
  const idValidation = validateId(id, 'id')
  if (!idValidation.success) {
    return idValidation
  }

  // 验证状态
  const validStatuses = ['pending', 'processing', 'replied', 'resolved', 'closed']
  const statusValidation = validateEnum(status, 'status', validStatuses)
  if (!statusValidation.success) {
    return statusValidation
  }

  try {
    // 检查反馈是否存在
    const existingResult = await feedbackDB.getFeedbackById(id)
    if (!existingResult.success || !existingResult.data) {
      return notFound('反馈')
    }

    // 构建更新数据
    const updateData = { status }
    
    if (priority !== undefined) {
      if (priority < 1 || priority > 5) {
        return invalidParam('priority', '优先级必须在1-5之间')
      }
      updateData.priority = priority
    }
    
    if (tags !== undefined) {
      if (!Array.isArray(tags)) {
        return invalidParam('tags', '标签必须是数组')
      }
      updateData.tags = tags
    }
    
    if (adminNotes !== undefined) {
      if (adminNotes.length > 500) {
        return invalidParam('adminNotes', '管理员备注不能超过500字符')
      }
      updateData.adminNotes = adminNotes
    }

    const result = await feedbackDB.updateFeedbackStatus(id, updateData)
    
    if (!result.success) {
      return error(result.message || '更新反馈状态失败')
    }

    return success(result.data, '反馈状态更新成功')
  } catch (err) {
    console.error('更新反馈状态失败:', err)
    return error('更新反馈状态失败')
  }
})

/**
 * 获取反馈统计（管理端）
 */
exports.getFeedbackStatsAdmin = wrapAsync(async (params = {}) => {
  const { period = '30d' } = params

  // 验证时间周期
  const validPeriods = ['7d', '30d', '90d', '1y', 'all']
  const periodValidation = validateEnum(period, 'period', validPeriods)
  if (!periodValidation.success) {
    return periodValidation
  }

  try {
    const result = await feedbackDB.getFeedbackStatsAdmin(period)
    
    if (!result.success) {
      return error(result.message || '获取反馈统计失败')
    }

    return statsData(result.data, '获取反馈统计成功')
  } catch (err) {
    console.error('获取反馈统计失败:', err)
    return error('获取反馈统计失败')
  }
})

/**
 * 导出反馈数据（管理端）
 */
exports.exportFeedbackData = wrapAsync(async (params = {}) => {
  const {
    format = 'json',
    status = 'all',
    type = 'all',
    startDate,
    endDate,
    limit = 1000
  } = params

  // 验证导出格式
  const validFormats = ['json', 'csv']
  const formatValidation = validateEnum(format, 'format', validFormats)
  if (!formatValidation.success) {
    return formatValidation
  }

  // 验证导出数量限制
  if (limit > 10000) {
    return invalidParam('limit', '单次导出不能超过10000条记录')
  }

  // 验证日期范围
  const dateValidation = validateDateRange(params)
  if (!dateValidation.success) {
    return dateValidation
  }

  try {
    const result = await feedbackDB.exportFeedbackDataAdmin({
      status,
      type,
      startDate: dateValidation.data.startDate,
      endDate: dateValidation.data.endDate,
      limit
    })

    if (!result.success) {
      return error(result.message || '导出反馈数据失败')
    }

    // 生成文件名
    const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-')
    const filename = `feedback_export_${timestamp}.${format}`

    return exportData(result.data, filename, format, '反馈数据导出成功')
  } catch (err) {
    console.error('导出反馈数据失败:', err)
    return error('导出反馈数据失败')
  }
})

/**
 * 批量操作反馈（管理端）
 */
exports.batchOperateFeedback = wrapAsync(async (params = {}) => {
  // 验证必填参数
  const validation = validateRequired(params, ['ids', 'action'])
  if (!validation.success) {
    return validation
  }

  const { ids, action, data = {} } = params

  // 验证ID列表
  if (!Array.isArray(ids) || ids.length === 0) {
    return invalidParam('ids', 'ID列表不能为空')
  }

  if (ids.length > 100) {
    return invalidParam('ids', '单次批量操作不能超过100条记录')
  }

  // 验证操作类型
  const validActions = ['updateStatus', 'delete', 'archive']
  const actionValidation = validateEnum(action, 'action', validActions)
  if (!actionValidation.success) {
    return actionValidation
  }

  try {
    const result = await feedbackDB.batchOperateFeedback(ids, action, data)
    
    if (!result.success) {
      return error(result.message || '批量操作失败')
    }

    return success(result.data, '批量操作成功')
  } catch (err) {
    console.error('批量操作反馈失败:', err)
    return error('批量操作失败')
  }
})
