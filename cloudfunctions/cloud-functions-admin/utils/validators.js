/**
 * 管理端参数验证工具
 * 复用用户端的验证逻辑，并添加管理端特有的验证
 */

const { invalidParam } = require('./response')

/**
 * 验证必填参数
 * @param {Object} params - 参数对象
 * @param {Array} requiredFields - 必填字段数组
 * @returns {Object} 验证结果
 */
function validateRequired(params, requiredFields) {
  if (!params || typeof params !== 'object') {
    return invalidParam('params', '参数必须是对象')
  }

  for (const field of requiredFields) {
    if (!(field in params) || params[field] === null || params[field] === undefined) {
      return invalidParam(field, '此字段为必填项')
    }

    // 检查字符串是否为空
    if (typeof params[field] === 'string' && params[field].trim() === '') {
      return invalidParam(field, '此字段不能为空字符串')
    }
  }

  return { success: true }
}

/**
 * 验证分页参数
 * @param {Object} params - 参数对象
 * @param {number} maxPageSize - 最大页面大小
 * @returns {Object} 验证结果和标准化的分页参数
 */
function validatePagination(params, maxPageSize = 100) {
  const { page = 1, pageSize = 20 } = params

  // 验证页码
  if (!Number.isInteger(page) || page < 1) {
    return invalidParam('page', '页码必须是大于0的整数')
  }

  // 验证页面大小
  if (!Number.isInteger(pageSize) || pageSize < 1) {
    return invalidParam('pageSize', '页面大小必须是大于0的整数')
  }

  if (pageSize > maxPageSize) {
    return invalidParam('pageSize', `页面大小不能超过${maxPageSize}`)
  }

  return {
    success: true,
    data: {
      page: parseInt(page),
      pageSize: parseInt(pageSize),
      skip: (parseInt(page) - 1) * parseInt(pageSize),
      limit: parseInt(pageSize)
    }
  }
}

/**
 * 验证排序参数
 * @param {Object} params - 参数对象
 * @param {Array} allowedFields - 允许排序的字段
 * @returns {Object} 验证结果和标准化的排序参数
 */
function validateSorting(params, allowedFields = []) {
  const { sortBy = 'createTime', sortOrder = 'desc' } = params

  // 验证排序字段
  if (allowedFields.length > 0 && !allowedFields.includes(sortBy)) {
    return invalidParam('sortBy', `排序字段必须是: ${allowedFields.join(', ')}`)
  }

  // 验证排序方向
  if (!['asc', 'desc'].includes(sortOrder.toLowerCase())) {
    return invalidParam('sortOrder', '排序方向必须是 asc 或 desc')
  }

  return {
    success: true,
    data: {
      sortBy,
      sortOrder: sortOrder.toLowerCase()
    }
  }
}

/**
 * 验证日期范围
 * @param {Object} params - 参数对象
 * @returns {Object} 验证结果和标准化的日期参数
 */
function validateDateRange(params) {
  const { startDate, endDate } = params

  let start = null
  let end = null

  // 验证开始日期
  if (startDate) {
    start = new Date(startDate)
    if (isNaN(start.getTime())) {
      return invalidParam('startDate', '开始日期格式无效')
    }
  }

  // 验证结束日期
  if (endDate) {
    end = new Date(endDate)
    if (isNaN(end.getTime())) {
      return invalidParam('endDate', '结束日期格式无效')
    }
  }

  // 验证日期范围
  if (start && end && start > end) {
    return invalidParam('dateRange', '开始日期不能晚于结束日期')
  }

  return {
    success: true,
    data: {
      startDate: start,
      endDate: end
    }
  }
}

/**
 * 验证ID参数
 * @param {string} id - ID值
 * @param {string} fieldName - 字段名称
 * @returns {Object} 验证结果
 */
function validateId(id, fieldName = 'id') {
  if (!id) {
    return invalidParam(fieldName, 'ID不能为空')
  }

  if (typeof id !== 'string') {
    return invalidParam(fieldName, 'ID必须是字符串')
  }

  // 简单的ID格式验证（可根据实际需要调整）
  if (id.length < 1 || id.length > 100) {
    return invalidParam(fieldName, 'ID长度必须在1-100字符之间')
  }

  return { success: true }
}

/**
 * 验证邮箱格式
 * @param {string} email - 邮箱地址
 * @param {boolean} required - 是否必填
 * @returns {Object} 验证结果
 */
function validateEmail(email, required = false) {
  if (!email) {
    if (required) {
      return invalidParam('email', '邮箱地址为必填项')
    }
    return { success: true }
  }

  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  if (!emailRegex.test(email)) {
    return invalidParam('email', '邮箱地址格式无效')
  }

  return { success: true }
}

/**
 * 验证字符串长度
 * @param {string} value - 字符串值
 * @param {string} fieldName - 字段名称
 * @param {number} minLength - 最小长度
 * @param {number} maxLength - 最大长度
 * @returns {Object} 验证结果
 */
function validateStringLength(value, fieldName, minLength = 0, maxLength = Infinity) {
  if (typeof value !== 'string') {
    return invalidParam(fieldName, '必须是字符串')
  }

  if (value.length < minLength) {
    return invalidParam(fieldName, `长度不能少于${minLength}个字符`)
  }

  if (value.length > maxLength) {
    return invalidParam(fieldName, `长度不能超过${maxLength}个字符`)
  }

  return { success: true }
}

/**
 * 验证数字范围
 * @param {number} value - 数字值
 * @param {string} fieldName - 字段名称
 * @param {number} min - 最小值
 * @param {number} max - 最大值
 * @returns {Object} 验证结果
 */
function validateNumberRange(value, fieldName, min = -Infinity, max = Infinity) {
  if (typeof value !== 'number' || isNaN(value)) {
    return invalidParam(fieldName, '必须是有效数字')
  }

  if (value < min) {
    return invalidParam(fieldName, `不能小于${min}`)
  }

  if (value > max) {
    return invalidParam(fieldName, `不能大于${max}`)
  }

  return { success: true }
}

/**
 * 验证枚举值
 * @param {*} value - 值
 * @param {string} fieldName - 字段名称
 * @param {Array} allowedValues - 允许的值列表
 * @returns {Object} 验证结果
 */
function validateEnum(value, fieldName, allowedValues) {
  if (!allowedValues.includes(value)) {
    return invalidParam(fieldName, `必须是以下值之一: ${allowedValues.join(', ')}`)
  }

  return { success: true }
}

/**
 * 批量验证
 * @param {Array} validations - 验证函数数组
 * @returns {Object} 验证结果
 */
function validateBatch(validations) {
  for (const validation of validations) {
    if (typeof validation === 'function') {
      const result = validation()
      if (!result.success) {
        return result
      }
    } else if (validation && !validation.success) {
      return validation
    }
  }

  return { success: true }
}

module.exports = {
  validateRequired,
  validatePagination,
  validateSorting,
  validateDateRange,
  validateId,
  validateEmail,
  validateStringLength,
  validateNumberRange,
  validateEnum,
  validateBatch
}
