/**
 * 日期处理工具函数
 */

/**
 * 格式化日期
 * @param {Date|string} date - 日期对象或字符串
 * @param {string} format - 格式化模式
 * @returns {string} 格式化后的日期字符串
 */
function formatDate(date, format = 'YYYY-MM-DD HH:mm:ss') {
  if (!date) return ''
  
  const d = new Date(date)
  if (isNaN(d.getTime())) return ''
  
  const year = d.getFullYear()
  const month = String(d.getMonth() + 1).padStart(2, '0')
  const day = String(d.getDate()).padStart(2, '0')
  const hours = String(d.getHours()).padStart(2, '0')
  const minutes = String(d.getMinutes()).padStart(2, '0')
  const seconds = String(d.getSeconds()).padStart(2, '0')
  
  return format
    .replace('YYYY', year)
    .replace('MM', month)
    .replace('DD', day)
    .replace('HH', hours)
    .replace('mm', minutes)
    .replace('ss', seconds)
}

/**
 * 标准化时间戳
 * @param {Date|string|number} time - 时间
 * @returns {string} ISO字符串
 */
function normalizeTime(time) {
  if (!time) return new Date().toISOString()
  
  const date = new Date(time)
  if (isNaN(date.getTime())) {
    return new Date().toISOString()
  }
  
  return date.toISOString()
}

/**
 * 获取今天的开始时间
 * @returns {Date} 今天00:00:00
 */
function getTodayStart() {
  const today = new Date()
  today.setHours(0, 0, 0, 0)
  return today
}

/**
 * 获取今天的结束时间
 * @returns {Date} 今天23:59:59
 */
function getTodayEnd() {
  const today = new Date()
  today.setHours(23, 59, 59, 999)
  return today
}

/**
 * 获取指定天数前的日期
 * @param {number} days - 天数
 * @returns {Date} 指定天数前的日期
 */
function getDaysAgo(days) {
  const date = new Date()
  date.setDate(date.getDate() - days)
  return date
}

/**
 * 获取指定天数后的日期
 * @param {number} days - 天数
 * @returns {Date} 指定天数后的日期
 */
function getDaysLater(days) {
  const date = new Date()
  date.setDate(date.getDate() + days)
  return date
}

/**
 * 计算两个日期之间的天数差
 * @param {Date|string} date1 - 日期1
 * @param {Date|string} date2 - 日期2
 * @returns {number} 天数差
 */
function getDaysDiff(date1, date2) {
  const d1 = new Date(date1)
  const d2 = new Date(date2)
  
  if (isNaN(d1.getTime()) || isNaN(d2.getTime())) {
    return 0
  }
  
  const diffTime = Math.abs(d2.getTime() - d1.getTime())
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24))
}

/**
 * 检查日期是否是今天
 * @param {Date|string} date - 日期
 * @returns {boolean} 是否是今天
 */
function isToday(date) {
  if (!date) return false
  
  const d = new Date(date)
  const today = new Date()
  
  return d.getFullYear() === today.getFullYear() &&
         d.getMonth() === today.getMonth() &&
         d.getDate() === today.getDate()
}

/**
 * 检查日期是否是昨天
 * @param {Date|string} date - 日期
 * @returns {boolean} 是否是昨天
 */
function isYesterday(date) {
  if (!date) return false
  
  const d = new Date(date)
  const yesterday = new Date()
  yesterday.setDate(yesterday.getDate() - 1)
  
  return d.getFullYear() === yesterday.getFullYear() &&
         d.getMonth() === yesterday.getMonth() &&
         d.getDate() === yesterday.getDate()
}

/**
 * 获取月份的第一天
 * @param {Date|string} date - 日期
 * @returns {Date} 月份第一天
 */
function getMonthStart(date = new Date()) {
  const d = new Date(date)
  return new Date(d.getFullYear(), d.getMonth(), 1)
}

/**
 * 获取月份的最后一天
 * @param {Date|string} date - 日期
 * @returns {Date} 月份最后一天
 */
function getMonthEnd(date = new Date()) {
  const d = new Date(date)
  return new Date(d.getFullYear(), d.getMonth() + 1, 0, 23, 59, 59, 999)
}

/**
 * 获取周的第一天（周一）
 * @param {Date|string} date - 日期
 * @returns {Date} 周的第一天
 */
function getWeekStart(date = new Date()) {
  const d = new Date(date)
  const day = d.getDay()
  const diff = d.getDate() - day + (day === 0 ? -6 : 1) // 调整为周一开始
  return new Date(d.setDate(diff))
}

/**
 * 获取周的最后一天（周日）
 * @param {Date|string} date - 日期
 * @returns {Date} 周的最后一天
 */
function getWeekEnd(date = new Date()) {
  const weekStart = getWeekStart(date)
  const weekEnd = new Date(weekStart)
  weekEnd.setDate(weekStart.getDate() + 6)
  weekEnd.setHours(23, 59, 59, 999)
  return weekEnd
}

/**
 * 格式化相对时间
 * @param {Date|string} date - 日期
 * @returns {string} 相对时间描述
 */
function formatRelativeTime(date) {
  if (!date) return ''
  
  const d = new Date(date)
  const now = new Date()
  const diff = now.getTime() - d.getTime()
  
  const seconds = Math.floor(diff / 1000)
  const minutes = Math.floor(seconds / 60)
  const hours = Math.floor(minutes / 60)
  const days = Math.floor(hours / 24)
  
  if (days > 0) {
    return `${days}天前`
  } else if (hours > 0) {
    return `${hours}小时前`
  } else if (minutes > 0) {
    return `${minutes}分钟前`
  } else {
    return '刚刚'
  }
}

/**
 * 解析时间段字符串
 * @param {string} period - 时间段字符串 (如: '7d', '30d', '1h')
 * @returns {Object} 解析结果
 */
function parsePeriod(period) {
  const match = period.match(/^(\d+)([hdwmy])$/)
  if (!match) {
    return { value: 1, unit: 'd', milliseconds: 24 * 60 * 60 * 1000 }
  }
  
  const value = parseInt(match[1])
  const unit = match[2]
  
  let milliseconds
  switch (unit) {
    case 'h': // 小时
      milliseconds = value * 60 * 60 * 1000
      break
    case 'd': // 天
      milliseconds = value * 24 * 60 * 60 * 1000
      break
    case 'w': // 周
      milliseconds = value * 7 * 24 * 60 * 60 * 1000
      break
    case 'm': // 月（按30天计算）
      milliseconds = value * 30 * 24 * 60 * 60 * 1000
      break
    case 'y': // 年（按365天计算）
      milliseconds = value * 365 * 24 * 60 * 60 * 1000
      break
    default:
      milliseconds = value * 24 * 60 * 60 * 1000
  }
  
  return { value, unit, milliseconds }
}

/**
 * 获取时间段的开始日期
 * @param {string} period - 时间段字符串
 * @returns {Date} 开始日期
 */
function getPeriodStart(period) {
  const { milliseconds } = parsePeriod(period)
  return new Date(Date.now() - milliseconds)
}

module.exports = {
  formatDate,
  normalizeTime,
  getTodayStart,
  getTodayEnd,
  getDaysAgo,
  getDaysLater,
  getDaysDiff,
  isToday,
  isYesterday,
  getMonthStart,
  getMonthEnd,
  getWeekStart,
  getWeekEnd,
  formatRelativeTime,
  parsePeriod,
  getPeriodStart
}
