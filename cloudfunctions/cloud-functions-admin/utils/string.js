/**
 * 字符串处理工具函数
 */

/**
 * 将 \n 替换为真正的换行符
 * @param {string} str - 输入字符串
 * @returns {string} 处理后的字符串
 */
function replaceNWithNewline(str) {
  if (typeof str !== 'string') return ''
  return str.replace(/\\n/g, '\n')
}

/**
 * 将换行符替换为 \n
 * @param {string} str - 输入字符串
 * @returns {string} 处理后的字符串
 */
function replaceNewlineWithN(str) {
  if (typeof str !== 'string') return ''
  return str.replace(/\n/g, '\\n')
}

/**
 * 截断字符串
 * @param {string} str - 输入字符串
 * @param {number} length - 最大长度
 * @param {string} suffix - 后缀
 * @returns {string} 截断后的字符串
 */
function truncate(str, length = 100, suffix = '...') {
  if (typeof str !== 'string') return ''
  if (str.length <= length) return str
  return str.substring(0, length) + suffix
}

/**
 * 清理HTML标签
 * @param {string} str - 输入字符串
 * @returns {string} 清理后的字符串
 */
function stripHtml(str) {
  if (typeof str !== 'string') return ''
  return str.replace(/<[^>]*>/g, '')
}

/**
 * 转义HTML特殊字符
 * @param {string} str - 输入字符串
 * @returns {string} 转义后的字符串
 */
function escapeHtml(str) {
  if (typeof str !== 'string') return ''
  const map = {
    '&': '&amp;',
    '<': '&lt;',
    '>': '&gt;',
    '"': '&quot;',
    "'": '&#39;'
  }
  return str.replace(/[&<>"']/g, m => map[m])
}

/**
 * 反转义HTML特殊字符
 * @param {string} str - 输入字符串
 * @returns {string} 反转义后的字符串
 */
function unescapeHtml(str) {
  if (typeof str !== 'string') return ''
  const map = {
    '&amp;': '&',
    '&lt;': '<',
    '&gt;': '>',
    '&quot;': '"',
    '&#39;': "'"
  }
  return str.replace(/&(amp|lt|gt|quot|#39);/g, (match, entity) => map[match])
}

/**
 * 首字母大写
 * @param {string} str - 输入字符串
 * @returns {string} 首字母大写的字符串
 */
function capitalize(str) {
  if (typeof str !== 'string' || str.length === 0) return ''
  return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase()
}

/**
 * 驼峰命名转换
 * @param {string} str - 输入字符串
 * @returns {string} 驼峰命名的字符串
 */
function toCamelCase(str) {
  if (typeof str !== 'string') return ''
  return str.replace(/[-_\s]+(.)?/g, (_, char) => char ? char.toUpperCase() : '')
}

/**
 * 短横线命名转换
 * @param {string} str - 输入字符串
 * @returns {string} 短横线命名的字符串
 */
function toKebabCase(str) {
  if (typeof str !== 'string') return ''
  return str
    .replace(/([a-z])([A-Z])/g, '$1-$2')
    .replace(/[\s_]+/g, '-')
    .toLowerCase()
}

/**
 * 下划线命名转换
 * @param {string} str - 输入字符串
 * @returns {string} 下划线命名的字符串
 */
function toSnakeCase(str) {
  if (typeof str !== 'string') return ''
  return str
    .replace(/([a-z])([A-Z])/g, '$1_$2')
    .replace(/[\s-]+/g, '_')
    .toLowerCase()
}

/**
 * 生成随机字符串
 * @param {number} length - 长度
 * @param {string} chars - 字符集
 * @returns {string} 随机字符串
 */
function randomString(length = 8, chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789') {
  let result = ''
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  return result
}

/**
 * 生成UUID
 * @returns {string} UUID字符串
 */
function generateUUID() {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0
    const v = c === 'x' ? r : (r & 0x3 | 0x8)
    return v.toString(16)
  })
}

/**
 * 检查字符串是否为空
 * @param {string} str - 输入字符串
 * @returns {boolean} 是否为空
 */
function isEmpty(str) {
  return !str || typeof str !== 'string' || str.trim().length === 0
}

/**
 * 检查字符串是否为有效邮箱
 * @param {string} email - 邮箱字符串
 * @returns {boolean} 是否为有效邮箱
 */
function isValidEmail(email) {
  if (typeof email !== 'string') return false
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

/**
 * 检查字符串是否为有效手机号
 * @param {string} phone - 手机号字符串
 * @returns {boolean} 是否为有效手机号
 */
function isValidPhone(phone) {
  if (typeof phone !== 'string') return false
  const phoneRegex = /^1[3-9]\d{9}$/
  return phoneRegex.test(phone)
}

/**
 * 检查字符串是否为有效URL
 * @param {string} url - URL字符串
 * @returns {boolean} 是否为有效URL
 */
function isValidUrl(url) {
  if (typeof url !== 'string') return false
  try {
    new URL(url)
    return true
  } catch {
    return false
  }
}

/**
 * 格式化文件大小
 * @param {number} bytes - 字节数
 * @param {number} decimals - 小数位数
 * @returns {string} 格式化后的文件大小
 */
function formatFileSize(bytes, decimals = 2) {
  if (bytes === 0) return '0 Bytes'
  
  const k = 1024
  const dm = decimals < 0 ? 0 : decimals
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB']
  
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i]
}

/**
 * 格式化数字
 * @param {number} num - 数字
 * @param {number} decimals - 小数位数
 * @returns {string} 格式化后的数字
 */
function formatNumber(num, decimals = 0) {
  if (typeof num !== 'number') return '0'
  return num.toLocaleString('zh-CN', {
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals
  })
}

/**
 * 高亮关键词
 * @param {string} text - 文本
 * @param {string} keyword - 关键词
 * @param {string} className - CSS类名
 * @returns {string} 高亮后的HTML
 */
function highlightKeyword(text, keyword, className = 'highlight') {
  if (!text || !keyword) return text
  
  const regex = new RegExp(`(${keyword})`, 'gi')
  return text.replace(regex, `<span class="${className}">$1</span>`)
}

/**
 * 移除字符串中的空白字符
 * @param {string} str - 输入字符串
 * @returns {string} 处理后的字符串
 */
function removeWhitespace(str) {
  if (typeof str !== 'string') return ''
  return str.replace(/\s+/g, '')
}

/**
 * 标准化空白字符
 * @param {string} str - 输入字符串
 * @returns {string} 处理后的字符串
 */
function normalizeWhitespace(str) {
  if (typeof str !== 'string') return ''
  return str.replace(/\s+/g, ' ').trim()
}

/**
 * 计算字符串的字节长度（中文按2字节计算）
 * @param {string} str - 输入字符串
 * @returns {number} 字节长度
 */
function getByteLength(str) {
  if (typeof str !== 'string') return 0
  let length = 0
  for (let i = 0; i < str.length; i++) {
    const charCode = str.charCodeAt(i)
    if (charCode >= 0 && charCode <= 128) {
      length += 1
    } else {
      length += 2
    }
  }
  return length
}

module.exports = {
  replaceNWithNewline,
  replaceNewlineWithN,
  truncate,
  stripHtml,
  escapeHtml,
  unescapeHtml,
  capitalize,
  toCamelCase,
  toKebabCase,
  toSnakeCase,
  randomString,
  generateUUID,
  isEmpty,
  isValidEmail,
  isValidPhone,
  isValidUrl,
  formatFileSize,
  formatNumber,
  highlightKeyword,
  removeWhitespace,
  normalizeWhitespace,
  getByteLength
}
