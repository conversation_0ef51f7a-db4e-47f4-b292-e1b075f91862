/**
 * 文件处理工具函数
 */

const cloud = require('wx-server-sdk')

/**
 * 将云存储文件ID转换为临时访问链接
 * @param {string} fileId - 云存储文件ID (cloud://...)
 * @returns {Promise<string>} 临时访问链接
 */
async function getFileUrl(fileId) {
  if (!fileId || !fileId.startsWith('cloud://')) {
    return fileId // 如果不是云存储文件ID，直接返回
  }

  try {
    const result = await cloud.getTempFileURL({
      fileList: [fileId]
    })

    if (result.fileList && result.fileList.length > 0) {
      const file = result.fileList[0]
      if (file.status === 0) {
        return file.tempFileURL
      } else {
        console.error('获取文件URL失败:', file.errMsg)
        return fileId // 失败时返回原始ID
      }
    }

    return fileId
  } catch (error) {
    console.error('转换文件URL失败:', error)
    return fileId // 失败时返回原始ID
  }
}

/**
 * 批量转换文件ID为临时访问链接
 * @param {Array<string>} fileIds - 文件ID数组
 * @returns {Promise<Array<string>>} 临时访问链接数组
 */
async function getFileUrls(fileIds) {
  if (!Array.isArray(fileIds) || fileIds.length === 0) {
    return []
  }

  // 过滤出云存储文件ID
  const cloudFileIds = fileIds.filter(id => id && id.startsWith('cloud://'))
  
  if (cloudFileIds.length === 0) {
    return fileIds // 如果没有云存储文件，直接返回原数组
  }

  try {
    const result = await cloud.getTempFileURL({
      fileList: cloudFileIds
    })

    if (result.fileList && result.fileList.length > 0) {
      // 创建文件ID到URL的映射
      const urlMap = {}
      result.fileList.forEach(file => {
        if (file.status === 0) {
          urlMap[file.fileID] = file.tempFileURL
        } else {
          urlMap[file.fileID] = file.fileID // 失败时保持原ID
        }
      })

      // 替换原数组中的文件ID
      return fileIds.map(id => urlMap[id] || id)
    }

    return fileIds
  } catch (error) {
    console.error('批量转换文件URL失败:', error)
    return fileIds // 失败时返回原数组
  }
}

/**
 * 处理用户数据中的头像URL
 * @param {Object} user - 用户数据
 * @returns {Promise<Object>} 处理后的用户数据
 */
async function processUserAvatar(user) {
  if (!user || !user.avatar) {
    return user
  }

  const avatarUrl = await getFileUrl(user.avatar)
  
  return {
    ...user,
    avatar: avatarUrl
  }
}

/**
 * 批量处理用户数据中的头像URL
 * @param {Array<Object>} users - 用户数据数组
 * @returns {Promise<Array<Object>>} 处理后的用户数据数组
 */
async function processUsersAvatars(users) {
  if (!Array.isArray(users) || users.length === 0) {
    return users
  }

  // 收集所有头像文件ID
  const avatarIds = users
    .map(user => user.avatar)
    .filter(avatar => avatar && avatar.startsWith('cloud://'))

  if (avatarIds.length === 0) {
    return users // 如果没有云存储头像，直接返回
  }

  // 批量获取头像URL
  const avatarUrls = await getFileUrls(avatarIds)
  
  // 创建文件ID到URL的映射
  const urlMap = {}
  avatarIds.forEach((id, index) => {
    urlMap[id] = avatarUrls[index]
  })

  // 替换用户数据中的头像URL
  return users.map(user => {
    if (user.avatar && urlMap[user.avatar]) {
      return {
        ...user,
        avatar: urlMap[user.avatar]
      }
    }
    return user
  })
}

/**
 * 处理图片数据
 * @param {Object} data - 包含图片字段的数据
 * @param {Array<string>} imageFields - 图片字段名数组
 * @returns {Promise<Object>} 处理后的数据
 */
async function processImageFields(data, imageFields = []) {
  if (!data || !Array.isArray(imageFields) || imageFields.length === 0) {
    return data
  }

  const processedData = { ...data }
  
  for (const field of imageFields) {
    if (processedData[field]) {
      if (Array.isArray(processedData[field])) {
        // 处理图片数组
        processedData[field] = await getFileUrls(processedData[field])
      } else {
        // 处理单个图片
        processedData[field] = await getFileUrl(processedData[field])
      }
    }
  }

  return processedData
}

/**
 * 批量处理数据中的图片字段
 * @param {Array<Object>} dataList - 数据数组
 * @param {Array<string>} imageFields - 图片字段名数组
 * @returns {Promise<Array<Object>>} 处理后的数据数组
 */
async function processImageFieldsBatch(dataList, imageFields = []) {
  if (!Array.isArray(dataList) || dataList.length === 0) {
    return dataList
  }

  if (!Array.isArray(imageFields) || imageFields.length === 0) {
    return dataList
  }

  // 收集所有图片文件ID
  const allImageIds = []
  dataList.forEach(data => {
    imageFields.forEach(field => {
      if (data[field]) {
        if (Array.isArray(data[field])) {
          allImageIds.push(...data[field].filter(id => id && id.startsWith('cloud://')))
        } else if (data[field].startsWith('cloud://')) {
          allImageIds.push(data[field])
        }
      }
    })
  })

  if (allImageIds.length === 0) {
    return dataList
  }

  // 批量获取图片URL
  const imageUrls = await getFileUrls(allImageIds)
  
  // 创建文件ID到URL的映射
  const urlMap = {}
  allImageIds.forEach((id, index) => {
    urlMap[id] = imageUrls[index]
  })

  // 替换数据中的图片URL
  return dataList.map(data => {
    const processedData = { ...data }
    
    imageFields.forEach(field => {
      if (processedData[field]) {
        if (Array.isArray(processedData[field])) {
          processedData[field] = processedData[field].map(id => urlMap[id] || id)
        } else if (urlMap[processedData[field]]) {
          processedData[field] = urlMap[processedData[field]]
        }
      }
    })
    
    return processedData
  })
}

/**
 * 格式化文件大小
 * @param {number} bytes - 字节数
 * @param {number} decimals - 小数位数
 * @returns {string} 格式化后的文件大小
 */
function formatFileSize(bytes, decimals = 2) {
  if (bytes === 0) return '0 Bytes'
  
  const k = 1024
  const dm = decimals < 0 ? 0 : decimals
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB']
  
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i]
}

/**
 * 检查文件类型
 * @param {string} fileName - 文件名
 * @returns {Object} 文件类型信息
 */
function getFileType(fileName) {
  if (!fileName) return { type: 'unknown', category: 'other' }
  
  const ext = fileName.toLowerCase().split('.').pop()
  
  const imageExts = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg']
  const videoExts = ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm']
  const audioExts = ['mp3', 'wav', 'flac', 'aac', 'ogg']
  const documentExts = ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt']
  
  if (imageExts.includes(ext)) {
    return { type: ext, category: 'image' }
  } else if (videoExts.includes(ext)) {
    return { type: ext, category: 'video' }
  } else if (audioExts.includes(ext)) {
    return { type: ext, category: 'audio' }
  } else if (documentExts.includes(ext)) {
    return { type: ext, category: 'document' }
  } else {
    return { type: ext, category: 'other' }
  }
}

module.exports = {
  getFileUrl,
  getFileUrls,
  processUserAvatar,
  processUsersAvatars,
  processImageFields,
  processImageFieldsBatch,
  formatFileSize,
  getFileType
}
