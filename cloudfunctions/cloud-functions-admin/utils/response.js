/**
 * 管理端响应格式化工具函数
 * 复用用户端的响应格式，确保一致性
 */

/**
 * 成功响应
 * @param {*} data - 响应数据
 * @param {string} message - 响应消息
 * @returns {Object} 格式化的成功响应
 */
function success(data = null, message = '操作成功') {
  return {
    success: true,
    message,
    data,
    timestamp: new Date().toISOString()
  }
}

/**
 * 失败响应
 * @param {string} message - 错误消息
 * @param {string} code - 错误代码
 * @param {*} data - 额外数据
 * @returns {Object} 格式化的失败响应
 */
function error(message = '操作失败', code = 'UNKNOWN_ERROR', data = null) {
  return {
    success: false,
    message,
    code,
    data,
    timestamp: new Date().toISOString()
  }
}

/**
 * 权限不足响应
 * @param {string} featureName - 功能名称
 * @param {string} requiredPermission - 所需权限
 * @returns {Object} 权限不足响应
 */
function permissionDenied(featureName = '此功能', requiredPermission = 'admin') {
  return {
    success: false,
    message: `${featureName}需要${requiredPermission}权限`,
    code: 'PERMISSION_DENIED',
    requiredPermission,
    timestamp: new Date().toISOString()
  }
}

/**
 * 参数错误响应
 * @param {string} paramName - 参数名称
 * @param {string} reason - 错误原因
 * @returns {Object} 参数错误响应
 */
function invalidParam(paramName, reason = '参数无效') {
  return {
    success: false,
    message: `参数错误: ${paramName} - ${reason}`,
    code: 'INVALID_PARAM',
    param: paramName,
    timestamp: new Date().toISOString()
  }
}

/**
 * 资源不存在响应
 * @param {string} resourceName - 资源名称
 * @returns {Object} 资源不存在响应
 */
function notFound(resourceName = '资源') {
  return {
    success: false,
    message: `${resourceName}不存在`,
    code: 'NOT_FOUND',
    resource: resourceName,
    timestamp: new Date().toISOString()
  }
}

/**
 * 服务器错误响应
 * @param {string} message - 错误消息
 * @returns {Object} 服务器错误响应
 */
function serverError(message = '服务器内部错误') {
  return {
    success: false,
    message,
    code: 'SERVER_ERROR',
    timestamp: new Date().toISOString()
  }
}

/**
 * 频率限制响应
 * @param {string} message - 限制消息
 * @returns {Object} 频率限制响应
 */
function rateLimited(message = '请求过于频繁，请稍后再试') {
  return {
    success: false,
    message,
    code: 'RATE_LIMITED',
    timestamp: new Date().toISOString()
  }
}

/**
 * 分页响应
 * @param {Array} data - 数据列表
 * @param {number} total - 总数
 * @param {number} page - 当前页
 * @param {number} pageSize - 页大小
 * @param {string} message - 响应消息
 * @returns {Object} 分页响应
 */
function paginated(data = [], total = 0, page = 1, pageSize = 20, message = '查询成功') {
  return {
    success: true,
    message,
    data,
    pagination: {
      total,
      page,
      pageSize,
      totalPages: Math.ceil(total / pageSize),
      hasNext: page * pageSize < total,
      hasPrev: page > 1
    },
    timestamp: new Date().toISOString()
  }
}

/**
 * 导出数据响应
 * @param {Array} data - 导出的数据
 * @param {string} filename - 建议的文件名
 * @param {string} format - 数据格式
 * @param {string} message - 响应消息
 * @returns {Object} 导出响应
 */
function exportData(data = [], filename = 'export.json', format = 'json', message = '数据导出成功') {
  return {
    success: true,
    message,
    data,
    export: {
      filename,
      format,
      count: Array.isArray(data) ? data.length : 1,
      generatedAt: new Date().toISOString()
    },
    timestamp: new Date().toISOString()
  }
}

/**
 * 统计数据响应
 * @param {Object} stats - 统计数据
 * @param {string} message - 响应消息
 * @returns {Object} 统计响应
 */
function statsData(stats = {}, message = '统计数据获取成功') {
  return {
    success: true,
    message,
    data: stats,
    stats: {
      generatedAt: new Date().toISOString(),
      period: stats.period || 'all',
      metrics: Object.keys(stats).filter(key => key !== 'period')
    },
    timestamp: new Date().toISOString()
  }
}

/**
 * 批量操作响应
 * @param {number} total - 总数
 * @param {number} success - 成功数
 * @param {number} failed - 失败数
 * @param {Array} errors - 错误列表
 * @param {string} message - 响应消息
 * @returns {Object} 批量操作响应
 */
function batchResult(total = 0, successCount = 0, failed = 0, errors = [], message = '批量操作完成') {
  return {
    success: failed === 0,
    message,
    data: {
      total,
      success: successCount,
      failed,
      errors: errors.slice(0, 10), // 最多返回10个错误
      hasMoreErrors: errors.length > 10
    },
    timestamp: new Date().toISOString()
  }
}

module.exports = {
  success,
  error,
  permissionDenied,
  invalidParam,
  notFound,
  serverError,
  rateLimited,
  paginated,
  exportData,
  statsData,
  batchResult
}
