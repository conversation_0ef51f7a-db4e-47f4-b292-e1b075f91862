/**
 * 管理端异步函数包装器
 * 复用用户端的异步包装逻辑，确保一致性
 */

const { success, error, invalidParam, permissionDenied, notFound, serverError } = require('./response')

/**
 * 包装异步函数，统一错误处理
 * @param {Function} fn - 异步函数
 * @returns {Function} 包装后的函数
 */
function wrapAsync(fn) {
  return async (...args) => {
    try {
      const result = await fn(...args)
      
      // 如果结果已经是标准格式，直接返回
      if (result && typeof result === 'object' && 'success' in result) {
        return result
      }
      
      // 否则包装为成功响应
      return success(result)
    } catch (err) {
      console.error('管理端API执行错误:', err)
      
      // 根据错误类型返回不同响应
      if (err.name === 'ValidationError') {
        return invalidParam(err.path, err.message)
      }
      
      if (err.name === 'PermissionError') {
        return permissionDenied(err.feature, err.required)
      }
      
      if (err.name === 'NotFoundError') {
        return notFound(err.resource)
      }
      
      if (err.name === 'AuthError') {
        return error(err.message || '身份验证失败', 'AUTH_ERROR')
      }
      
      return serverError(err.message || '未知错误')
    }
  }
}

/**
 * 高级异步包装器，支持自定义选项
 * @param {Function} fn - 异步函数
 * @param {Object} options - 选项
 * @param {boolean} options.logErrors - 是否记录错误日志
 * @param {Function} options.errorHandler - 自定义错误处理器
 * @param {string} options.operationName - 操作名称（用于日志）
 * @returns {Function} 包装后的函数
 */
function wrapAsyncWithOptions(fn, options = {}) {
  const {
    logErrors = true,
    errorHandler = null,
    operationName = 'Unknown Operation'
  } = options
  
  return async (...args) => {
    const startTime = Date.now()
    
    try {
      if (logErrors) {
        console.log(`[${operationName}] 开始执行`)
      }
      
      const result = await fn(...args)
      
      if (logErrors) {
        const duration = Date.now() - startTime
        console.log(`[${operationName}] 执行完成，耗时: ${duration}ms`)
      }
      
      // 如果结果已经是标准格式，直接返回
      if (result && typeof result === 'object' && 'success' in result) {
        return result
      }
      
      // 否则包装为成功响应
      return success(result)
    } catch (err) {
      const duration = Date.now() - startTime
      
      if (logErrors) {
        console.error(`[${operationName}] 执行失败，耗时: ${duration}ms`, err)
      }
      
      // 使用自定义错误处理器
      if (errorHandler && typeof errorHandler === 'function') {
        return errorHandler(err)
      }
      
      // 默认错误处理
      if (err.name === 'ValidationError') {
        return invalidParam(err.path, err.message)
      }
      
      if (err.name === 'PermissionError') {
        return permissionDenied(err.feature, err.required)
      }
      
      if (err.name === 'NotFoundError') {
        return notFound(err.resource)
      }
      
      if (err.name === 'AuthError') {
        return error(err.message || '身份验证失败', 'AUTH_ERROR')
      }
      
      return serverError(err.message || '未知错误')
    }
  }
}

/**
 * 批量操作包装器
 * @param {Function} fn - 批量操作函数
 * @param {Object} options - 选项
 * @returns {Function} 包装后的函数
 */
function wrapBatchAsync(fn, options = {}) {
  const { maxBatchSize = 100, operationName = 'Batch Operation' } = options
  
  return async (items = [], ...args) => {
    if (!Array.isArray(items)) {
      return invalidParam('items', '批量操作需要数组参数')
    }
    
    if (items.length === 0) {
      return success({ total: 0, success: 0, failed: 0, errors: [] }, '没有需要处理的项目')
    }
    
    if (items.length > maxBatchSize) {
      return invalidParam('items', `批量操作最多支持${maxBatchSize}个项目`)
    }
    
    const startTime = Date.now()
    console.log(`[${operationName}] 开始批量处理 ${items.length} 个项目`)
    
    let successCount = 0
    let failedCount = 0
    const errors = []
    const results = []
    
    try {
      for (let i = 0; i < items.length; i++) {
        try {
          const result = await fn(items[i], i, ...args)
          results.push(result)
          successCount++
        } catch (err) {
          failedCount++
          errors.push({
            index: i,
            item: items[i],
            error: err.message || '处理失败'
          })
          console.error(`[${operationName}] 项目 ${i} 处理失败:`, err)
        }
      }
      
      const duration = Date.now() - startTime
      console.log(`[${operationName}] 批量处理完成，耗时: ${duration}ms，成功: ${successCount}，失败: ${failedCount}`)
      
      return success({
        total: items.length,
        success: successCount,
        failed: failedCount,
        errors: errors.slice(0, 10), // 最多返回10个错误
        hasMoreErrors: errors.length > 10,
        results: results
      }, `批量操作完成，成功: ${successCount}，失败: ${failedCount}`)
      
    } catch (err) {
      const duration = Date.now() - startTime
      console.error(`[${operationName}] 批量处理异常，耗时: ${duration}ms`, err)
      return serverError(`批量操作失败: ${err.message}`)
    }
  }
}

module.exports = {
  wrapAsync,
  wrapAsyncWithOptions,
  wrapBatchAsync
}
