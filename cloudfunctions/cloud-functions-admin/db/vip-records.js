/**
 * VIP记录数据库操作
 */

const BaseDB = require('./base')

class VipRecordsDB extends BaseDB {
  constructor() {
    super('vip-records')
  }

  /**
   * 创建VIP记录
   * @param {Object} recordData - VIP记录数据
   * @returns {Promise<Object>} 创建结果
   */
  async createRecord(recordData) {
    const defaultData = {
      userId: recordData.userId,
      userNumber: recordData.userNumber,
      action: recordData.action, // 'activate', 'extend', 'expire', 'cancel'
      duration: recordData.duration || 0, // 天数
      startDate: recordData.startDate,
      endDate: recordData.endDate,
      source: recordData.source || 'manual', // 'purchase', 'gift', 'manual'
      description: recordData.description || '',
      relatedId: recordData.relatedId || null,
      ...recordData
    }

    return await this.create(defaultData)
  }

  /**
   * 获取用户VIP记录
   * @param {string} userId - 用户ID
   * @param {Object} options - 查询选项
   * @returns {Promise<Object>} VIP记录
   */
  async getUserRecords(userId, options = {}) {
    try {
      const { limit = 20, page = 1 } = options
      const skip = (page - 1) * limit

      const result = await this.find({ userId }, {
        orderBy: { field: 'createTime', order: 'desc' },
        limit,
        skip
      })

      return result
    } catch (error) {
      console.error('获取用户VIP记录失败:', error)
      return {
        success: false,
        message: error.message || '获取用户VIP记录失败'
      }
    }
  }

  /**
   * 获取所有VIP记录（管理端）
   * @param {Object} options - 查询选项
   * @returns {Promise<Object>} VIP记录列表
   */
  async getAllRecords(options = {}) {
    try {
      const { action, source, userId, limit, page = 1 } = options
      
      let query = {}
      
      if (action && action !== 'all') {
        query.action = action
      }
      
      if (source && source !== 'all') {
        query.source = source
      }
      
      if (userId && userId.trim()) {
        query.userId = userId.trim()
      }

      const queryOptions = {
        orderBy: { field: 'createTime', order: 'desc' }
      }

      if (limit) {
        queryOptions.limit = limit
        queryOptions.skip = (page - 1) * limit
      }

      return await this.find(query, queryOptions)
    } catch (error) {
      console.error('获取VIP记录列表失败:', error)
      return {
        success: false,
        message: error.message || '获取VIP记录列表失败'
      }
    }
  }

  /**
   * 获取VIP统计信息
   * @param {Object} options - 统计选项
   * @returns {Promise<Object>} 统计结果
   */
  async getVipStats(options = {}) {
    try {
      const { period = '30d' } = options
      
      // 计算时间范围
      const now = new Date()
      let startDate = null
      
      if (period === '7d') {
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
      } else if (period === '30d') {
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
      } else if (period === '90d') {
        startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000)
      }

      let query = {}
      if (startDate) {
        query.createTime = this.db.command.gte(startDate.toISOString())
      }

      // 获取所有记录
      const allRecordsResult = await this.find(query)
      
      if (!allRecordsResult.success) {
        return allRecordsResult
      }

      const records = allRecordsResult.data || []
      
      // 计算统计数据
      let activations = 0
      let extensions = 0
      let cancellations = 0
      let totalDuration = 0

      records.forEach(record => {
        switch (record.action) {
          case 'activate':
            activations++
            totalDuration += record.duration || 0
            break
          case 'extend':
            extensions++
            totalDuration += record.duration || 0
            break
          case 'cancel':
            cancellations++
            break
        }
      })

      return {
        success: true,
        data: {
          totalRecords: records.length,
          activations,
          extensions,
          cancellations,
          totalDuration,
          averageDuration: records.length > 0 ? Math.round(totalDuration / records.length) : 0,
          period
        }
      }
    } catch (error) {
      console.error('获取VIP统计失败:', error)
      return {
        success: false,
        message: error.message || '获取VIP统计失败'
      }
    }
  }

  /**
   * 获取当前有效VIP用户数量
   * @returns {Promise<Object>} 统计结果
   */
  async getActiveVipCount() {
    try {
      const now = new Date().toISOString()
      
      // 这里需要查询users表来获取当前有效的VIP用户
      // 由于这是VIP记录表，我们只能提供记录统计
      const recentActivationsResult = await this.find({
        action: 'activate',
        createTime: this.db.command.gte(new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString())
      })

      return {
        success: true,
        data: {
          recentActivations: recentActivationsResult.success ? recentActivationsResult.data.length : 0,
          note: '此统计基于最近30天的激活记录，实际有效VIP数量需要查询用户表'
        }
      }
    } catch (error) {
      console.error('获取活跃VIP统计失败:', error)
      return {
        success: false,
        message: error.message || '获取活跃VIP统计失败'
      }
    }
  }

  /**
   * 删除用户VIP记录
   * @param {string} userId - 用户ID
   * @returns {Promise<Object>} 删除结果
   */
  async deleteUserRecords(userId) {
    try {
      return await this.delete({ userId })
    } catch (error) {
      console.error('删除用户VIP记录失败:', error)
      return {
        success: false,
        message: error.message || '删除用户VIP记录失败'
      }
    }
  }

  /**
   * 获取VIP收入统计（基于购买记录）
   * @param {Object} options - 统计选项
   * @returns {Promise<Object>} 收入统计
   */
  async getRevenueStats(options = {}) {
    try {
      const { period = '30d' } = options
      
      // 计算时间范围
      const now = new Date()
      let startDate = null
      
      if (period === '7d') {
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
      } else if (period === '30d') {
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
      } else if (period === '90d') {
        startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000)
      }

      let query = { source: 'purchase' }
      if (startDate) {
        query.createTime = this.db.command.gte(startDate.toISOString())
      }

      // 获取购买记录
      const purchaseRecordsResult = await this.find(query)
      
      if (!purchaseRecordsResult.success) {
        return purchaseRecordsResult
      }

      const records = purchaseRecordsResult.data || []
      
      // 这里需要根据实际的价格字段来计算收入
      // 假设记录中有price字段
      let totalRevenue = 0
      let purchaseCount = records.length

      records.forEach(record => {
        if (record.price) {
          totalRevenue += record.price
        }
      })

      return {
        success: true,
        data: {
          totalRevenue,
          purchaseCount,
          averagePrice: purchaseCount > 0 ? Math.round(totalRevenue / purchaseCount * 100) / 100 : 0,
          period
        }
      }
    } catch (error) {
      console.error('获取VIP收入统计失败:', error)
      return {
        success: false,
        message: error.message || '获取VIP收入统计失败'
      }
    }
  }
}

module.exports = new VipRecordsDB()
