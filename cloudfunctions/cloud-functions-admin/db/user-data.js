/**
 * 用户数据存储操作
 */

const BaseDB = require('./base')
const { formatDate, normalizeTime } = require('../utils/date.js')

class UserDataDB extends BaseDB {
  constructor() {
    super('user-data')
  }

  /**
   * 根据用户ID查找数据
   * @param {string} userId - 用户ID
   * @returns {Promise<Object>} 查询结果
   */
  async findByUserId(userId) {
    return await this.findOne({ userId })
  }

  /**
   * 保存用户数据
   * @param {string} userId - 用户ID
   * @param {Object} userData - 用户数据
   * @returns {Promise<Object>} 保存结果
   */
  async saveUserData(userId, userData) {
    try {
      // 使用 userData 中的 lastModified 作为时间来源
      const userLastModified = userData.lastModified || new Date().toISOString()
      const saveTime = new Date(userLastModified)

      // 获取当前日期（用于同一天判断）
      const currentDate = formatDate(saveTime, 'YYYY-MM-DD') // YYYY-MM-DD 格式

      const dataToSave = {
        userId,
        data: userData,
        lastModified: normalizeTime(saveTime),
        recordDate: currentDate
      }

      // 查找同一天的数据
      const todayDataResult = await this.findOne({
        userId,
        recordDate: currentDate
      })

      if (todayDataResult.success && todayDataResult.data) {
        // 同一天内有数据，更新（覆盖）现有数据
        console.log(`更新用户 ${userId} 在 ${currentDate} 的数据`)
        return await this.updateById(todayDataResult.data._id, dataToSave)
      } else {
        // 同一天内没有数据，创建新记录
        console.log(`创建用户 ${userId} 在 ${currentDate} 的新数据`)
        return await this.create(dataToSave)
      }
    } catch (error) {
      console.error('保存用户数据失败:', error)
      return {
        success: false,
        message: error.message || '保存用户数据失败'
      }
    }
  }

  /**
   * 获取用户数据（获取最新的数据）
   * @param {string} userId - 用户ID
   * @returns {Promise<Object>} 查询结果
   */
  async getUserData(userId) {
    try {
      // 获取用户的最新数据（按 lastModified 降序排列，取第一条）
      const result = await this.find({ userId }, {
        orderBy: { field: 'lastModified', order: 'desc' },
        limit: 1
      })

      if (!result.success) {
        return result
      }

      if (!result.data || result.data.length === 0) {
        return {
          success: true,
          data: null,
          hasData: false
        }
      }

      const latestRecord = result.data[0]

      // 使用 userData 中的 lastModified 作为时间来源
      const userData = latestRecord.data
      const userLastModified = userData.lastModified || latestRecord.lastModified
      const normalizedTimestamp = Math.floor(new Date(userLastModified).getTime() / 1000) * 1000

      return {
        success: true,
        data: userData,
        hasData: true,
        timestamp: normalizedTimestamp,
        recordDate: latestRecord.recordDate,
        _id: latestRecord._id
      }
    } catch (error) {
      console.error('获取用户数据失败:', error)
      return {
        success: false,
        message: error.message || '获取用户数据失败'
      }
    }
  }

  /**
   * 获取用户历史数据
   * @param {string} userId - 用户ID
   * @param {Object} options - 查询选项
   * @returns {Promise<Object>} 历史数据
   */
  async getUserHistory(userId, options = {}) {
    try {
      const { limit = 30, startDate, endDate } = options
      
      let query = { userId }
      
      if (startDate && endDate) {
        query.recordDate = this.db.command.gte(startDate).and(this.db.command.lte(endDate))
      }

      const result = await this.find(query, {
        orderBy: { field: 'lastModified', order: 'desc' },
        limit
      })

      return result
    } catch (error) {
      console.error('获取用户历史数据失败:', error)
      return {
        success: false,
        message: error.message || '获取用户历史数据失败'
      }
    }
  }

  /**
   * 删除用户数据
   * @param {string} userId - 用户ID
   * @returns {Promise<Object>} 删除结果
   */
  async deleteUserData(userId) {
    try {
      return await this.delete({ userId })
    } catch (error) {
      console.error('删除用户数据失败:', error)
      return {
        success: false,
        message: error.message || '删除用户数据失败'
      }
    }
  }

  /**
   * 获取数据统计
   * @returns {Promise<Object>} 统计结果
   */
  async getStats() {
    try {
      const totalResult = await this.count()
      const todayDate = formatDate(new Date(), 'YYYY-MM-DD')
      const todayResult = await this.count({ recordDate: todayDate })

      return {
        success: true,
        data: {
          total: totalResult.success ? totalResult.data.total : 0,
          today: todayResult.success ? todayResult.data.total : 0
        }
      }
    } catch (error) {
      console.error('获取数据统计失败:', error)
      return {
        success: false,
        message: error.message || '获取数据统计失败'
      }
    }
  }
}

module.exports = new UserDataDB()
