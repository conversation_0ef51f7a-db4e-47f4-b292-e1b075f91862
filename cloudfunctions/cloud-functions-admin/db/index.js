/**
 * 数据库操作统一入口 - 管理端
 */

const usersDB = require('./users')
const userDataDB = require('./user-data')
const announcementsDB = require('./announcements')
const vipRecordsDB = require('./vip-records')
const fishingStatusDB = require('./fishing-status')
const feedbackDB = require('./feedback')
const friendAppsDB = require('./friend-apps')
const cacheDB = require('./cache')
const checkInsDB = require('./check-ins')
const pointsRecordsDB = require('./points-records')
const redemptionCodesDB = require('./redemption-codes')
const storeItemsDB = require('./store-items')

module.exports = {
  users: usersDB,
  userData: userDataDB,
  announcements: announcementsDB,
  vipRecords: vipRecordsDB,
  fishingStatus: fishingStatusDB,
  feedback: feedbackDB,
  friendApps: friendAppsDB,
  cache: cacheDB,
  checkIns: checkInsDB,
  pointsRecords: pointsRecordsDB,
  redemptionCodes: redemptionCodesDB,
  storeItems: storeItemsDB
}
