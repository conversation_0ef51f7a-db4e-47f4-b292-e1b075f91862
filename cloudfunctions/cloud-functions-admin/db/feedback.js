/**
 * 反馈数据库操作类（管理端）
 */

const BaseDB = require('./base')

class FeedbackDB extends BaseDB {
  constructor() {
    super('feedback')
  }

  /**
   * 获取反馈列表（管理端）
   * @param {Object} options - 查询选项
   * @returns {Promise<Object>} 查询结果
   */
  async getFeedbackListAdmin(options = {}) {
    try {
      const {
        status = 'all',
        type = 'all',
        keyword = '',
        startDate,
        endDate,
        sortBy = 'createTime',
        sortOrder = 'desc',
        limit = 20,
        skip = 0
      } = options

      console.log(`[FeedbackDB] 管理端获取反馈列表: status=${status}, type=${type}, keyword=${keyword}`)

      // 构建查询条件
      const where = { isDeleted: false }
      
      if (status !== 'all') {
        where.status = status
      }
      
      if (type !== 'all') {
        where.category = type
      }
      
      if (keyword) {
        where.$or = [
          { content: new RegExp(keyword, 'i') },
          { email: new RegExp(keyword, 'i') },
          { reply: new RegExp(keyword, 'i') }
        ]
      }
      
      if (startDate || endDate) {
        where.createTime = {}
        if (startDate) {
          where.createTime.$gte = startDate
        }
        if (endDate) {
          where.createTime.$lte = endDate
        }
      }

      // 查询总数
      const countResult = await this.count(where)
      const total = countResult.success ? countResult.data : 0

      // 查询数据
      const dataResult = await this.find(where, {
        limit,
        skip,
        orderBy: sortBy,
        order: sortOrder
      })

      if (!dataResult.success) {
        return dataResult
      }

      console.log(`[FeedbackDB] 管理端反馈查询成功，共 ${dataResult.data.length} 条记录，总计 ${total} 条`)

      return {
        success: true,
        data: {
          list: dataResult.data,
          total,
          hasMore: skip + dataResult.data.length < total
        }
      }
    } catch (error) {
      console.error('[FeedbackDB] 管理端获取反馈列表失败:', error)
      return { 
        success: false, 
        message: error.message 
      }
    }
  }

  /**
   * 根据ID获取反馈详情
   * @param {string} id - 反馈ID
   * @returns {Promise<Object>} 查询结果
   */
  async getFeedbackById(id) {
    try {
      console.log(`[FeedbackDB] 获取反馈详情: ${id}`)
      
      const result = await this.findById(id)
      
      return result
    } catch (error) {
      console.error(`[FeedbackDB] 获取反馈详情失败: ${id}`, error)
      return { 
        success: false, 
        message: error.message 
      }
    }
  }

  /**
   * 回复反馈
   * @param {string} id - 反馈ID
   * @param {Object} replyData - 回复数据
   * @returns {Promise<Object>} 更新结果
   */
  async replyFeedback(id, replyData) {
    try {
      console.log(`[FeedbackDB] 回复反馈: ${id}`)
      
      const updateData = {
        ...replyData,
        updateTime: new Date()
      }
      
      const result = await this.update({ _id: id }, updateData)
      
      if (result.success) {
        console.log(`[FeedbackDB] 反馈回复成功: ${id}`)
      }
      
      return result
    } catch (error) {
      console.error(`[FeedbackDB] 回复反馈失败: ${id}`, error)
      return { 
        success: false, 
        message: error.message 
      }
    }
  }

  /**
   * 更新反馈状态
   * @param {string} id - 反馈ID
   * @param {Object} updateData - 更新数据
   * @returns {Promise<Object>} 更新结果
   */
  async updateFeedbackStatus(id, updateData) {
    try {
      console.log(`[FeedbackDB] 更新反馈状态: ${id}`)
      
      const data = {
        ...updateData,
        updateTime: new Date()
      }
      
      const result = await this.update({ _id: id }, data)
      
      if (result.success) {
        console.log(`[FeedbackDB] 反馈状态更新成功: ${id}`)
      }
      
      return result
    } catch (error) {
      console.error(`[FeedbackDB] 更新反馈状态失败: ${id}`, error)
      return { 
        success: false, 
        message: error.message 
      }
    }
  }

  /**
   * 获取反馈统计信息（管理端）
   * @param {string} period - 统计周期
   * @returns {Promise<Object>} 统计结果
   */
  async getFeedbackStatsAdmin(period = '30d') {
    try {
      console.log(`[FeedbackDB] 获取反馈统计信息: ${period}`)

      // 计算时间范围
      let startDate = null
      const now = new Date()
      
      switch (period) {
        case '7d':
          startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
          break
        case '30d':
          startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
          break
        case '90d':
          startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000)
          break
        case '1y':
          startDate = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000)
          break
        case 'all':
        default:
          startDate = null
          break
      }

      const baseWhere = { isDeleted: false }
      if (startDate) {
        baseWhere.createTime = { $gte: startDate }
      }

      // 总反馈数
      const totalResult = await this.count(baseWhere)
      const total = totalResult.success ? totalResult.data : 0

      // 已回复的反馈数
      const repliedResult = await this.count({ ...baseWhere, status: { $in: ['replied', 'resolved'] } })
      const replied = repliedResult.success ? repliedResult.data : 0

      // 回复率
      const replyRate = total > 0 ? (replied / total * 100).toFixed(2) : 0

      const stats = {
        total,
        replied,
        pending: total - replied,
        replyRate: parseFloat(replyRate),
        period,
        generatedAt: new Date().toISOString()
      }

      console.log('[FeedbackDB] 反馈统计信息获取成功')

      return {
        success: true,
        data: stats
      }
    } catch (error) {
      console.error('[FeedbackDB] 获取反馈统计失败:', error)
      return {
        success: false,
        message: error.message
      }
    }
  }

  /**
   * 按日期范围获取反馈统计
   * @param {Date} startDate - 开始日期
   * @param {Date} endDate - 结束日期
   * @returns {Promise<Object>} 统计结果
   */
  async getFeedbackStatsByDateRange(startDate, endDate) {
    try {
      console.log(`[FeedbackDB] 按日期范围获取反馈统计: ${startDate.toISOString()} - ${endDate.toISOString()}`)

      const baseWhere = {
        isDeleted: false,
        createTime: {
          $gte: startDate.toISOString(),
          $lt: endDate.toISOString()
        }
      }

      // 总反馈数
      const totalResult = await this.count(baseWhere)
      const total = totalResult.success ? totalResult.data : 0

      // 已回复的反馈数
      const repliedResult = await this.count({ ...baseWhere, status: { $in: ['replied', 'resolved'] } })
      const replied = repliedResult.success ? repliedResult.data : 0

      // 回复率
      const replyRate = total > 0 ? (replied / total * 100).toFixed(2) : 0

      const stats = {
        total,
        replied,
        pending: total - replied,
        replyRate: parseFloat(replyRate)
      }

      return {
        success: true,
        data: stats
      }
    } catch (error) {
      console.error('[FeedbackDB] 按日期范围获取反馈统计失败:', error)
      return {
        success: false,
        message: error.message
      }
    }
  }

  /**
   * 导出反馈数据（管理端）
   * @param {Object} options - 导出选项
   * @returns {Promise<Object>} 导出结果
   */
  async exportFeedbackDataAdmin(options = {}) {
    try {
      const {
        status = 'all',
        type = 'all',
        startDate,
        endDate,
        limit = 1000
      } = options

      console.log(`[FeedbackDB] 导出反馈数据: status=${status}, type=${type}, limit=${limit}`)

      // 构建查询条件
      const where = { isDeleted: false }
      
      if (status !== 'all') {
        where.status = status
      }
      
      if (type !== 'all') {
        where.category = type
      }
      
      if (startDate || endDate) {
        where.createTime = {}
        if (startDate) {
          where.createTime.$gte = startDate
        }
        if (endDate) {
          where.createTime.$lte = endDate
        }
      }

      // 查询数据
      const result = await this.find(where, {
        orderBy: 'createTime',
        order: 'desc',
        limit
      })

      if (!result.success) {
        return result
      }

      console.log(`[FeedbackDB] 反馈数据导出成功，共 ${result.data.length} 条记录`)

      return {
        success: true,
        data: result.data
      }
    } catch (error) {
      console.error('[FeedbackDB] 导出反馈数据失败:', error)
      return { 
        success: false, 
        message: error.message 
      }
    }
  }

  /**
   * 批量操作反馈
   * @param {Array} ids - 反馈ID列表
   * @param {string} action - 操作类型
   * @param {Object} data - 操作数据
   * @returns {Promise<Object>} 操作结果
   */
  async batchOperateFeedback(ids, action, data = {}) {
    try {
      console.log(`[FeedbackDB] 批量操作反馈: action=${action}, count=${ids.length}`)

      let updateData = {}
      
      switch (action) {
        case 'updateStatus':
          if (!data.status) {
            throw new Error('缺少状态参数')
          }
          updateData = { 
            status: data.status,
            updateTime: new Date()
          }
          break
        case 'delete':
          updateData = { 
            isDeleted: true,
            deleteTime: new Date()
          }
          break
        case 'archive':
          updateData = { 
            status: 'archived',
            updateTime: new Date()
          }
          break
        default:
          throw new Error('不支持的操作类型')
      }

      // 批量更新
      const promises = ids.map(id => 
        this.update({ _id: id }, updateData)
      )
      
      const results = await Promise.allSettled(promises)
      
      const successCount = results.filter(r => r.status === 'fulfilled' && r.value.success).length
      const failedCount = results.filter(r => r.status === 'rejected' || !r.value.success).length

      console.log(`[FeedbackDB] 批量操作完成: 成功=${successCount}, 失败=${failedCount}`)

      return {
        success: true,
        data: {
          total: ids.length,
          success: successCount,
          failed: failedCount,
          results
        }
      }
    } catch (error) {
      console.error('[FeedbackDB] 批量操作反馈失败:', error)
      return { 
        success: false, 
        message: error.message 
      }
    }
  }
}

module.exports = new FeedbackDB()
