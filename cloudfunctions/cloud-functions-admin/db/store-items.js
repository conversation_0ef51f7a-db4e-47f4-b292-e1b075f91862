/**
 * 商店商品数据库操作
 */

const BaseDB = require('./base')

class StoreItemsDB extends BaseDB {
  constructor() {
    super('store-items')
  }

  /**
   * 创建商品
   * @param {Object} itemData - 商品数据
   * @returns {Promise<Object>} 创建结果
   */
  async createItem(itemData) {
    const defaultData = {
      name: itemData.name,
      description: itemData.description || '',
      price: itemData.price,
      category: itemData.category || 'other',
      imageUrl: itemData.imageUrl || '',
      isAvailable: itemData.isAvailable !== false,
      stock: itemData.stock || 0,
      sortOrder: itemData.sortOrder || 0,
      tags: itemData.tags || [],
      ...itemData
    }

    return await this.create(defaultData)
  }

  /**
   * 获取可用商品列表
   * @param {Object} options - 查询选项
   * @returns {Promise<Object>} 商品列表
   */
  async getAvailableItems(options = {}) {
    try {
      const { category, limit, page = 1 } = options
      
      let query = { isAvailable: true }
      
      if (category && category !== 'all') {
        query.category = category
      }

      const queryOptions = {
        orderBy: { field: 'sortOrder', order: 'asc' }
      }

      if (limit) {
        queryOptions.limit = limit
        queryOptions.skip = (page - 1) * limit
      }

      return await this.find(query, queryOptions)
    } catch (error) {
      console.error('获取可用商品列表失败:', error)
      return {
        success: false,
        message: error.message || '获取可用商品列表失败'
      }
    }
  }

  /**
   * 获取所有商品列表（管理端）
   * @param {Object} options - 查询选项
   * @returns {Promise<Object>} 商品列表
   */
  async getAllItems(options = {}) {
    try {
      const { category, isAvailable, keyword, limit, page = 1 } = options
      
      let query = {}
      
      if (category && category !== 'all') {
        query.category = category
      }
      
      if (isAvailable !== undefined && isAvailable !== 'all') {
        query.isAvailable = isAvailable
      }

      const queryOptions = {
        orderBy: { field: 'sortOrder', order: 'asc' }
      }

      if (limit) {
        queryOptions.limit = limit
        queryOptions.skip = (page - 1) * limit
      }

      const result = await this.find(query, queryOptions)
      
      // 如果有关键词搜索，在前端过滤
      if (result.success && keyword && keyword.trim()) {
        const searchTerm = keyword.trim().toLowerCase()
        result.data = result.data.filter(item => 
          item.name.toLowerCase().includes(searchTerm) ||
          item.description.toLowerCase().includes(searchTerm)
        )
      }

      return result
    } catch (error) {
      console.error('获取商品列表失败:', error)
      return {
        success: false,
        message: error.message || '获取商品列表失败'
      }
    }
  }

  /**
   * 更新商品
   * @param {string} id - 商品ID
   * @param {Object} updateData - 更新数据
   * @returns {Promise<Object>} 更新结果
   */
  async updateItem(id, updateData) {
    try {
      // 过滤允许更新的字段
      const allowedFields = [
        'name', 'description', 'price', 'category', 'imageUrl',
        'isAvailable', 'stock', 'sortOrder', 'tags'
      ]
      
      const filteredData = {}
      allowedFields.forEach(field => {
        if (updateData.hasOwnProperty(field)) {
          filteredData[field] = updateData[field]
        }
      })

      if (Object.keys(filteredData).length === 0) {
        return {
          success: false,
          message: '没有可更新的字段'
        }
      }

      return await this.updateById(id, filteredData)
    } catch (error) {
      console.error('更新商品失败:', error)
      return {
        success: false,
        message: error.message || '更新商品失败'
      }
    }
  }

  /**
   * 删除商品
   * @param {string} id - 商品ID
   * @returns {Promise<Object>} 删除结果
   */
  async deleteItem(id) {
    try {
      return await this.deleteById(id)
    } catch (error) {
      console.error('删除商品失败:', error)
      return {
        success: false,
        message: error.message || '删除商品失败'
      }
    }
  }

  /**
   * 更新商品库存
   * @param {string} id - 商品ID
   * @param {number} quantity - 数量变化（正数增加，负数减少）
   * @returns {Promise<Object>} 更新结果
   */
  async updateStock(id, quantity) {
    try {
      const itemResult = await this.findById(id)
      if (!itemResult.success || !itemResult.data) {
        return {
          success: false,
          message: '商品不存在'
        }
      }

      const currentStock = itemResult.data.stock || 0
      const newStock = Math.max(0, currentStock + quantity)

      return await this.updateById(id, { stock: newStock })
    } catch (error) {
      console.error('更新商品库存失败:', error)
      return {
        success: false,
        message: error.message || '更新商品库存失败'
      }
    }
  }

  /**
   * 切换商品可用状态
   * @param {string} id - 商品ID
   * @param {boolean} isAvailable - 是否可用
   * @returns {Promise<Object>} 更新结果
   */
  async toggleAvailability(id, isAvailable) {
    try {
      return await this.updateById(id, { isAvailable })
    } catch (error) {
      console.error('切换商品状态失败:', error)
      return {
        success: false,
        message: error.message || '切换商品状态失败'
      }
    }
  }

  /**
   * 获取商品统计信息
   * @returns {Promise<Object>} 统计结果
   */
  async getStats() {
    try {
      const totalResult = await this.count()
      const availableResult = await this.count({ isAvailable: true })
      const outOfStockResult = await this.count({ stock: 0 })

      return {
        success: true,
        data: {
          total: totalResult.success ? totalResult.data.total : 0,
          available: availableResult.success ? availableResult.data.total : 0,
          outOfStock: outOfStockResult.success ? outOfStockResult.data.total : 0
        }
      }
    } catch (error) {
      console.error('获取商品统计失败:', error)
      return {
        success: false,
        message: error.message || '获取商品统计失败'
      }
    }
  }

  /**
   * 获取商品分类列表
   * @returns {Promise<Object>} 分类列表
   */
  async getCategories() {
    try {
      // 获取所有商品的分类
      const result = await this.find({}, { 
        fields: { category: true }
      })

      if (!result.success) {
        return result
      }

      // 提取唯一分类
      const categories = [...new Set(result.data.map(item => item.category))]
        .filter(category => category && category.trim())
        .sort()

      return {
        success: true,
        data: categories
      }
    } catch (error) {
      console.error('获取商品分类失败:', error)
      return {
        success: false,
        message: error.message || '获取商品分类失败'
      }
    }
  }
}

module.exports = new StoreItemsDB()
