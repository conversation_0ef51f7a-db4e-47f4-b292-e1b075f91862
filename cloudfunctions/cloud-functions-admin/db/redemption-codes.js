/**
 * 兑换码数据库操作
 */

const BaseDB = require('./base')

class RedemptionCodesDB extends BaseDB {
  constructor() {
    super('redemption-codes')
  }

  /**
   * 创建兑换码
   * @param {Object} codeData - 兑换码数据
   * @returns {Promise<Object>} 创建结果
   */
  async createCode(codeData) {
    const defaultData = {
      code: codeData.code,
      type: codeData.type, // 'points', 'item', 'discount'
      value: codeData.value, // 积分数量、商品ID或折扣百分比
      description: codeData.description || '',
      isActive: codeData.isActive !== false,
      usageLimit: codeData.usageLimit || 1, // 使用次数限制
      usedCount: 0,
      expiresAt: codeData.expiresAt || null,
      createdBy: codeData.createdBy || 'admin',
      ...codeData
    }

    return await this.create(defaultData)
  }

  /**
   * 根据兑换码查找
   * @param {string} code - 兑换码
   * @returns {Promise<Object>} 查询结果
   */
  async findByCode(code) {
    return await this.findOne({ code })
  }

  /**
   * 验证兑换码是否有效
   * @param {string} code - 兑换码
   * @returns {Promise<Object>} 验证结果
   */
  async validateCode(code) {
    try {
      const result = await this.findByCode(code)
      
      if (!result.success || !result.data) {
        return {
          success: false,
          message: '兑换码不存在',
          code: 'CODE_NOT_FOUND'
        }
      }

      const codeData = result.data

      // 检查是否激活
      if (!codeData.isActive) {
        return {
          success: false,
          message: '兑换码已被禁用',
          code: 'CODE_DISABLED'
        }
      }

      // 检查是否过期
      if (codeData.expiresAt && new Date() > new Date(codeData.expiresAt)) {
        return {
          success: false,
          message: '兑换码已过期',
          code: 'CODE_EXPIRED'
        }
      }

      // 检查使用次数
      if (codeData.usedCount >= codeData.usageLimit) {
        return {
          success: false,
          message: '兑换码使用次数已达上限',
          code: 'CODE_EXHAUSTED'
        }
      }

      return {
        success: true,
        data: codeData,
        message: '兑换码有效'
      }
    } catch (error) {
      console.error('验证兑换码失败:', error)
      return {
        success: false,
        message: error.message || '验证兑换码失败',
        code: 'VALIDATION_ERROR'
      }
    }
  }

  /**
   * 使用兑换码（增加使用次数）
   * @param {string} code - 兑换码
   * @param {string} userId - 使用者ID
   * @returns {Promise<Object>} 使用结果
   */
  async useCode(code, userId) {
    try {
      // 先验证兑换码
      const validation = await this.validateCode(code)
      if (!validation.success) {
        return validation
      }

      const codeData = validation.data

      // 增加使用次数
      const updateResult = await this.updateById(codeData._id, {
        usedCount: codeData.usedCount + 1,
        lastUsedAt: new Date().toISOString(),
        lastUsedBy: userId
      })

      if (!updateResult.success) {
        return {
          success: false,
          message: '更新兑换码使用状态失败'
        }
      }

      return {
        success: true,
        data: {
          type: codeData.type,
          value: codeData.value,
          description: codeData.description,
          remainingUses: codeData.usageLimit - (codeData.usedCount + 1)
        },
        message: '兑换码使用成功'
      }
    } catch (error) {
      console.error('使用兑换码失败:', error)
      return {
        success: false,
        message: error.message || '使用兑换码失败'
      }
    }
  }

  /**
   * 获取兑换码列表（管理端）
   * @param {Object} options - 查询选项
   * @returns {Promise<Object>} 兑换码列表
   */
  async getCodesList(options = {}) {
    try {
      const { type, isActive, keyword, limit, page = 1 } = options
      
      let query = {}
      
      if (type && type !== 'all') {
        query.type = type
      }
      
      if (isActive !== undefined && isActive !== 'all') {
        query.isActive = isActive
      }

      const queryOptions = {
        orderBy: { field: 'createTime', order: 'desc' }
      }

      if (limit) {
        queryOptions.limit = limit
        queryOptions.skip = (page - 1) * limit
      }

      const result = await this.find(query, queryOptions)
      
      // 如果有关键词搜索，在前端过滤
      if (result.success && keyword && keyword.trim()) {
        const searchTerm = keyword.trim().toLowerCase()
        result.data = result.data.filter(code => 
          code.code.toLowerCase().includes(searchTerm) ||
          code.description.toLowerCase().includes(searchTerm)
        )
      }

      return result
    } catch (error) {
      console.error('获取兑换码列表失败:', error)
      return {
        success: false,
        message: error.message || '获取兑换码列表失败'
      }
    }
  }

  /**
   * 更新兑换码
   * @param {string} id - 兑换码ID
   * @param {Object} updateData - 更新数据
   * @returns {Promise<Object>} 更新结果
   */
  async updateCode(id, updateData) {
    try {
      // 过滤允许更新的字段
      const allowedFields = [
        'description', 'isActive', 'usageLimit', 'expiresAt'
      ]
      
      const filteredData = {}
      allowedFields.forEach(field => {
        if (updateData.hasOwnProperty(field)) {
          filteredData[field] = updateData[field]
        }
      })

      if (Object.keys(filteredData).length === 0) {
        return {
          success: false,
          message: '没有可更新的字段'
        }
      }

      return await this.updateById(id, filteredData)
    } catch (error) {
      console.error('更新兑换码失败:', error)
      return {
        success: false,
        message: error.message || '更新兑换码失败'
      }
    }
  }

  /**
   * 删除兑换码
   * @param {string} id - 兑换码ID
   * @returns {Promise<Object>} 删除结果
   */
  async deleteCode(id) {
    try {
      return await this.deleteById(id)
    } catch (error) {
      console.error('删除兑换码失败:', error)
      return {
        success: false,
        message: error.message || '删除兑换码失败'
      }
    }
  }

  /**
   * 切换兑换码激活状态
   * @param {string} id - 兑换码ID
   * @param {boolean} isActive - 是否激活
   * @returns {Promise<Object>} 更新结果
   */
  async toggleActive(id, isActive) {
    try {
      return await this.updateById(id, { isActive })
    } catch (error) {
      console.error('切换兑换码状态失败:', error)
      return {
        success: false,
        message: error.message || '切换兑换码状态失败'
      }
    }
  }

  /**
   * 获取兑换码统计信息
   * @returns {Promise<Object>} 统计结果
   */
  async getStats() {
    try {
      const totalResult = await this.count()
      const activeResult = await this.count({ isActive: true })
      const expiredResult = await this.count({
        expiresAt: this.db.command.lt(new Date().toISOString())
      })

      // 获取使用统计
      const allCodesResult = await this.find({})
      let totalUsed = 0
      if (allCodesResult.success) {
        totalUsed = allCodesResult.data.reduce((sum, code) => sum + (code.usedCount || 0), 0)
      }

      return {
        success: true,
        data: {
          total: totalResult.success ? totalResult.data.total : 0,
          active: activeResult.success ? activeResult.data.total : 0,
          expired: expiredResult.success ? expiredResult.data.total : 0,
          totalUsed
        }
      }
    } catch (error) {
      console.error('获取兑换码统计失败:', error)
      return {
        success: false,
        message: error.message || '获取兑换码统计失败'
      }
    }
  }

  /**
   * 生成随机兑换码
   * @param {number} length - 兑换码长度
   * @returns {string} 随机兑换码
   */
  generateRandomCode(length = 8) {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'
    let result = ''
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length))
    }
    return result
  }

  /**
   * 批量创建兑换码
   * @param {Object} codeTemplate - 兑换码模板
   * @param {number} count - 创建数量
   * @returns {Promise<Object>} 创建结果
   */
  async batchCreateCodes(codeTemplate, count) {
    try {
      const codes = []
      const createdCodes = []

      for (let i = 0; i < count; i++) {
        let code
        let attempts = 0
        
        // 生成唯一兑换码
        do {
          code = this.generateRandomCode()
          attempts++
          if (attempts > 100) {
            throw new Error('无法生成唯一兑换码')
          }
        } while (codes.includes(code))

        codes.push(code)

        const codeData = {
          ...codeTemplate,
          code
        }

        const result = await this.createCode(codeData)
        if (result.success) {
          createdCodes.push(result.data)
        }
      }

      return {
        success: true,
        data: createdCodes,
        message: `成功创建 ${createdCodes.length} 个兑换码`
      }
    } catch (error) {
      console.error('批量创建兑换码失败:', error)
      return {
        success: false,
        message: error.message || '批量创建兑换码失败'
      }
    }
  }
}

module.exports = new RedemptionCodesDB()
