/**
 * 数据库操作基类（管理端）
 * 提供通用的数据库操作方法
 */

const cloud = require('wx-server-sdk')

// 初始化云开发
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV,
})

const db = cloud.database()

class BaseDB {
  constructor(collectionName) {
    this.collectionName = collectionName
    this.collection = db.collection(collectionName)
  }

  /**
   * 创建记录
   * @param {Object} data - 要创建的数据
   * @returns {Promise<Object>} 创建结果
   */
  async create(data) {
    try {
      const createTime = new Date()
      const recordData = {
        ...data,
        createTime,
        updateTime: createTime
      }

      console.log(`[${this.collectionName}] 创建记录:`, recordData)
      
      const result = await this.collection.add({
        data: recordData
      })

      console.log(`[${this.collectionName}] 记录创建成功:`, result._id)
      
      return { 
        success: true, 
        data: { 
          _id: result._id, 
          ...recordData 
        } 
      }
    } catch (error) {
      console.error(`[${this.collectionName}] 创建记录失败:`, error)
      return { 
        success: false, 
        message: error.message 
      }
    }
  }

  /**
   * 根据ID查询记录
   * @param {string} id - 记录ID
   * @returns {Promise<Object>} 查询结果
   */
  async findById(id) {
    try {
      console.log(`[${this.collectionName}] 查询记录:`, id)

      const result = await this.collection.doc(id).get()

      // 微信云开发中，doc().get()返回的是单个文档，不是数组
      if (!result.data) {
        return {
          success: false,
          message: '记录不存在'
        }
      }

      return {
        success: true,
        data: { _id: id, ...result.data }
      }
    } catch (error) {
      console.error(`[${this.collectionName}] 查询记录失败:`, error)
      return {
        success: false,
        message: error.message
      }
    }
  }

  /**
   * 条件查询
   * @param {Object} where - 查询条件
   * @param {Object} options - 查询选项
   * @returns {Promise<Object>} 查询结果
   */
  async find(where = {}, options = {}) {
    try {
      const { 
        limit = 20, 
        skip = 0, 
        orderBy = 'createTime', 
        order = 'desc' 
      } = options

      console.log(`[${this.collectionName}] 条件查询:`, where, options)
      
      const result = await this.collection
        .where(where)
        .orderBy(orderBy, order)
        .skip(skip)
        .limit(limit)
        .get()

      return { 
        success: true, 
        data: result.data 
      }
    } catch (error) {
      console.error(`[${this.collectionName}] 条件查询失败:`, error)
      return { 
        success: false, 
        message: error.message 
      }
    }
  }

  /**
   * 更新记录
   * @param {Object} where - 查询条件
   * @param {Object} data - 更新数据
   * @returns {Promise<Object>} 更新结果
   */
  async update(where, data) {
    try {
      const updateData = {
        ...data,
        updateTime: new Date()
      }

      console.log(`[${this.collectionName}] 更新记录:`, where, updateData)

      let result

      // 如果where条件包含_id，使用doc().update()
      if (where._id) {
        result = await this.collection.doc(where._id).update({
          data: updateData
        })
      } else {
        // 否则使用where().update()
        result = await this.collection
          .where(where)
          .update({
            data: updateData
          })
      }

      console.log(`[${this.collectionName}] 记录更新成功:`, result.stats?.updated || 1)

      return {
        success: true,
        data: result
      }
    } catch (error) {
      console.error(`[${this.collectionName}] 更新记录失败:`, error)
      return {
        success: false,
        message: error.message
      }
    }
  }

  /**
   * 删除记录
   * @param {Object} where - 查询条件
   * @returns {Promise<Object>} 删除结果
   */
  async delete(where) {
    try {
      console.log(`[${this.collectionName}] 删除记录:`, where)
      
      const result = await this.collection
        .where(where)
        .remove()

      console.log(`[${this.collectionName}] 记录删除成功:`, result.stats.removed)
      
      return { 
        success: true, 
        data: result 
      }
    } catch (error) {
      console.error(`[${this.collectionName}] 删除记录失败:`, error)
      return { 
        success: false, 
        message: error.message 
      }
    }
  }

  /**
   * 计数
   * @param {Object} where - 查询条件
   * @returns {Promise<Object>} 计数结果
   */
  async count(where = {}) {
    try {
      console.log(`[${this.collectionName}] 计数查询:`, where)
      
      const result = await this.collection.where(where).count()
      
      return { 
        success: true, 
        data: result.total 
      }
    } catch (error) {
      console.error(`[${this.collectionName}] 计数查询失败:`, error)
      return { 
        success: false, 
        message: error.message 
      }
    }
  }
}

module.exports = BaseDB
