/**
 * 缓存数据库操作类
 * 提供简单的键值缓存功能，支持过期时间
 */

const BaseDB = require('./base')

class CacheDB extends BaseDB {
  constructor() {
    super('cache-data')
  }

  /**
   * 获取缓存（自动清理过期数据）
   * @param {string} key - 缓存键
   * @returns {Promise<Object>} 缓存数据或null
   */
  async get(key) {
    try {
      console.log(`[Cache] 尝试获取缓存: ${key}`)
      
      // 先清理所有过期的缓存
      await this.cleanupExpired()
      
      // 查找指定缓存
      const result = await this.findOne({ key })
      
      if (!result.success || !result.data) {
        console.log(`[Cache] 缓存未命中: ${key}`)
        return { success: true, data: null }
      }
      
      console.log(`[Cache] 缓存命中: ${key}`)
      return { success: true, data: result.data.value }
      
    } catch (error) {
      console.error(`[Cache] 获取缓存失败: ${key}`, error)
      return { success: false, message: error.message }
    }
  }

  /**
   * 设置缓存
   * @param {string} key - 缓存键
   * @param {Object} value - 缓存数据
   * @param {Date} expireTime - 过期时间
   * @returns {Promise<Object>} 操作结果
   */
  async set(key, value, expireTime) {
    try {
      console.log(`[Cache] 设置缓存: ${key}, 过期时间: ${expireTime.toISOString()}`)
      
      const cacheData = {
        key,
        value,
        expireTime,
        createTime: new Date()
      }
      
      // 先删除已存在的缓存
      await this.delete({ key })
      
      // 创建新缓存
      const result = await this.create(cacheData)
      
      if (result.success) {
        console.log(`[Cache] 缓存设置成功: ${key}`)
      }
      
      return result
      
    } catch (error) {
      console.error(`[Cache] 设置缓存失败: ${key}`, error)
      return { success: false, message: error.message }
    }
  }

  /**
   * 删除指定缓存
   * @param {string} key - 缓存键
   * @returns {Promise<Object>} 删除结果
   */
  async deleteByKey(key) {
    try {
      console.log(`[Cache] 删除缓存: ${key}`)
      const result = await this.delete({ key })
      
      if (result.success) {
        console.log(`[Cache] 缓存删除成功: ${key}`)
      }
      
      return result
      
    } catch (error) {
      console.error(`[Cache] 删除缓存失败: ${key}`, error)
      return { success: false, message: error.message }
    }
  }

  /**
   * 清理过期缓存
   * @returns {Promise<Object>} 清理结果
   */
  async cleanupExpired() {
    try {
      const now = new Date()
      const result = await this.delete({ 
        expireTime: this.db.command.lt(now) 
      })
      
      if (result.success && result.data && result.data.deleted > 0) {
        console.log(`[Cache] 清理过期缓存: ${result.data.deleted} 条`)
      }
      
      return result
      
    } catch (error) {
      console.error('[Cache] 清理过期缓存失败:', error)
      return { success: false, message: error.message }
    }
  }

  /**
   * 获取缓存统计信息
   * @returns {Promise<Object>} 统计信息
   */
  async getStats() {
    try {
      const totalResult = await this.count({})
      const now = new Date()
      const expiredResult = await this.count({
        expireTime: this.db.command.lt(now)
      })
      
      return {
        success: true,
        data: {
          total: totalResult.success ? totalResult.data.total : 0,
          expired: expiredResult.success ? expiredResult.data.total : 0,
          active: (totalResult.success ? totalResult.data.total : 0) - 
                  (expiredResult.success ? expiredResult.data.total : 0)
        }
      }
      
    } catch (error) {
      console.error('[Cache] 获取缓存统计失败:', error)
      return { success: false, message: error.message }
    }
  }
}

module.exports = new CacheDB()
