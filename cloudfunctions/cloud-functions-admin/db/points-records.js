/**
 * 积分记录数据库操作
 */

const BaseDB = require('./base')

class PointsRecordsDB extends BaseDB {
  constructor() {
    super('points-records')
  }

  /**
   * 创建积分记录
   * @param {Object} recordData - 积分记录数据
   * @returns {Promise<Object>} 操作结果
   */
  async createRecord(recordData) {
    const defaultData = {
      userId: recordData.userId,
      userNumber: recordData.userNumber,
      type: recordData.type, // 'earn' | 'spend'
      amount: recordData.amount, // 正数为获得，负数为消费
      source: recordData.source, // 'check_in', 'purchase', 'admin', 'redeem'
      description: recordData.description,
      relatedId: recordData.relatedId || null,
      timestamp: new Date().toISOString(),
      ...recordData
    }

    return await this.create(defaultData)
  }

  /**
   * 获取用户积分记录
   * @param {string} userId - 用户ID
   * @param {Object} options - 查询选项
   * @returns {Promise<Object>} 积分记录
   */
  async getUserRecords(userId, options = {}) {
    try {
      const {
        limit = 20,
        page = 1,
        type = null, // 'earn' | 'spend' | null
        source = null,
        startDate = null,
        endDate = null
      } = options

      let query = { userId }

      // 添加类型筛选
      if (type) {
        query.type = type
      }

      // 添加来源筛选
      if (source) {
        query.source = source
      }

      // 添加时间范围筛选
      if (startDate && endDate) {
        query.timestamp = this.db.command.gte(startDate).and(this.db.command.lte(endDate))
      } else if (startDate) {
        query.timestamp = this.db.command.gte(startDate)
      } else if (endDate) {
        query.timestamp = this.db.command.lte(endDate)
      }

      const skip = (page - 1) * limit

      const result = await this.find(query, {
        orderBy: { field: 'timestamp', order: 'desc' },
        limit,
        skip
      })

      return result
    } catch (error) {
      console.error('获取用户积分记录失败:', error)
      return {
        success: false,
        message: error.message || '获取用户积分记录失败'
      }
    }
  }

  /**
   * 获取积分统计
   * @param {string} userId - 用户ID
   * @param {Object} options - 统计选项
   * @returns {Promise<Object>} 统计结果
   */
  async getPointsStats(userId, options = {}) {
    try {
      const { period = '30d' } = options
      
      // 计算时间范围
      const now = new Date()
      let startDate = null
      
      if (period === '7d') {
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
      } else if (period === '30d') {
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
      } else if (period === '90d') {
        startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000)
      }

      let query = { userId }
      if (startDate) {
        query.timestamp = this.db.command.gte(startDate.toISOString())
      }

      // 获取所有记录
      const allRecordsResult = await this.find(query)
      
      if (!allRecordsResult.success) {
        return allRecordsResult
      }

      const records = allRecordsResult.data || []
      
      // 计算统计数据
      let totalEarned = 0
      let totalSpent = 0
      let earnCount = 0
      let spendCount = 0

      records.forEach(record => {
        if (record.type === 'earn') {
          totalEarned += Math.abs(record.amount)
          earnCount++
        } else if (record.type === 'spend') {
          totalSpent += Math.abs(record.amount)
          spendCount++
        }
      })

      return {
        success: true,
        data: {
          totalEarned,
          totalSpent,
          netPoints: totalEarned - totalSpent,
          earnCount,
          spendCount,
          totalTransactions: records.length,
          period
        }
      }
    } catch (error) {
      console.error('获取积分统计失败:', error)
      return {
        success: false,
        message: error.message || '获取积分统计失败'
      }
    }
  }

  /**
   * 获取积分排行榜
   * @param {Object} options - 查询选项
   * @returns {Promise<Object>} 排行榜数据
   */
  async getPointsLeaderboard(options = {}) {
    try {
      const { limit = 50, period = '30d' } = options
      
      // 计算时间范围
      const now = new Date()
      let startDate = null
      
      if (period === '7d') {
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
      } else if (period === '30d') {
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
      } else if (period === '90d') {
        startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000)
      }

      let query = {}
      if (startDate) {
        query.timestamp = this.db.command.gte(startDate.toISOString())
      }

      // 获取所有记录
      const allRecordsResult = await this.find(query)
      
      if (!allRecordsResult.success) {
        return allRecordsResult
      }

      const records = allRecordsResult.data || []
      
      // 按用户聚合数据
      const userStats = {}
      
      records.forEach(record => {
        const userId = record.userId
        if (!userStats[userId]) {
          userStats[userId] = {
            userId,
            userNumber: record.userNumber,
            totalEarned: 0,
            totalSpent: 0,
            earnCount: 0,
            spendCount: 0
          }
        }
        
        if (record.type === 'earn') {
          userStats[userId].totalEarned += Math.abs(record.amount)
          userStats[userId].earnCount++
        } else if (record.type === 'spend') {
          userStats[userId].totalSpent += Math.abs(record.amount)
          userStats[userId].spendCount++
        }
      })

      // 转换为数组并排序
      const leaderboard = Object.values(userStats)
        .map(user => ({
          ...user,
          netPoints: user.totalEarned - user.totalSpent,
          totalTransactions: user.earnCount + user.spendCount
        }))
        .sort((a, b) => b.netPoints - a.netPoints)
        .slice(0, limit)

      return {
        success: true,
        data: leaderboard
      }
    } catch (error) {
      console.error('获取积分排行榜失败:', error)
      return {
        success: false,
        message: error.message || '获取积分排行榜失败'
      }
    }
  }

  /**
   * 获取积分统计（管理端）
   * @param {string} period - 统计周期
   * @returns {Promise<Object>} 统计结果
   */
  async getPointsStatsAdmin(period = '30d') {
    try {
      console.log(`[PointsRecordsDB] 获取积分统计: ${period}`)

      // 计算时间范围
      const now = new Date()
      let startDate = null

      switch (period) {
        case '7d':
          startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
          break
        case '30d':
          startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
          break
        case '90d':
          startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000)
          break
        case '1y':
          startDate = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000)
          break
        case 'all':
        default:
          startDate = null
          break
      }

      let query = {}
      if (startDate) {
        query.timestamp = { $gte: startDate.toISOString() }
      }

      // 获取所有记录
      const allRecordsResult = await this.findMany(query)

      if (!allRecordsResult.success) {
        return allRecordsResult
      }

      const records = allRecordsResult.data || []

      // 计算统计数据
      let totalEarned = 0
      let totalSpent = 0
      let earnCount = 0
      let spendCount = 0

      records.forEach(record => {
        if (record.type === 'earn') {
          totalEarned += Math.abs(record.amount)
          earnCount++
        } else if (record.type === 'spend') {
          totalSpent += Math.abs(record.amount)
          spendCount++
        }
      })

      const stats = {
        totalPoints: totalEarned - totalSpent,
        totalEarned,
        totalSpent,
        earnCount,
        spendCount,
        totalTransactions: records.length,
        period
      }

      return {
        success: true,
        data: stats
      }
    } catch (error) {
      console.error('[PointsRecordsDB] 获取积分统计失败:', error)
      return {
        success: false,
        message: error.message
      }
    }
  }

  /**
   * 按日期范围获取积分统计
   * @param {Date} startDate - 开始日期
   * @param {Date} endDate - 结束日期
   * @returns {Promise<Object>} 统计结果
   */
  async getPointsStatsByDateRange(startDate, endDate) {
    try {
      console.log(`[PointsRecordsDB] 按日期范围获取积分统计: ${startDate.toISOString()} - ${endDate.toISOString()}`)

      const query = {
        timestamp: {
          $gte: startDate.toISOString(),
          $lt: endDate.toISOString()
        }
      }

      // 获取所有记录
      const allRecordsResult = await this.findMany(query)

      if (!allRecordsResult.success) {
        return allRecordsResult
      }

      const records = allRecordsResult.data || []

      // 计算统计数据
      let totalEarned = 0
      let totalSpent = 0
      let earnCount = 0
      let spendCount = 0

      records.forEach(record => {
        if (record.type === 'earn') {
          totalEarned += Math.abs(record.amount)
          earnCount++
        } else if (record.type === 'spend') {
          totalSpent += Math.abs(record.amount)
          spendCount++
        }
      })

      const stats = {
        totalPoints: totalEarned - totalSpent,
        totalEarned,
        totalSpent,
        earnCount,
        spendCount,
        totalTransactions: records.length
      }

      return {
        success: true,
        data: stats
      }
    } catch (error) {
      console.error('[PointsRecordsDB] 按日期范围获取积分统计失败:', error)
      return {
        success: false,
        message: error.message
      }
    }
  }

  /**
   * 删除用户积分记录
   * @param {string} userId - 用户ID
   * @returns {Promise<Object>} 删除结果
   */
  async deleteUserRecords(userId) {
    try {
      return await this.delete({ userId })
    } catch (error) {
      console.error('删除用户积分记录失败:', error)
      return {
        success: false,
        message: error.message || '删除用户积分记录失败'
      }
    }
  }
}

module.exports = new PointsRecordsDB()
