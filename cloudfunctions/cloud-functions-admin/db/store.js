/**
 * 商店数据库操作类（管理端）
 */

const BaseDB = require('./base')

class StoreDB extends BaseDB {
  constructor() {
    super('store-items')
  }

  /**
   * 创建商品
   * @param {Object} itemData - 商品数据
   * @returns {Promise<Object>} 创建结果
   */
  async createStoreItem(itemData) {
    try {
      console.log('[StoreDB] 创建商品:', itemData)
      
      const data = {
        ...itemData,
        status: itemData.status || 'active',
        stock: itemData.stock || 0,
        soldCount: 0,
        isVisible: itemData.isVisible !== false
      }

      const result = await this.create(data)
      
      if (result.success) {
        console.log('[StoreDB] 商品创建成功:', result.data._id)
      }
      
      return result
    } catch (error) {
      console.error('[StoreDB] 创建商品失败:', error)
      return { 
        success: false, 
        message: error.message 
      }
    }
  }

  /**
   * 更新商品
   * @param {string} id - 商品ID
   * @param {Object} updateData - 更新数据
   * @returns {Promise<Object>} 更新结果
   */
  async updateStoreItem(id, updateData) {
    try {
      console.log(`[StoreDB] 更新商品: ${id}`)
      
      const result = await this.update({ _id: id }, updateData)
      
      if (result.success) {
        console.log(`[StoreDB] 商品更新成功: ${id}`)
      }
      
      return result
    } catch (error) {
      console.error(`[StoreDB] 更新商品失败: ${id}`, error)
      return { 
        success: false, 
        message: error.message 
      }
    }
  }

  /**
   * 删除商品
   * @param {string} id - 商品ID
   * @returns {Promise<Object>} 删除结果
   */
  async deleteStoreItem(id) {
    try {
      console.log(`[StoreDB] 删除商品: ${id}`)
      
      const result = await this.delete({ _id: id })
      
      if (result.success) {
        console.log(`[StoreDB] 商品删除成功: ${id}`)
      }
      
      return result
    } catch (error) {
      console.error(`[StoreDB] 删除商品失败: ${id}`, error)
      return { 
        success: false, 
        message: error.message 
      }
    }
  }

  /**
   * 获取商品列表（管理端）
   * @param {Object} options - 查询选项
   * @returns {Promise<Object>} 查询结果
   */
  async getStoreItemListAdmin(options = {}) {
    try {
      const {
        category = 'all',
        status = 'all',
        keyword = '',
        sortBy = 'createTime',
        sortOrder = 'desc',
        limit = 20,
        skip = 0
      } = options

      console.log(`[StoreDB] 管理端获取商品列表: category=${category}, status=${status}, keyword=${keyword}`)

      // 构建查询条件
      const where = {}
      
      if (category !== 'all') {
        where.category = category
      }
      
      if (status !== 'all') {
        where.status = status
      }
      
      if (keyword) {
        where.$or = [
          { name: new RegExp(keyword, 'i') },
          { description: new RegExp(keyword, 'i') }
        ]
      }

      // 查询总数
      const countResult = await this.count(where)
      const total = countResult.success ? countResult.data : 0

      // 查询数据
      const dataResult = await this.find(where, {
        limit,
        skip,
        orderBy: sortBy,
        order: sortOrder
      })

      if (!dataResult.success) {
        return dataResult
      }

      console.log(`[StoreDB] 管理端商品查询成功，共 ${dataResult.data.length} 条记录，总计 ${total} 条`)

      return {
        success: true,
        data: {
          list: dataResult.data,
          total,
          hasMore: skip + dataResult.data.length < total
        }
      }
    } catch (error) {
      console.error('[StoreDB] 管理端获取商品列表失败:', error)
      return { 
        success: false, 
        message: error.message 
      }
    }
  }

  /**
   * 获取商店统计信息（管理端）
   * @param {string} period - 统计周期
   * @returns {Promise<Object>} 统计结果
   */
  async getStoreStatsAdmin(period = '30d') {
    try {
      console.log(`[StoreDB] 获取商店统计信息: ${period}`)

      // 计算时间范围
      let startDate = null
      const now = new Date()
      
      switch (period) {
        case '7d':
          startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
          break
        case '30d':
          startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
          break
        case '90d':
          startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000)
          break
        case '1y':
          startDate = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000)
          break
        case 'all':
        default:
          startDate = null
          break
      }

      const baseWhere = {}
      if (startDate) {
        baseWhere.createTime = { $gte: startDate }
      }

      // 总商品数
      const totalResult = await this.count(baseWhere)
      const total = totalResult.success ? totalResult.data : 0

      // 活跃商品数
      const activeResult = await this.count({ ...baseWhere, status: 'active' })
      const active = activeResult.success ? activeResult.data : 0

      const stats = {
        total,
        active,
        inactive: total - active,
        period,
        generatedAt: new Date().toISOString()
      }

      console.log('[StoreDB] 商店统计信息获取成功')

      return {
        success: true,
        data: stats
      }
    } catch (error) {
      console.error('[StoreDB] 获取商店统计失败:', error)
      return { 
        success: false, 
        message: error.message 
      }
    }
  }
}

// 兑换码数据库操作类
class RedemptionCodeDB extends BaseDB {
  constructor() {
    super('redemption_codes')
  }

  /**
   * 创建兑换码
   * @param {Object} codeData - 兑换码数据
   * @returns {Promise<Object>} 创建结果
   */
  async createRedemptionCode(codeData) {
    try {
      console.log('[RedemptionCodeDB] 创建兑换码:', codeData)
      
      const data = {
        ...codeData,
        status: 'active',
        usedCount: 0,
        isUsed: false
      }

      const result = await this.create(data)
      
      if (result.success) {
        console.log('[RedemptionCodeDB] 兑换码创建成功:', result.data._id)
      }
      
      return result
    } catch (error) {
      console.error('[RedemptionCodeDB] 创建兑换码失败:', error)
      return { 
        success: false, 
        message: error.message 
      }
    }
  }

  /**
   * 获取兑换码列表（管理端）
   * @param {Object} options - 查询选项
   * @returns {Promise<Object>} 查询结果
   */
  async getRedemptionCodeListAdmin(options = {}) {
    try {
      const {
        status = 'all',
        type = 'all',
        keyword = '',
        sortBy = 'createTime',
        sortOrder = 'desc',
        limit = 20,
        skip = 0
      } = options

      console.log(`[RedemptionCodeDB] 管理端获取兑换码列表: status=${status}, type=${type}`)

      // 构建查询条件
      const where = {}
      
      if (status !== 'all') {
        where.status = status
      }
      
      if (type !== 'all') {
        where.type = type
      }
      
      if (keyword) {
        where.$or = [
          { code: new RegExp(keyword, 'i') },
          { description: new RegExp(keyword, 'i') }
        ]
      }

      // 查询总数
      const countResult = await this.count(where)
      const total = countResult.success ? countResult.data : 0

      // 查询数据
      const dataResult = await this.find(where, {
        limit,
        skip,
        orderBy: sortBy,
        order: sortOrder
      })

      if (!dataResult.success) {
        return dataResult
      }

      console.log(`[RedemptionCodeDB] 管理端兑换码查询成功，共 ${dataResult.data.length} 条记录，总计 ${total} 条`)

      return {
        success: true,
        data: {
          list: dataResult.data,
          total,
          hasMore: skip + dataResult.data.length < total
        }
      }
    } catch (error) {
      console.error('[RedemptionCodeDB] 管理端获取兑换码列表失败:', error)
      return { 
        success: false, 
        message: error.message 
      }
    }
  }
}

module.exports = {
  storeDB: new StoreDB(),
  redemptionCodeDB: new RedemptionCodeDB()
}
