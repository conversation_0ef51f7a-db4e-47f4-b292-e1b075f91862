/**
 * 配置数据库操作类（管理端）
 */

const BaseDB = require('./base')

class ConfigDB extends BaseDB {
  constructor() {
    super('config')
  }

  /**
   * 创建配置
   * @param {Object} configData - 配置数据
   * @returns {Promise<Object>} 操作结果
   */
  async createConfig(configData) {
    try {
      console.log('[ConfigDB] 创建配置:', configData)
      
      const data = {
        ...configData,
        enable: configData.enable !== false,
        category: configData.category || 'general',
        dataType: configData.dataType || 'string',
        description: configData.description || ''
      }

      const result = await this.create(data)
      
      if (result.success) {
        console.log('[ConfigDB] 配置创建成功:', result.data._id)
      }
      
      return result
    } catch (error) {
      console.error('[ConfigDB] 创建配置失败:', error)
      return { 
        success: false, 
        message: error.message 
      }
    }
  }

  /**
   * 更新配置
   * @param {string} key - 配置键
   * @param {Object} updateData - 更新数据
   * @returns {Promise<Object>} 操作结果
   */
  async updateConfig(key, updateData) {
    try {
      console.log(`[ConfigDB] 更新配置: ${key}`)
      
      const result = await this.update({ key }, updateData)
      
      if (result.success) {
        console.log(`[ConfigDB] 配置更新成功: ${key}`)
      }
      
      return result
    } catch (error) {
      console.error(`[ConfigDB] 更新配置失败: ${key}`, error)
      return { 
        success: false, 
        message: error.message 
      }
    }
  }

  /**
   * 删除配置
   * @param {string} key - 配置键
   * @returns {Promise<Object>} 操作结果
   */
  async deleteConfig(key) {
    try {
      console.log(`[ConfigDB] 删除配置: ${key}`)
      
      const result = await this.delete({ key })
      
      if (result.success) {
        console.log(`[ConfigDB] 配置删除成功: ${key}`)
      }
      
      return result
    } catch (error) {
      console.error(`[ConfigDB] 删除配置失败: ${key}`, error)
      return { 
        success: false, 
        message: error.message 
      }
    }
  }

  /**
   * 获取所有配置（管理端）
   * @param {Object} options - 查询选项
   * @returns {Promise<Object>} 操作结果
   */
  async getAllConfigsAdmin(options = {}) {
    try {
      const {
        category = 'all',
        enable = 'all',
        keyword = '',
        sortBy = 'key',
        sortOrder = 'asc',
        limit = 50,
        skip = 0
      } = options

      console.log(`[ConfigDB] 管理端获取配置列表: category=${category}, enable=${enable}, keyword=${keyword}`)

      // 构建查询条件
      const where = {}
      
      if (category !== 'all') {
        where.category = category
      }
      
      if (enable !== 'all') {
        where.enable = enable === 'true' || enable === true
      }
      
      if (keyword) {
        where.$or = [
          { key: new RegExp(keyword, 'i') },
          { description: new RegExp(keyword, 'i') }
        ]
      }

      // 查询总数
      const countResult = await this.count(where)
      const total = countResult.success ? countResult.data : 0

      // 查询数据
      const dataResult = await this.find(where, {
        limit,
        skip,
        orderBy: sortBy,
        order: sortOrder
      })

      if (!dataResult.success) {
        return dataResult
      }

      console.log(`[ConfigDB] 管理端配置查询成功，共 ${dataResult.data.length} 条记录，总计 ${total} 条`)

      return {
        success: true,
        data: {
          list: dataResult.data,
          total,
          hasMore: skip + dataResult.data.length < total
        }
      }
    } catch (error) {
      console.error('[ConfigDB] 管理端获取配置列表失败:', error)
      return { 
        success: false, 
        message: error.message 
      }
    }
  }

  /**
   * 获取配置统计信息（管理端）
   * @returns {Promise<Object>} 操作结果
   */
  async getConfigStats() {
    try {
      console.log('[ConfigDB] 获取配置统计信息')

      // 总配置数
      const totalResult = await this.count()
      const total = totalResult.success ? totalResult.data : 0

      // 启用的配置数
      const enabledResult = await this.count({ enable: true })
      const enabled = enabledResult.success ? enabledResult.data : 0

      const stats = {
        total,
        enabled,
        disabled: total - enabled,
        generatedAt: new Date().toISOString()
      }

      console.log('[ConfigDB] 配置统计信息获取成功')

      return {
        success: true,
        data: stats
      }
    } catch (error) {
      console.error('[ConfigDB] 获取配置统计失败:', error)
      return { 
        success: false, 
        message: error.message 
      }
    }
  }
}

module.exports = new ConfigDB()
