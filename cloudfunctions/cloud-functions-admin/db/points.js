/**
 * 积分数据库操作类（管理端）
 */

const BaseDB = require('./base')

class PointsDB extends BaseDB {
  constructor() {
    super('points-records')
  }

  /**
   * 获取积分统计信息（管理端）
   * @param {string} period - 统计周期
   * @returns {Promise<Object>} 统计结果
   */
  async getPointsStatsAdmin(period = '30d') {
    try {
      console.log(`[PointsDB] 获取积分统计信息: ${period}`)

      // 计算时间范围
      let startDate = null
      const now = new Date()
      
      switch (period) {
        case '7d':
          startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
          break
        case '30d':
          startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
          break
        case '90d':
          startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000)
          break
        case '1y':
          startDate = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000)
          break
        case 'all':
        default:
          startDate = null
          break
      }

      const baseWhere = {}
      if (startDate) {
        baseWhere.createTime = { $gte: startDate }
      }

      // 总积分记录数
      const totalResult = await this.count(baseWhere)
      const total = totalResult.success ? totalResult.data : 0

      // 模拟积分统计（实际应该通过聚合查询计算）
      const stats = {
        total,
        totalPoints: total * 10, // 模拟总积分
        earnedPoints: total * 8, // 模拟获得积分
        spentPoints: total * 2,  // 模拟消费积分
        period,
        generatedAt: new Date().toISOString()
      }

      console.log('[PointsDB] 积分统计信息获取成功')

      return {
        success: true,
        data: stats
      }
    } catch (error) {
      console.error('[PointsDB] 获取积分统计失败:', error)
      return { 
        success: false, 
        message: error.message 
      }
    }
  }

  /**
   * 手动调整用户积分
   * @param {Object} adjustData - 调整数据
   * @returns {Promise<Object>} 调整结果
   */
  async adjustUserPoints(adjustData) {
    try {
      const { userId, points, reason, adminId, type } = adjustData
      
      console.log(`[PointsDB] 调整用户积分: userId=${userId}, points=${points}`)

      // 创建积分记录
      const recordData = {
        userId,
        type: type || (points > 0 ? 'admin_add' : 'admin_deduct'),
        amount: points,
        source: 'admin',
        description: reason,
        adminId,
        relatedId: adminId
      }

      const result = await this.create(recordData)
      
      if (result.success) {
        console.log(`[PointsDB] 用户积分调整成功: ${userId}`)
      }

      return result
    } catch (error) {
      console.error('[PointsDB] 调整用户积分失败:', error)
      return { 
        success: false, 
        message: error.message 
      }
    }
  }

  /**
   * 获取积分记录（管理端）
   * @param {Object} options - 查询选项
   * @returns {Promise<Object>} 查询结果
   */
  async getPointsRecordsAdmin(options = {}) {
    try {
      const {
        userId = 'all',
        type = 'all',
        startDate,
        endDate,
        sortBy = 'createTime',
        sortOrder = 'desc',
        limit = 20,
        skip = 0
      } = options

      console.log(`[PointsDB] 管理端获取积分记录: userId=${userId}, type=${type}`)

      // 构建查询条件
      const where = {}
      
      if (userId !== 'all') {
        where.userId = userId
      }
      
      if (type !== 'all') {
        where.type = type
      }
      
      if (startDate || endDate) {
        where.createTime = {}
        if (startDate) {
          where.createTime.$gte = startDate
        }
        if (endDate) {
          where.createTime.$lte = endDate
        }
      }

      // 查询总数
      const countResult = await this.count(where)
      const total = countResult.success ? countResult.data : 0

      // 查询数据
      const dataResult = await this.find(where, {
        limit,
        skip,
        orderBy: sortBy,
        order: sortOrder
      })

      if (!dataResult.success) {
        return dataResult
      }

      console.log(`[PointsDB] 管理端积分记录查询成功，共 ${dataResult.data.length} 条记录，总计 ${total} 条`)

      return {
        success: true,
        data: {
          list: dataResult.data,
          total,
          hasMore: skip + dataResult.data.length < total
        }
      }
    } catch (error) {
      console.error('[PointsDB] 管理端获取积分记录失败:', error)
      return { 
        success: false, 
        message: error.message 
      }
    }
  }

  /**
   * 导出积分数据（管理端）
   * @param {Object} options - 导出选项
   * @returns {Promise<Object>} 导出结果
   */
  async exportPointsDataAdmin(options = {}) {
    try {
      const {
        userId = 'all',
        type = 'all',
        startDate,
        endDate,
        limit = 1000
      } = options

      console.log(`[PointsDB] 导出积分数据: userId=${userId}, type=${type}, limit=${limit}`)

      // 构建查询条件
      const where = {}
      
      if (userId !== 'all') {
        where.userId = userId
      }
      
      if (type !== 'all') {
        where.type = type
      }
      
      if (startDate || endDate) {
        where.createTime = {}
        if (startDate) {
          where.createTime.$gte = startDate
        }
        if (endDate) {
          where.createTime.$lte = endDate
        }
      }

      // 查询数据
      const result = await this.find(where, {
        orderBy: 'createTime',
        order: 'desc',
        limit
      })

      if (!result.success) {
        return result
      }

      console.log(`[PointsDB] 积分数据导出成功，共 ${result.data.length} 条记录`)

      return {
        success: true,
        data: result.data
      }
    } catch (error) {
      console.error('[PointsDB] 导出积分数据失败:', error)
      return { 
        success: false, 
        message: error.message 
      }
    }
  }

  /**
   * 获取积分排行榜
   * @param {string} period - 统计周期
   * @param {number} limit - 排行榜数量
   * @returns {Promise<Object>} 排行榜结果
   */
  async getPointsLeaderboard(period, limit) {
    try {
      console.log(`[PointsDB] 获取积分排行榜: period=${period}, limit=${limit}`)

      // 这里应该实现真实的排行榜逻辑
      // 暂时返回模拟数据
      const leaderboard = []
      for (let i = 1; i <= Math.min(limit, 10); i++) {
        leaderboard.push({
          rank: i,
          userId: `user_${i}`,
          username: `用户${i}`,
          totalPoints: 1000 - i * 50,
          period
        })
      }

      return {
        success: true,
        data: leaderboard
      }
    } catch (error) {
      console.error('[PointsDB] 获取积分排行榜失败:', error)
      return { 
        success: false, 
        message: error.message 
      }
    }
  }

  /**
   * 批量调整积分
   * @param {Array} adjustments - 调整列表
   * @param {string} reason - 调整原因
   * @param {string} adminId - 管理员ID
   * @returns {Promise<Object>} 调整结果
   */
  async batchAdjustPoints(adjustments, reason, adminId) {
    try {
      console.log(`[PointsDB] 批量调整积分: count=${adjustments.length}`)

      const promises = adjustments.map(adjustment => 
        this.adjustUserPoints({
          ...adjustment,
          reason,
          adminId
        })
      )
      
      const results = await Promise.allSettled(promises)
      
      const successCount = results.filter(r => r.status === 'fulfilled' && r.value.success).length
      const failedCount = results.filter(r => r.status === 'rejected' || !r.value.success).length

      console.log(`[PointsDB] 批量调整完成: 成功=${successCount}, 失败=${failedCount}`)

      return {
        success: true,
        data: {
          total: adjustments.length,
          success: successCount,
          failed: failedCount,
          results
        }
      }
    } catch (error) {
      console.error('[PointsDB] 批量调整积分失败:', error)
      return { 
        success: false, 
        message: error.message 
      }
    }
  }

  /**
   * 获取积分配置
   * @returns {Promise<Object>} 配置结果
   */
  async getPointsConfig() {
    try {
      // 这里应该从配置表中获取积分相关配置
      // 暂时返回默认配置
      const config = {
        dailyCheckIn: 10,
        shareReward: 5,
        inviteReward: 50,
        maxDailyEarn: 100,
        exchangeRate: 1
      }

      return {
        success: true,
        data: config
      }
    } catch (error) {
      console.error('[PointsDB] 获取积分配置失败:', error)
      return { 
        success: false, 
        message: error.message 
      }
    }
  }

  /**
   * 更新积分配置
   * @param {Object} config - 配置数据
   * @returns {Promise<Object>} 更新结果
   */
  async updatePointsConfig(config) {
    try {
      console.log('[PointsDB] 更新积分配置:', config)

      // 这里应该更新配置表
      // 暂时返回成功
      return {
        success: true,
        data: config
      }
    } catch (error) {
      console.error('[PointsDB] 更新积分配置失败:', error)
      return { 
        success: false, 
        message: error.message 
      }
    }
  }
}

module.exports = new PointsDB()
