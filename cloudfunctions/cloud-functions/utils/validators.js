/**
 * 参数验证工具函数
 */

/**
 * 验证必需参数
 * @param {Object} params - 参数对象
 * @param {Array} required - 必需参数列表
 * @throws {Error} 参数验证失败时抛出错误
 */
function validateRequired(params, required) {
  for (const param of required) {
    if (params[param] === undefined || params[param] === null || params[param] === '') {
      const err = new Error(`缺少必需参数: ${param}`)
      err.name = 'ValidationError'
      err.path = param
      throw err
    }
  }
}

/**
 * 验证参数类型
 * @param {*} value - 参数值
 * @param {string} type - 期望类型
 * @param {string} paramName - 参数名称
 * @throws {Error} 类型验证失败时抛出错误
 */
function validateType(value, type, paramName) {
  const actualType = typeof value
  
  if (type === 'array' && !Array.isArray(value)) {
    const err = new Error(`参数类型错误，期望数组`)
    err.name = 'ValidationError'
    err.path = paramName
    throw err
  }
  
  if (type !== 'array' && actualType !== type) {
    const err = new Error(`参数类型错误，期望${type}，实际${actualType}`)
    err.name = 'ValidationError'
    err.path = paramName
    throw err
  }
}

/**
 * 验证字符串长度
 * @param {string} value - 字符串值
 * @param {number} maxLength - 最大长度
 * @param {string} paramName - 参数名称
 * @throws {Error} 长度验证失败时抛出错误
 */
function validateLength(value, maxLength, paramName) {
  if (typeof value === 'string' && value.length > maxLength) {
    const err = new Error(`参数长度超过限制: ${paramName} (最大${maxLength}字符)`)
    err.name = 'ValidationError'
    err.path = paramName
    throw err
  }
}

/**
 * 验证正则表达式
 * @param {string} value - 字符串值
 * @param {RegExp} pattern - 正则表达式
 * @param {string} paramName - 参数名称
 * @throws {Error} 格式验证失败时抛出错误
 */
function validatePattern(value, pattern, paramName) {
  if (typeof value === 'string' && !pattern.test(value)) {
    const err = new Error(`参数格式不正确: ${paramName}`)
    err.name = 'ValidationError'
    err.path = paramName
    throw err
  }
}

/**
 * 验证数值范围
 * @param {number} value - 数值
 * @param {number} min - 最小值
 * @param {number} max - 最大值
 * @param {string} paramName - 参数名称
 * @throws {Error} 范围验证失败时抛出错误
 */
function validateRange(value, min, max, paramName) {
  if (typeof value === 'number') {
    if (value < min || value > max) {
      const err = new Error(`参数超出范围: ${paramName} (${min}-${max})`)
      err.name = 'ValidationError'
      err.path = paramName
      throw err
    }
  }
}

module.exports = {
  validateRequired,
  validateType,
  validateLength,
  validatePattern,
  validateRange
}
