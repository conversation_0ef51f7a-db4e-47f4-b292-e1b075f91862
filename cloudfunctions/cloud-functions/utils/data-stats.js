/**
 * 数据统计工具函数
 */

/**
 * 计算用户数据统计
 * @param {Object} userData - 用户数据
 * @returns {Object} 统计结果
 */
function calculateUserDataStats(userData) {
  const stats = {
    workHistoryCount: 0,
    timeSegmentCount: 0,
    fishingRecordCount: 0,
    incomeAdjustmentCount: 0
  }

  if (!userData || !userData.workHistory) {
    console.log('calculateUserDataStats: 无用户数据或工作履历')
    return stats
  }

  // 统计工作履历数量
  stats.workHistoryCount = Object.keys(userData.workHistory).length

  // 遍历每个工作履历，统计时间段、摸鱼记录和收入调整
  Object.values(userData.workHistory).forEach(work => {
    if (work.timeTracking) {
      Object.values(work.timeTracking).forEach(dayData => {
        // 统计时间段数量
        if (dayData.segments && Array.isArray(dayData.segments)) {
          stats.timeSegmentCount += dayData.segments.length
        }

        // 统计摸鱼记录数量
        if (dayData.fishes && Array.isArray(dayData.fishes)) {
          stats.fishingRecordCount += dayData.fishes.length
        }

        // 统计收入调整记录数量（额外收入 + 扣款）
        if (dayData.extraIncomes && Array.isArray(dayData.extraIncomes)) {
          stats.incomeAdjustmentCount += dayData.extraIncomes.length
        }
        if (dayData.deductions && Array.isArray(dayData.deductions)) {
          stats.incomeAdjustmentCount += dayData.deductions.length
        }
      })
    }
  })

  console.log('calculateUserDataStats 结果:', stats)
  return stats
}

/**
 * 格式化统计信息为可读文本
 * @param {Object} stats - 统计数据
 * @returns {string} 格式化的统计文本
 */
function formatStatsText(stats) {
  if (!stats) {
    return '无统计信息'
  }
  
  const workCount = stats.workHistoryCount || 0
  const segmentCount = stats.timeSegmentCount || 0
  const fishingCount = stats.fishingRecordCount || 0
  const adjustmentCount = stats.incomeAdjustmentCount || 0
  
  let result = `${workCount}个履历, ${segmentCount}个时间段`
  if (fishingCount > 0 || adjustmentCount > 0) {
    result += `, ${fishingCount}个摸鱼, ${adjustmentCount}个调整`
  }
  
  return result
}

/**
 * 验证统计数据的有效性
 * @param {Object} stats - 统计数据
 * @returns {boolean} 是否有效
 */
function validateStats(stats) {
  if (!stats || typeof stats !== 'object') {
    return false
  }

  const requiredFields = ['workHistoryCount', 'timeSegmentCount', 'fishingRecordCount', 'incomeAdjustmentCount']
  return requiredFields.every(field => 
    typeof stats[field] === 'number' && stats[field] >= 0
  )
}

/**
 * 比较两个统计数据
 * @param {Object} stats1 - 统计数据1
 * @param {Object} stats2 - 统计数据2
 * @returns {Object} 比较结果
 */
function compareStats(stats1, stats2) {
  const comparison = {}
  
  const fields = ['workHistoryCount', 'timeSegmentCount', 'fishingRecordCount', 'incomeAdjustmentCount']
  
  fields.forEach(field => {
    const value1 = stats1?.[field] || 0
    const value2 = stats2?.[field] || 0
    comparison[field] = {
      local: value1,
      cloud: value2,
      diff: value1 - value2,
      equal: value1 === value2
    }
  })
  
  return comparison
}

module.exports = {
  calculateUserDataStats,
  formatStatsText,
  validateStats,
  compareStats
}
