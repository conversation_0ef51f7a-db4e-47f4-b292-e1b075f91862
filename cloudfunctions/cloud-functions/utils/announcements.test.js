/**
 * 公告工具函数测试
 * 用于验证工具函数的正确性
 */

const {
  formatPublishTime,
  getTypeText,
  getSupportedTypes,
  isValidType,
  getStatusText,
  isValidStatus,
  formatAnnouncementData,
  formatAnnouncementList
} = require('./announcements')

/**
 * 测试时间格式化函数
 */
function testFormatPublishTime() {
  console.log('=== 测试 formatPublishTime ===')
  
  const now = new Date()
  const today = new Date(now)
  const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000)
  const threeDaysAgo = new Date(now.getTime() - 3 * 24 * 60 * 60 * 1000)
  const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
  
  console.log('今天:', formatPublishTime(today))
  console.log('昨天:', formatPublishTime(yesterday))
  console.log('3天前:', formatPublishTime(threeDaysAgo))
  console.log('1周前:', formatPublishTime(oneWeekAgo))
}

/**
 * 测试类型文本函数
 */
function testGetTypeText() {
  console.log('\n=== 测试 getTypeText ===')
  
  const types = ['announcement', 'update', 'notice', 'maintenance', 'invalid']
  
  types.forEach(type => {
    console.log(`${type}: ${getTypeText(type)}`)
  })
}

/**
 * 测试类型验证函数
 */
function testTypeValidation() {
  console.log('\n=== 测试类型验证 ===')
  
  console.log('支持的类型:', getSupportedTypes())
  
  const testTypes = ['announcement', 'update', 'invalid', 'notice']
  testTypes.forEach(type => {
    console.log(`${type} 是否有效: ${isValidType(type)}`)
  })
}

/**
 * 测试状态相关函数
 */
function testStatusFunctions() {
  console.log('\n=== 测试状态函数 ===')
  
  const statuses = ['draft', 'published', 'archived', 'invalid']
  
  statuses.forEach(status => {
    console.log(`${status}: ${getStatusText(status)} (有效: ${isValidStatus(status)})`)
  })
}

/**
 * 测试数据格式化函数
 */
function testDataFormatting() {
  console.log('\n=== 测试数据格式化 ===')
  
  const mockAnnouncement = {
    _id: 'test-id',
    title: '测试公告',
    content: '这是一个测试公告',
    type: 'announcement',
    status: 'published',
    publishTime: new Date(),
    isSticky: true
  }
  
  console.log('原始数据:', mockAnnouncement)
  console.log('格式化后:', formatAnnouncementData(mockAnnouncement))
  
  const mockList = [mockAnnouncement, {
    ...mockAnnouncement,
    _id: 'test-id-2',
    title: '测试更新',
    type: 'update',
    isSticky: false
  }]
  
  console.log('\n批量格式化:')
  formatAnnouncementList(mockList).forEach((item, index) => {
    console.log(`${index + 1}. ${item.title} - ${item.typeText} - ${item.formattedTime}`)
  })
}

/**
 * 运行所有测试
 */
function runAllTests() {
  console.log('开始测试公告工具函数...\n')
  
  try {
    testFormatPublishTime()
    testGetTypeText()
    testTypeValidation()
    testStatusFunctions()
    testDataFormatting()
    
    console.log('\n✅ 所有测试完成！')
  } catch (error) {
    console.error('\n❌ 测试失败:', error)
  }
}

// 如果直接运行此文件，执行测试
if (require.main === module) {
  runAllTests()
}

module.exports = {
  testFormatPublishTime,
  testGetTypeText,
  testTypeValidation,
  testStatusFunctions,
  testDataFormatting,
  runAllTests
}
