/**
 * 响应格式化工具函数
 */

/**
 * 成功响应
 * @param {*} data - 响应数据
 * @param {string} message - 响应消息
 * @returns {Object} 格式化的成功响应
 */
function success(data = null, message = '操作成功') {
  return {
    success: true,
    message,
    data,
    timestamp: new Date().toISOString()
  }
}

/**
 * 失败响应
 * @param {string} message - 错误消息
 * @param {string} code - 错误代码
 * @param {*} data - 额外数据
 * @returns {Object} 格式化的失败响应
 */
function error(message = '操作失败', code = 'UNKNOWN_ERROR', data = null) {
  return {
    success: false,
    message,
    code,
    data,
    timestamp: new Date().toISOString()
  }
}

/**
 * 权限不足响应
 * @param {string} featureName - 功能名称
 * @param {string} requiredMembership - 所需会员等级
 * @returns {Object} 权限不足响应
 */
function permissionDenied(featureName = '此功能', requiredMembership = 'pro') {
  return {
    success: false,
    message: `${featureName}仅限${requiredMembership === 'pro' ? 'Pro会员' : '高级会员'}使用`,
    code: 'PERMISSION_DENIED',
    needUpgrade: true,
    membershipRequired: requiredMembership,
    timestamp: new Date().toISOString()
  }
}

/**
 * 参数错误响应
 * @param {string} paramName - 参数名称
 * @param {string} reason - 错误原因
 * @returns {Object} 参数错误响应
 */
function invalidParam(paramName, reason = '参数无效') {
  return {
    success: false,
    message: `参数错误: ${paramName} - ${reason}`,
    code: 'INVALID_PARAM',
    param: paramName,
    timestamp: new Date().toISOString()
  }
}

/**
 * 资源不存在响应
 * @param {string} resourceName - 资源名称
 * @returns {Object} 资源不存在响应
 */
function notFound(resourceName = '资源') {
  return {
    success: false,
    message: `${resourceName}不存在`,
    code: 'NOT_FOUND',
    timestamp: new Date().toISOString()
  }
}

/**
 * 服务器内部错误响应
 * @param {string} message - 错误消息
 * @returns {Object} 服务器错误响应
 */
function serverError(message = '服务器内部错误') {
  return {
    success: false,
    message: message,
    code: 'SERVER_ERROR',
    timestamp: new Date().toISOString()
  }
}

/**
 * 频率限制响应
 * @param {string} message - 限制消息
 * @param {Object} limitInfo - 限制信息
 * @returns {Object} 频率限制响应
 */
function rateLimited(message = 'API调用频率超限', limitInfo = {}) {
  return {
    success: false,
    message: message,
    code: 'RATE_LIMITED',
    limitInfo,
    timestamp: new Date().toISOString()
  }
}

/**
 * 分页响应
 * @param {Array} data - 数据列表
 * @param {number} total - 总数
 * @param {number} page - 当前页
 * @param {number} pageSize - 页大小
 * @param {string} message - 响应消息
 * @returns {Object} 分页响应
 */
function paginated(data = [], total = 0, page = 1, pageSize = 20, message = '查询成功') {
  return {
    success: true,
    message,
    data,
    pagination: {
      total,
      page,
      pageSize,
      totalPages: Math.ceil(total / pageSize),
      hasNext: page * pageSize < total,
      hasPrev: page > 1
    },
    timestamp: new Date().toISOString()
  }
}

module.exports = {
  success,
  error,
  permissionDenied,
  invalidParam,
  notFound,
  serverError,
  rateLimited,
  paginated
}
