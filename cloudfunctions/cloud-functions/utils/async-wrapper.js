/**
 * 异步函数包装器工具
 */

const { success, error, invalidParam, permissionDenied, notFound, serverError } = require('./response')

/**
 * 包装异步函数，统一错误处理
 * @param {Function} fn - 异步函数
 * @returns {Function} 包装后的函数
 */
function wrapAsync(fn) {
  return async (...args) => {
    try {
      const result = await fn(...args)
      
      // 如果结果已经是标准格式，直接返回
      if (result && typeof result === 'object' && 'success' in result) {
        return result
      }
      
      // 否则包装为成功响应
      return success(result)
    } catch (err) {
      console.error('API执行错误:', err)
      
      // 根据错误类型返回不同响应
      if (err.name === 'ValidationError') {
        return invalidParam(err.path, err.message)
      }
      
      if (err.name === 'PermissionError') {
        return permissionDenied(err.feature, err.required)
      }
      
      if (err.name === 'NotFoundError') {
        return notFound(err.resource)
      }
      
      return serverError(err.message || '未知错误')
    }
  }
}

/**
 * 包装异步函数，支持自定义错误处理
 * @param {Function} fn - 异步函数
 * @param {Object} options - 选项
 * @param {Function} options.errorHandler - 自定义错误处理函数
 * @param {boolean} options.logErrors - 是否记录错误日志
 * @returns {Function} 包装后的函数
 */
function wrapAsyncWithOptions(fn, options = {}) {
  const { errorHandler, logErrors = true } = options
  
  return async (...args) => {
    try {
      const result = await fn(...args)
      
      // 如果结果已经是标准格式，直接返回
      if (result && typeof result === 'object' && 'success' in result) {
        return result
      }
      
      // 否则包装为成功响应
      return success(result)
    } catch (err) {
      if (logErrors) {
        console.error('API执行错误:', err)
      }
      
      // 使用自定义错误处理器
      if (errorHandler && typeof errorHandler === 'function') {
        return errorHandler(err)
      }
      
      // 默认错误处理
      if (err.name === 'ValidationError') {
        return invalidParam(err.path, err.message)
      }
      
      if (err.name === 'PermissionError') {
        return permissionDenied(err.feature, err.required)
      }
      
      if (err.name === 'NotFoundError') {
        return notFound(err.resource)
      }
      
      return serverError(err.message || '未知错误')
    }
  }
}

module.exports = {
  wrapAsync,
  wrapAsyncWithOptions
}
