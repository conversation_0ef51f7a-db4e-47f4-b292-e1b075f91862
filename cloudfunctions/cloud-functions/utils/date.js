/**
 * 日期时间工具函数
 */

/**
 * 格式化日期
 * @param {Date|string} date - 日期对象或字符串
 * @param {string} format - 格式字符串，默认 'YYYY-MM-DD'
 * @returns {string} 格式化后的日期字符串
 */
function formatDate(date, format = 'YYYY-MM-DD') {
  if (!date) return ''
  
  const d = new Date(date)
  if (isNaN(d.getTime())) return ''
  
  const year = d.getFullYear()
  const month = String(d.getMonth() + 1).padStart(2, '0')
  const day = String(d.getDate()).padStart(2, '0')
  const hour = String(d.getHours()).padStart(2, '0')
  const minute = String(d.getMinutes()).padStart(2, '0')
  const second = String(d.getSeconds()).padStart(2, '0')
  
  return format
    .replace('YYYY', year)
    .replace('MM', month)
    .replace('DD', day)
    .replace('HH', hour)
    .replace('mm', minute)
    .replace('ss', second)
}

/**
 * 格式化时间
 * @param {Date|string} date - 日期对象或字符串
 * @param {string} format - 格式字符串，默认 'HH:mm:ss'
 * @returns {string} 格式化后的时间字符串
 */
function formatTime(date, format = 'HH:mm:ss') {
  return formatDate(date, format)
}

/**
 * 格式化日期时间
 * @param {Date|string} date - 日期对象或字符串
 * @param {string} format - 格式字符串，默认 'YYYY-MM-DD HH:mm:ss'
 * @returns {string} 格式化后的日期时间字符串
 */
function formatDateTime(date, format = 'YYYY-MM-DD HH:mm:ss') {
  return formatDate(date, format)
}

/**
 * 获取今天的日期字符串
 * @param {string} format - 格式字符串，默认 'YYYY-MM-DD'
 * @returns {string} 今天的日期字符串
 */
function getToday(format = 'YYYY-MM-DD') {
  return formatDate(new Date(), format)
}

/**
 * 获取昨天的日期字符串
 * @param {string} format - 格式字符串，默认 'YYYY-MM-DD'
 * @returns {string} 昨天的日期字符串
 */
function getYesterday(format = 'YYYY-MM-DD') {
  const yesterday = new Date()
  yesterday.setDate(yesterday.getDate() - 1)
  return formatDate(yesterday, format)
}

/**
 * 获取明天的日期字符串
 * @param {string} format - 格式字符串，默认 'YYYY-MM-DD'
 * @returns {string} 明天的日期字符串
 */
function getTomorrow(format = 'YYYY-MM-DD') {
  const tomorrow = new Date()
  tomorrow.setDate(tomorrow.getDate() + 1)
  return formatDate(tomorrow, format)
}

/**
 * 获取本周开始日期
 * @param {string} format - 格式字符串，默认 'YYYY-MM-DD'
 * @returns {string} 本周开始日期字符串
 */
function getWeekStart(format = 'YYYY-MM-DD') {
  const now = new Date()
  const dayOfWeek = now.getDay()
  const diff = now.getDate() - dayOfWeek + (dayOfWeek === 0 ? -6 : 1) // 周一为一周开始
  const weekStart = new Date(now.setDate(diff))
  return formatDate(weekStart, format)
}

/**
 * 获取本周结束日期
 * @param {string} format - 格式字符串，默认 'YYYY-MM-DD'
 * @returns {string} 本周结束日期字符串
 */
function getWeekEnd(format = 'YYYY-MM-DD') {
  const now = new Date()
  const dayOfWeek = now.getDay()
  const diff = now.getDate() - dayOfWeek + (dayOfWeek === 0 ? 0 : 7) // 周日为一周结束
  const weekEnd = new Date(now.setDate(diff))
  return formatDate(weekEnd, format)
}

/**
 * 获取本月开始日期
 * @param {string} format - 格式字符串，默认 'YYYY-MM-DD'
 * @returns {string} 本月开始日期字符串
 */
function getMonthStart(format = 'YYYY-MM-DD') {
  const now = new Date()
  const monthStart = new Date(now.getFullYear(), now.getMonth(), 1)
  return formatDate(monthStart, format)
}

/**
 * 获取本月结束日期
 * @param {string} format - 格式字符串，默认 'YYYY-MM-DD'
 * @returns {string} 本月结束日期字符串
 */
function getMonthEnd(format = 'YYYY-MM-DD') {
  const now = new Date()
  const monthEnd = new Date(now.getFullYear(), now.getMonth() + 1, 0)
  return formatDate(monthEnd, format)
}

/**
 * 计算两个日期之间的天数差
 * @param {Date|string} date1 - 开始日期
 * @param {Date|string} date2 - 结束日期
 * @returns {number} 天数差
 */
function daysBetween(date1, date2) {
  const d1 = new Date(date1)
  const d2 = new Date(date2)
  
  if (isNaN(d1.getTime()) || isNaN(d2.getTime())) {
    return 0
  }
  
  const timeDiff = Math.abs(d2.getTime() - d1.getTime())
  return Math.ceil(timeDiff / (1000 * 3600 * 24))
}

/**
 * 检查日期是否为今天
 * @param {Date|string} date - 要检查的日期
 * @returns {boolean} 是否为今天
 */
function isToday(date) {
  const d = new Date(date)
  const today = new Date()
  
  return d.getFullYear() === today.getFullYear() &&
         d.getMonth() === today.getMonth() &&
         d.getDate() === today.getDate()
}

/**
 * 检查日期是否为昨天
 * @param {Date|string} date - 要检查的日期
 * @returns {boolean} 是否为昨天
 */
function isYesterday(date) {
  const d = new Date(date)
  const yesterday = new Date()
  yesterday.setDate(yesterday.getDate() - 1)
  
  return d.getFullYear() === yesterday.getFullYear() &&
         d.getMonth() === yesterday.getMonth() &&
         d.getDate() === yesterday.getDate()
}

/**
 * 检查日期是否为本周
 * @param {Date|string} date - 要检查的日期
 * @returns {boolean} 是否为本周
 */
function isThisWeek(date) {
  const d = new Date(date)
  const weekStart = new Date(getWeekStart())
  const weekEnd = new Date(getWeekEnd())
  
  return d >= weekStart && d <= weekEnd
}

/**
 * 检查日期是否为本月
 * @param {Date|string} date - 要检查的日期
 * @returns {boolean} 是否为本月
 */
function isThisMonth(date) {
  const d = new Date(date)
  const now = new Date()
  
  return d.getFullYear() === now.getFullYear() &&
         d.getMonth() === now.getMonth()
}

/**
 * 获取相对时间描述
 * @param {Date|string} date - 日期
 * @returns {string} 相对时间描述
 */
function getRelativeTime(date) {
  const d = new Date(date)
  const now = new Date()
  const diff = now.getTime() - d.getTime()

  if (diff < 0) {
    return '未来时间'
  }

  const seconds = Math.floor(diff / 1000)
  const minutes = Math.floor(seconds / 60)
  const hours = Math.floor(minutes / 60)
  const days = Math.floor(hours / 24)

  if (days > 0) {
    if (isYesterday(date)) return '昨天'
    if (days < 7) return `${days}天前`
    if (days < 30) return `${Math.floor(days / 7)}周前`
    if (days < 365) return `${Math.floor(days / 30)}个月前`
    return `${Math.floor(days / 365)}年前`
  }

  if (hours > 0) return `${hours}小时前`
  if (minutes > 0) return `${minutes}分钟前`
  if (seconds > 30) return `${seconds}秒前`

  return '刚刚'
}

/**
 * 标准化时间为秒级精度
 * @param {Date} date - 日期对象
 * @returns {Date} 标准化的日期对象（秒级精度）
 */
function normalizeTime(date = new Date()) {
  const normalizedDate = new Date(date)
  normalizedDate.setMilliseconds(0)
  return normalizedDate
}

module.exports = {
  formatDate,
  formatTime,
  formatDateTime,
  getToday,
  getYesterday,
  getTomorrow,
  getWeekStart,
  getWeekEnd,
  getMonthStart,
  getMonthEnd,
  daysBetween,
  isToday,
  isYesterday,
  isThisWeek,
  isThisMonth,
  getRelativeTime,
  normalizeTime
}
