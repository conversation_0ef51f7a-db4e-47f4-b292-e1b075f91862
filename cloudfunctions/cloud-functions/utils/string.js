/**
 * 字符串工具函数
 */

/**
 * 将字符串中的 \n 替换为实际换行符
 * @param {*} str - 输入字符串
 * @returns {string} 处理后的字符串
 */
function replaceNWithNewline(str) {
  // 处理null或undefined的情况
  if (str == null) {
    return ''
  }

  // 确保输入是字符串类型，防止非字符串输入导致错误
  const stringValue = String(str)

  // 替换所有的\n为实际换行符
  return stringValue.replace(/\\n/g, '\n')
}

/**
 * 截断字符串到指定长度
 * @param {string} str - 输入字符串
 * @param {number} maxLength - 最大长度
 * @param {string} suffix - 截断后的后缀，默认为 '...'
 * @returns {string} 截断后的字符串
 */
function truncate(str, maxLength, suffix = '...') {
  if (typeof str !== 'string') {
    return String(str)
  }
  
  if (str.length <= maxLength) {
    return str
  }
  
  return str.substring(0, maxLength - suffix.length) + suffix
}

/**
 * 首字母大写
 * @param {string} str - 输入字符串
 * @returns {string} 首字母大写的字符串
 */
function capitalize(str) {
  if (typeof str !== 'string' || str.length === 0) {
    return str
  }
  
  return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase()
}

/**
 * 驼峰命名转换为短横线命名
 * @param {string} str - 驼峰命名字符串
 * @returns {string} 短横线命名字符串
 */
function camelToKebab(str) {
  if (typeof str !== 'string') {
    return str
  }
  
  return str.replace(/([a-z0-9])([A-Z])/g, '$1-$2').toLowerCase()
}

/**
 * 短横线命名转换为驼峰命名
 * @param {string} str - 短横线命名字符串
 * @returns {string} 驼峰命名字符串
 */
function kebabToCamel(str) {
  if (typeof str !== 'string') {
    return str
  }
  
  return str.replace(/-([a-z])/g, (match, letter) => letter.toUpperCase())
}

/**
 * 移除字符串中的HTML标签
 * @param {string} str - 包含HTML的字符串
 * @returns {string} 纯文本字符串
 */
function stripHtml(str) {
  if (typeof str !== 'string') {
    return str
  }
  
  return str.replace(/<[^>]*>/g, '')
}

/**
 * 生成随机字符串
 * @param {number} length - 字符串长度
 * @param {string} chars - 可用字符集，默认为字母数字
 * @returns {string} 随机字符串
 */
function randomString(length = 8, chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789') {
  let result = ''
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  return result
}

module.exports = {
  replaceNWithNewline,
  truncate,
  capitalize,
  camelToKebab,
  kebabToCamel,
  stripHtml,
  randomString
}
