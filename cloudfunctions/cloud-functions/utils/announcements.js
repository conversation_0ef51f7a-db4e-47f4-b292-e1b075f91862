/**
 * 公告相关工具函数
 */

/**
 * 格式化发布时间
 * @param {Date|string} publishTime - 发布时间
 * @returns {string} 格式化后的时间字符串
 */
function formatPublishTime(publishTime) {
  const date = new Date(publishTime)
  const now = new Date()
  const diffMs = now - date
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))

  if (diffDays === 0) {
    return '今天'
  } else if (diffDays === 1) {
    return '昨天'
  } else if (diffDays < 7) {
    return `${diffDays}天前`
  } else {
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }
}

/**
 * 获取类型文本
 * @param {string} type - 公告类型
 * @returns {string} 类型文本
 */
function getTypeText(type) {
  const typeMap = {
    'announcement': '📢 公告',
    'update': '🔄 更新',
    'notice': '📋 通知',
    'maintenance': '🔧 维护'
  }
  return typeMap[type] || '📢 公告'
}

/**
 * 获取所有支持的公告类型
 * @returns {Array} 公告类型列表
 */
function getSupportedTypes() {
  return [
    { value: 'announcement', label: '📢 公告' },
    { value: 'update', label: '🔄 更新' },
    { value: 'notice', label: '📋 通知' },
    { value: 'maintenance', label: '🔧 维护' }
  ]
}

/**
 * 验证公告类型是否有效
 * @param {string} type - 公告类型
 * @returns {boolean} 是否有效
 */
function isValidType(type) {
  const validTypes = ['announcement', 'update', 'notice', 'maintenance']
  return validTypes.includes(type)
}

/**
 * 获取公告状态文本
 * @param {string} status - 公告状态
 * @returns {string} 状态文本
 */
function getStatusText(status) {
  const statusMap = {
    'draft': '草稿',
    'published': '已发布',
    'archived': '已归档'
  }
  return statusMap[status] || '未知'
}

/**
 * 验证公告状态是否有效
 * @param {string} status - 公告状态
 * @returns {boolean} 是否有效
 */
function isValidStatus(status) {
  const validStatuses = ['draft', 'published', 'archived']
  return validStatuses.includes(status)
}

/**
 * 格式化公告数据（用于API返回）
 * @param {Object} announcement - 公告数据
 * @returns {Object} 格式化后的公告数据
 */
function formatAnnouncementData(announcement) {
  if (!announcement) return null

  return {
    ...announcement,
    formattedTime: formatPublishTime(announcement.publishTime),
    typeText: getTypeText(announcement.type),
    statusText: getStatusText(announcement.status)
  }
}

/**
 * 批量格式化公告数据
 * @param {Array} announcements - 公告数据列表
 * @returns {Array} 格式化后的公告数据列表
 */
function formatAnnouncementList(announcements) {
  if (!Array.isArray(announcements)) return []
  
  return announcements.map(formatAnnouncementData)
}

module.exports = {
  formatPublishTime,
  getTypeText,
  getSupportedTypes,
  isValidType,
  getStatusText,
  isValidStatus,
  formatAnnouncementData,
  formatAnnouncementList
}
