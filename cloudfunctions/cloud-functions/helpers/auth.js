/**
 * 认证相关辅助函数
 */

const cloud = require('wx-server-sdk')
const usersDB = require('../db/users')

/**
 * 获取当前用户的openid
 * @returns {string} 用户openid
 */
function getCurrentOpenid() {
  const wxContext = cloud.getWXContext()
  return wxContext.OPENID
}

/**
 * 获取当前用户的unionid
 * @returns {string} 用户unionid
 */
function getCurrentUnionid() {
  const wxContext = cloud.getWXContext()
  return wxContext.UNIONID
}

/**
 * 确保用户存在，不存在则创建
 * @param {Object} userInfo - 用户信息
 * @returns {Promise<Object>} 操作结果
 */
async function ensureUserExists(userInfo = {}) {
  try {
    const openid = getCurrentOpenid()
    if (!openid) {
      return {
        success: false,
        message: '无法获取用户标识'
      }
    }

    // 检查用户是否已存在
    const existingResult = await usersDB.findByOpenid(openid)
    if (existingResult.success && existingResult.data) {
      return {
        success: true,
        data: existingResult.data,
        isNewUser: false
      }
    }

    // 创建新用户
    const createResult = await usersDB.createUser({
      openid,
      version: userInfo.version
    })

    if (!createResult.success) {
      return createResult
    }

    return {
      success: true,
      data: createResult.data,
      isNewUser: true
    }
  } catch (error) {
    console.error('确保用户存在失败:', error)
    return {
      success: false,
      message: error.message || '用户操作失败'
    }
  }
}

/**
 * 验证VIP会员权限
 * @param {string} featureName - 功能名称
 * @param {string} openid - 用户openid，不传则使用当前用户
 * @returns {Promise<Object>} 验证结果
 */
async function validateVipPermission(featureName = '此功能', openid = null) {
  try {
    const { checkVipMembership } = require('./users')
    const isVipMember = await checkVipMembership(openid)

    if (!isVipMember) {
      return {
        success: false,
        message: `${featureName}仅限VIP会员使用`,
        needUpgrade: true,
        vipRequired: true
      }
    }

    return {
      success: true,
      message: '权限验证通过'
    }
  } catch (error) {
    console.error('验证VIP权限失败:', error)
    return {
      success: false,
      message: error.message || '权限验证失败'
    }
  }
}

/**
 * 检查用户是否为管理员
 * @param {string} openid - 用户openid，不传则使用当前用户
 * @returns {Promise<boolean>} 是否为管理员
 */
async function checkAdminPermission(openid = null) {
  try {
    const targetOpenid = openid || getCurrentOpenid()
    if (!targetOpenid) {
      return false
    }

    const userResult = await usersDB.findByOpenid(targetOpenid)
    if (!userResult.success || !userResult.data) {
      return false
    }

    return userResult.data.isAdmin === true
  } catch (error) {
    console.error('检查管理员权限失败:', error)
    return false
  }
}

/**
 * 验证管理员权限
 * @param {string} openid - 用户openid，不传则使用当前用户
 * @returns {Promise<Object>} 验证结果
 */
async function validateAdminPermission(openid = null) {
  try {
    const isAdmin = await checkAdminPermission(openid)

    if (!isAdmin) {
      return {
        success: false,
        message: '操作失败', // 不暴露权限不足信息
        needAdmin: true
      }
    }

    return {
      success: true,
      message: '管理员权限验证通过'
    }
  } catch (error) {
    console.error('验证管理员权限失败:', error)
    return {
      success: false,
      message: '操作失败'
    }
  }
}

module.exports = {
  getCurrentOpenid,
  getCurrentUnionid,
  ensureUserExists,
  validateVipPermission,
  checkAdminPermission,
  validateAdminPermission
}
