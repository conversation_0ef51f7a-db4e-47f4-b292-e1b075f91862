# 数据管理云函数测试

## 新增的API接口

### 1. getCloudDataStats - 获取云端数据统计

**请求格式：**
```json
{
  "type": "getCloudDataStats",
  "data": {},
  "version": "0.1.2"
}
```

**响应格式：**
```json
{
  "result": {
    "success": true,
    "message": "获取云端数据统计成功",
    "data": {
      "hasData": true,
      "lastModified": "2024-01-01T12:00:00.000Z",
      "stats": {
        "workHistoryCount": 3,
        "timeSegmentCount": 15,
        "fishingRecordCount": 5,
        "incomeAdjustmentCount": 2
      }
    }
  }
}
```

### 2. clearCloudData - 清空云端数据

**请求格式：**
```json
{
  "type": "clearCloudData",
  "data": {},
  "version": "0.1.2"
}
```

**响应格式：**
```json
{
  "result": {
    "success": true,
    "message": "云端数据清空成功",
    "data": null
  }
}
```

## 修改的API接口

### getHistoryData - 获取指定历史数据

现在支持两种查询方式：

**通过dataId查询：**
```json
{
  "type": "getHistoryData",
  "data": {
    "dataId": "历史数据记录ID"
  },
  "version": "0.1.2"
}
```

**通过timestamp查询：**
```json
{
  "type": "getHistoryData",
  "data": {
    "timestamp": 1704067200000
  },
  "version": "0.1.2"
}
```

## 测试步骤

### 1. 测试 getCloudDataStats

1. 确保用户已登录且有数据
2. 调用 getCloudDataStats API
3. 验证返回的统计数据是否正确

### 2. 测试 clearCloudData

1. 确保用户已登录且有数据
2. 调用 clearCloudData API
3. 验证数据是否被清空
4. 再次调用 getCloudDataInfo 确认无数据

### 3. 测试 getHistoryData

1. 上传一些测试数据
2. 通过 getHistoryDataList 获取历史数据列表
3. 使用 dataId 和 timestamp 两种方式调用 getHistoryData
4. 验证返回的数据是否正确

## 错误处理

### 常见错误情况

1. **用户未登录**
   - 错误码：401
   - 错误信息：用户未登录

2. **无数据**
   - getCloudDataStats 返回空统计
   - clearCloudData 正常执行
   - getHistoryData 返回数据不存在

3. **参数错误**
   - getHistoryData 缺少 dataId 和 timestamp
   - 返回参数错误信息

## 部署说明

1. 确保所有文件修改完成
2. 上传云函数代码
3. 测试各个接口功能
4. 更新小程序端调用代码

## 注意事项

1. clearCloudData 是软删除，实际删除用户的所有数据记录
2. getCloudDataStats 会计算所有工作履历的统计数据
3. getHistoryData 支持两种查询方式，提高了灵活性
4. 所有操作都有用户权限验证，确保数据安全
