# 软删除功能实现

## 概述

将云端数据清空功能从硬删除改为软删除，确保数据安全性和可恢复性。

## 实现内容

### 1. ✅ 基础数据库类扩展 (`db/base.js`)

#### 新增软删除方法
```javascript
// 软删除单个文档
async softDeleteById(id)

// 条件软删除文档
async softDelete(where)
```

#### 软删除机制
- 添加 `isDeleted: true` 标记
- 添加 `deletedAt` 时间戳
- 更新 `updateTime` 字段

#### 查询方法修改
```javascript
// find 方法自动过滤软删除数据
async find(where = {}, options = {})
// 新增 options.includeDeleted 参数控制是否包含软删除数据

// findOne 方法自动过滤软删除数据  
async findOne(where, options = {})
// 新增 options.includeDeleted 参数控制是否包含软删除数据
```

### 2. ✅ 用户数据删除修改 (`db/user-data.js`)

#### 删除方法改为软删除
```javascript
// 修改前：硬删除
async deleteUserData(userId) {
  return await this.delete({ userId })
}

// 修改后：软删除
async deleteUserData(userId) {
  return await this.softDelete({ userId })
}
```

### 3. ✅ 自动过滤机制

#### 受影响的查询方法
所有使用 `find` 和 `findOne` 的方法都会自动过滤软删除数据：

1. **`getUserData(userId)`** - 获取用户最新数据
2. **`getUserDataHistory(userId, options)`** - 获取历史数据列表
3. **`getHistoryDataByTimestamp(timestamp, userId)`** - 获取指定时间的历史数据
4. **`getHistoryDataById(dataId, userId)`** - 获取指定ID的历史数据

#### 查询条件自动添加
```javascript
// 自动添加的过滤条件
where = {
  ...where,
  isDeleted: db.command.neq(true) // 不等于 true，包括 undefined 和 false
}
```

## 数据结构变化

### 软删除标记字段
```javascript
{
  // 原有字段...
  isDeleted: true,           // 软删除标记
  deletedAt: "2024-01-01T12:00:00.000Z", // 删除时间
  updateTime: "2024-01-01T12:00:00.000Z"  // 更新时间
}
```

### 数据状态
- **正常数据**: `isDeleted` 字段不存在或为 `false`
- **软删除数据**: `isDeleted` 为 `true`，包含 `deletedAt` 时间戳

## API 行为变化

### 1. `clearCloudData` API
```javascript
// 修改前：真正删除数据
await userDataDB.deleteUserData(user._id)

// 修改后：软删除数据
await userDataDB.deleteUserData(user._id) // 内部调用 softDelete
```

### 2. 查询 API 自动过滤
- `getCloudDataInfo` - 不会返回软删除的数据
- `getCloudDataStats` - 不会统计软删除的数据
- `getHistoryDataList` - 不会包含软删除的历史数据
- `getHistoryData` - 不会返回软删除的历史数据

## 测试验证

### 1. 软删除功能测试
```javascript
// 1. 创建测试数据
const userData = { /* 测试数据 */ }
await userDataDB.saveUserData('test_user', userData)

// 2. 验证数据存在
const beforeDelete = await userDataDB.getUserData('test_user')
console.log('删除前:', beforeDelete.success) // 应该为 true

// 3. 执行软删除
await userDataDB.deleteUserData('test_user')

// 4. 验证数据被软删除（查询不到）
const afterDelete = await userDataDB.getUserData('test_user')
console.log('删除后:', afterDelete.success, afterDelete.data) // 应该为 true, null

// 5. 验证数据仍然存在（包含软删除）
const includeDeleted = await userDataDB.find({ userId: 'test_user' }, { includeDeleted: true })
console.log('包含软删除:', includeDeleted.data.length) // 应该 > 0
```

### 2. API 测试
```javascript
// 测试 clearCloudData API
{
  "type": "clearCloudData",
  "data": {},
  "version": "0.1.2"
}

// 预期结果：
// 1. 返回成功
// 2. 数据被软删除，不是真正删除
// 3. 后续查询不会返回该数据
```

### 3. 历史数据测试
```javascript
// 测试历史数据不包含软删除数据
{
  "type": "getHistoryDataList",
  "data": { "days": 7 },
  "version": "0.1.2"
}

// 预期结果：返回的历史数据列表不包含软删除的数据
```

## 数据恢复机制

### 手动恢复（管理员操作）
```javascript
// 恢复软删除的数据
async restoreUserData(userId) {
  return await this.collection.where({
    userId,
    isDeleted: true
  }).update({
    data: {
      isDeleted: false,
      restoredAt: new Date(),
      updateTime: new Date()
    }
  })
}
```

### 查询软删除数据
```javascript
// 查询所有软删除的数据
const deletedData = await userDataDB.find({ userId }, { includeDeleted: true })
const softDeletedItems = deletedData.data.filter(item => item.isDeleted)
```

## 注意事项

### 1. 向后兼容性
- 现有数据没有 `isDeleted` 字段，查询时会被正常返回
- 新的软删除数据会有 `isDeleted: true` 标记

### 2. 性能考虑
- 查询条件增加了 `isDeleted` 过滤，可能需要添加索引
- 建议在 `isDeleted` 字段上创建索引

### 3. 数据清理
- 软删除的数据会一直保留，需要定期清理策略
- 可以设置定时任务清理超过一定时间的软删除数据

### 4. 存储空间
- 软删除会增加存储空间使用
- 需要监控数据库大小

## 部署说明

### 1. 云函数部署
```bash
cd cloudfunctions/cloud-functions
npm run deploy
```

### 2. 数据库索引（可选）
```javascript
// 在云开发控制台中为 user-data 集合添加索引
{
  "isDeleted": 1,
  "userId": 1,
  "lastModified": -1
}
```

### 3. 验证部署
1. 测试 `clearCloudData` API
2. 验证数据被软删除而不是真正删除
3. 确认查询接口不返回软删除数据

## 安全性提升

### 1. 数据保护
- 防止意外删除重要数据
- 提供数据恢复能力
- 保留操作审计记录

### 2. 用户体验
- 清空操作更安全
- 可以提供数据恢复功能
- 减少用户数据丢失风险

### 3. 合规性
- 符合数据保护要求
- 提供数据删除记录
- 支持数据恢复需求

现在云端数据清空功能使用软删除，确保数据安全性和可恢复性！
