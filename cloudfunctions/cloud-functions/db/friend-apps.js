/**
 * 友情应用数据库操作
 */

const BaseDB = require('./base')
const { replaceNWithNewline } = require("../utils/string");

class FriendAppsDB extends BaseDB {
  constructor() {
    super('friend-apps')
  }

  /**
   * 获取可见的友情应用列表
   * @param {Object} options - 查询选项
   * @returns {Promise<Object>} 查询结果
   */
  async getVisibleApps(options = {}) {
    try {
      const queryOptions = {
        orderBy: { field: 'sortOrder', order: 'asc' },
        ...options
      }

      const result = await this.find({ isVisible: true }, queryOptions)
      
      if (!result.success) {
        return result
      }

      // 处理返回数据，确保字段完整
      const processedData = result.data.map(app => ({
        _id: app._id,
        appName: app.appName || '',
        description: replaceNWithNewline(app.description) || '',
        iconUrl: app.iconUrl || '',
        navigateParams: app.navigateParams || {},
        sortOrder: app.sortOrder || 0,
        createTime: app.createTime,
        updateTime: app.updateTime
      }))

      return {
        success: true,
        data: processedData,
        total: result.total
      }
    } catch (error) {
      console.error('获取友情应用列表失败:', error)
      return {
        success: false,
        message: error.message || '获取友情应用列表失败'
      }
    }
  }

  /**
   * 创建友情应用
   * @param {Object} appData - 应用数据
   * @returns {Promise<Object>} 创建结果
   */
  async createApp(appData) {
    try {
      // 验证必填字段
      if (!appData.appName) {
        return {
          success: false,
          message: '应用名称不能为空'
        }
      }

      if (!appData.navigateParams || !appData.navigateParams.appId) {
        return {
          success: false,
          message: '跳转参数中的appId不能为空'
        }
      }

      const defaultAppData = {
        isVisible: true,
        appName: appData.appName,
        description: appData.description || '',
        iconUrl: appData.iconUrl || '',
        navigateParams: {
          appId: appData.navigateParams.appId,
          path: appData.navigateParams.path || '',
          extraData: appData.navigateParams.extraData || {},
          envVersion: appData.navigateParams.envVersion || 'release'
        },
        sortOrder: appData.sortOrder || 0,
        ...appData
      }

      return await this.create(defaultAppData)
    } catch (error) {
      console.error('创建友情应用失败:', error)
      return {
        success: false,
        message: error.message || '创建友情应用失败'
      }
    }
  }

  /**
   * 更新友情应用
   * @param {string} id - 应用ID
   * @param {Object} appData - 更新的应用数据
   * @returns {Promise<Object>} 更新结果
   */
  async updateApp(id, appData) {
    try {
      // 过滤掉不允许更新的字段
      const allowedFields = [
        'isVisible', 'appName', 'description', 'iconUrl', 
        'navigateParams', 'sortOrder'
      ]
      
      const updateData = {}
      allowedFields.forEach(field => {
        if (appData.hasOwnProperty(field)) {
          updateData[field] = appData[field]
        }
      })

      if (Object.keys(updateData).length === 0) {
        return {
          success: false,
          message: '没有可更新的字段'
        }
      }

      return await this.updateById(id, updateData)
    } catch (error) {
      console.error('更新友情应用失败:', error)
      return {
        success: false,
        message: error.message || '更新友情应用失败'
      }
    }
  }

  /**
   * 删除友情应用
   * @param {string} id - 应用ID
   * @returns {Promise<Object>} 删除结果
   */
  async deleteApp(id) {
    try {
      return await this.deleteById(id)
    } catch (error) {
      console.error('删除友情应用失败:', error)
      return {
        success: false,
        message: error.message || '删除友情应用失败'
      }
    }
  }

  /**
   * 切换应用可见性
   * @param {string} id - 应用ID
   * @param {boolean} isVisible - 是否可见
   * @returns {Promise<Object>} 更新结果
   */
  async toggleVisibility(id, isVisible) {
    try {
      return await this.updateById(id, { isVisible })
    } catch (error) {
      console.error('切换应用可见性失败:', error)
      return {
        success: false,
        message: error.message || '切换应用可见性失败'
      }
    }
  }

  /**
   * 获取友情应用统计信息
   * @returns {Promise<Object>} 统计结果
   */
  async getStats() {
    try {
      const totalResult = await this.count()
      const visibleResult = await this.count({ isVisible: true })

      if (!totalResult.success || !visibleResult.success) {
        throw new Error('获取统计信息失败')
      }

      return {
        success: true,
        data: {
          total: totalResult.data.total,
          visible: visibleResult.data.total,
          hidden: totalResult.data.total - visibleResult.data.total
        }
      }
    } catch (error) {
      console.error('获取友情应用统计失败:', error)
      return {
        success: false,
        message: error.message || '获取友情应用统计失败'
      }
    }
  }
}

module.exports = new FriendAppsDB()
