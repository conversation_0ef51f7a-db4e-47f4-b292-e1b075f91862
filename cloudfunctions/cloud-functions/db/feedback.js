/**
 * 反馈意见数据库操作
 */

const BaseDB = require('./base')

class FeedbackDB extends BaseDB {
  constructor() {
    super('feedback')
  }

  /**
   * 创建反馈记录
   * @param {Object} feedbackData - 反馈数据
   * @returns {Promise<Object>} 创建结果
   */
  async createFeedback(feedbackData) {
    const defaultFeedbackData = {
      userId: feedbackData.userId,
      category: feedbackData.category, // 直接使用中文分类
      email: feedbackData.email || '',
      content: feedbackData.content,
      status: 'pending', // 默认状态：待处理
      reply: '',
      replyTime: null,
      isDeleted: false, // 软删除标记
      ...feedbackData
    }

    return await this.create(defaultFeedbackData)
  }

  /**
   * 根据用户ID获取反馈列表
   * @param {string} userId - 用户ID
   * @param {Object} options - 查询选项
   * @returns {Promise<Object>} 查询结果
   */
  async getFeedbacksByUserId(userId, options = {}) {
    const queryOptions = {
      orderBy: { field: 'createTime', order: 'desc' },
      limit: options.limit || 20,
      skip: options.skip || 0
    }

    // 只查询未删除的反馈
    return await this.find({ 
      userId, 
      isDeleted: false 
    }, queryOptions)
  }

  /**
   * 软删除反馈
   * @param {string} feedbackId - 反馈ID
   * @param {string} userId - 用户ID（确保只能删除自己的反馈）
   * @returns {Promise<Object>} 删除结果
   */
  async softDeleteFeedback(feedbackId, userId) {
    try {
      // 先查询确认是该用户的反馈
      const feedbackResult = await this.findById(feedbackId)
      
      if (!feedbackResult.success) {
        return {
          success: false,
          message: '反馈记录不存在'
        }
      }

      if (feedbackResult.data.userId !== userId) {
        return {
          success: false,
          message: '无权限删除此反馈'
        }
      }

      if (feedbackResult.data.isDeleted) {
        return {
          success: false,
          message: '反馈已被删除'
        }
      }

      // 执行软删除
      const result = await this.update(
        { _id: feedbackId, userId },
        { isDeleted: true }
      )

      return result
    } catch (error) {
      console.error('软删除反馈失败:', error)
      return {
        success: false,
        message: error.message || '删除反馈失败'
      }
    }
  }

  /**
   * 获取用户反馈统计
   * @param {string} userId - 用户ID
   * @returns {Promise<Object>} 统计结果
   */
  async getFeedbackStats(userId) {
    try {
      // 获取所有未删除的反馈
      const allFeedbackResult = await this.find({ 
        userId, 
        isDeleted: false 
      })

      if (!allFeedbackResult.success) {
        return {
          success: false,
          message: '获取统计数据失败'
        }
      }

      const feedbacks = allFeedbackResult.data
      const stats = {
        total: feedbacks.length,
        pending: feedbacks.filter(f => f.status === 'pending').length,
        replied: feedbacks.filter(f => f.status === 'replied').length,
        closed: feedbacks.filter(f => f.status === 'closed').length
      }

      return {
        success: true,
        data: stats
      }
    } catch (error) {
      console.error('获取反馈统计失败:', error)
      return {
        success: false,
        message: error.message || '获取统计数据失败'
      }
    }
  }

  /**
   * 根据ID获取反馈详情
   * @param {string} feedbackId - 反馈ID
   * @returns {Promise<Object>} 查询结果
   */
  async getFeedbackById(feedbackId) {
    return await this.findById(feedbackId)
  }

  /**
   * 管理员回复反馈（通过数据库直接操作）
   * @param {string} feedbackId - 反馈ID
   * @param {string} reply - 回复内容
   * @returns {Promise<Object>} 更新结果
   */
  async replyFeedback(feedbackId, reply) {
    const updateData = {
      reply: reply,
      replyTime: new Date(),
      status: 'replied'
    }

    return await this.update({ _id: feedbackId }, updateData)
  }

  /**
   * 关闭反馈（管理员操作）
   * @param {string} feedbackId - 反馈ID
   * @returns {Promise<Object>} 更新结果
   */
  async closeFeedback(feedbackId) {
    return await this.update({ _id: feedbackId }, { status: 'closed' })
  }

  /**
   * 获取反馈列表（管理端）
   * @param {Object} options - 查询选项
   * @returns {Promise<Object>} 查询结果
   */
  async getFeedbackListAdmin(options = {}) {
    try {
      const {
        status = 'all',
        type = 'all',
        keyword = '',
        startDate,
        endDate,
        sortBy = 'createTime',
        sortOrder = 'desc',
        limit = 20,
        skip = 0
      } = options

      console.log(`[FeedbackDB] 管理端获取反馈列表: status=${status}, type=${type}, keyword=${keyword}`)

      // 构建查询条件
      const where = { isDeleted: false }

      if (status !== 'all') {
        where.status = status
      }

      if (type !== 'all') {
        where.category = type
      }

      if (keyword) {
        where.$or = [
          { content: new RegExp(keyword, 'i') },
          { email: new RegExp(keyword, 'i') },
          { reply: new RegExp(keyword, 'i') }
        ]
      }

      if (startDate || endDate) {
        where.createTime = {}
        if (startDate) {
          where.createTime.$gte = startDate
        }
        if (endDate) {
          where.createTime.$lte = endDate
        }
      }

      // 查询总数
      const countResult = await this.collection.where(where).count()
      const total = countResult.total

      // 查询数据
      const dataResult = await this.collection
        .where(where)
        .orderBy(sortBy, sortOrder)
        .skip(skip)
        .limit(limit)
        .get()

      console.log(`[FeedbackDB] 管理端反馈查询成功，共 ${dataResult.data.length} 条记录，总计 ${total} 条`)

      return {
        success: true,
        data: {
          list: dataResult.data,
          total,
          hasMore: skip + dataResult.data.length < total
        }
      }
    } catch (error) {
      console.error('[FeedbackDB] 管理端获取反馈列表失败:', error)
      return {
        success: false,
        error: error.message
      }
    }
  }

  /**
   * 根据ID获取反馈详情
   * @param {string} id - 反馈ID
   * @returns {Promise<Object>} 查询结果
   */
  async getFeedbackById(id) {
    try {
      console.log(`[FeedbackDB] 获取反馈详情: ${id}`)

      const result = await this.collection.doc(id).get()

      if (result.data.length === 0) {
        return {
          success: false,
          message: '反馈不存在'
        }
      }

      return {
        success: true,
        data: result.data[0]
      }
    } catch (error) {
      console.error(`[FeedbackDB] 获取反馈详情失败: ${id}`, error)
      return {
        success: false,
        error: error.message
      }
    }
  }

  /**
   * 回复反馈
   * @param {string} id - 反馈ID
   * @param {Object} replyData - 回复数据
   * @returns {Promise<Object>} 更新结果
   */
  async replyFeedback(id, replyData) {
    try {
      console.log(`[FeedbackDB] 回复反馈: ${id}`)

      const updateData = {
        ...replyData,
        updateTime: new Date()
      }

      const result = await this.collection.doc(id).update(updateData)

      console.log(`[FeedbackDB] 反馈回复成功: ${id}`)

      return {
        success: true,
        data: result
      }
    } catch (error) {
      console.error(`[FeedbackDB] 回复反馈失败: ${id}`, error)
      return {
        success: false,
        error: error.message
      }
    }
  }

  /**
   * 更新反馈状态
   * @param {string} id - 反馈ID
   * @param {Object} updateData - 更新数据
   * @returns {Promise<Object>} 更新结果
   */
  async updateFeedbackStatus(id, updateData) {
    try {
      console.log(`[FeedbackDB] 更新反馈状态: ${id}`)

      const data = {
        ...updateData,
        updateTime: new Date()
      }

      const result = await this.collection.doc(id).update(data)

      console.log(`[FeedbackDB] 反馈状态更新成功: ${id}`)

      return {
        success: true,
        data: result
      }
    } catch (error) {
      console.error(`[FeedbackDB] 更新反馈状态失败: ${id}`, error)
      return {
        success: false,
        error: error.message
      }
    }
  }

  /**
   * 获取反馈统计信息（管理端）
   * @param {string} period - 统计周期
   * @returns {Promise<Object>} 统计结果
   */
  async getFeedbackStatsAdmin(period = '30d') {
    try {
      console.log(`[FeedbackDB] 获取反馈统计信息: ${period}`)

      // 计算时间范围
      let startDate = null
      const now = new Date()

      switch (period) {
        case '7d':
          startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
          break
        case '30d':
          startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
          break
        case '90d':
          startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000)
          break
        case '1y':
          startDate = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000)
          break
        case 'all':
        default:
          startDate = null
          break
      }

      const baseWhere = { isDeleted: false }
      if (startDate) {
        baseWhere.createTime = { $gte: startDate }
      }

      // 总反馈数
      const totalResult = await this.collection.where(baseWhere).count()
      const total = totalResult.total

      // 按状态统计
      const statusStats = await this.collection
        .aggregate()
        .match(baseWhere)
        .group({
          _id: '$status',
          count: { $sum: 1 }
        })
        .end()

      // 按分类统计
      const categoryStats = await this.collection
        .aggregate()
        .match(baseWhere)
        .group({
          _id: '$category',
          count: { $sum: 1 }
        })
        .end()

      // 回复率统计
      const repliedResult = await this.collection
        .where({ ...baseWhere, status: { $in: ['replied', 'resolved'] } })
        .count()
      const replied = repliedResult.total
      const replyRate = total > 0 ? (replied / total * 100).toFixed(2) : 0

      const stats = {
        total,
        replied,
        pending: total - replied,
        replyRate: parseFloat(replyRate),
        statusBreakdown: statusStats.list || [],
        categoryBreakdown: categoryStats.list || [],
        period,
        generatedAt: new Date().toISOString()
      }

      console.log('[FeedbackDB] 反馈统计信息获取成功')

      return {
        success: true,
        data: stats
      }
    } catch (error) {
      console.error('[FeedbackDB] 获取反馈统计失败:', error)
      return {
        success: false,
        error: error.message
      }
    }
  }

  /**
   * 导出反馈数据（管理端）
   * @param {Object} options - 导出选项
   * @returns {Promise<Object>} 导出结果
   */
  async exportFeedbackDataAdmin(options = {}) {
    try {
      const {
        status = 'all',
        type = 'all',
        startDate,
        endDate,
        limit = 1000
      } = options

      console.log(`[FeedbackDB] 导出反馈数据: status=${status}, type=${type}, limit=${limit}`)

      // 构建查询条件
      const where = { isDeleted: false }

      if (status !== 'all') {
        where.status = status
      }

      if (type !== 'all') {
        where.category = type
      }

      if (startDate || endDate) {
        where.createTime = {}
        if (startDate) {
          where.createTime.$gte = startDate
        }
        if (endDate) {
          where.createTime.$lte = endDate
        }
      }

      // 查询数据
      const result = await this.collection
        .where(where)
        .orderBy('createTime', 'desc')
        .limit(limit)
        .get()

      console.log(`[FeedbackDB] 反馈数据导出成功，共 ${result.data.length} 条记录`)

      return {
        success: true,
        data: result.data
      }
    } catch (error) {
      console.error('[FeedbackDB] 导出反馈数据失败:', error)
      return {
        success: false,
        error: error.message
      }
    }
  }

  /**
   * 批量操作反馈
   * @param {Array} ids - 反馈ID列表
   * @param {string} action - 操作类型
   * @param {Object} data - 操作数据
   * @returns {Promise<Object>} 操作结果
   */
  async batchOperateFeedback(ids, action, data = {}) {
    try {
      console.log(`[FeedbackDB] 批量操作反馈: action=${action}, count=${ids.length}`)

      let updateData = {}

      switch (action) {
        case 'updateStatus':
          if (!data.status) {
            throw new Error('缺少状态参数')
          }
          updateData = {
            status: data.status,
            updateTime: new Date()
          }
          break
        case 'delete':
          updateData = {
            isDeleted: true,
            deleteTime: new Date()
          }
          break
        case 'archive':
          updateData = {
            status: 'archived',
            updateTime: new Date()
          }
          break
        default:
          throw new Error('不支持的操作类型')
      }

      // 批量更新
      const promises = ids.map(id =>
        this.collection.doc(id).update(updateData)
      )

      const results = await Promise.allSettled(promises)

      const successCount = results.filter(r => r.status === 'fulfilled').length
      const failedCount = results.filter(r => r.status === 'rejected').length

      console.log(`[FeedbackDB] 批量操作完成: 成功=${successCount}, 失败=${failedCount}`)

      return {
        success: true,
        data: {
          total: ids.length,
          success: successCount,
          failed: failedCount,
          results
        }
      }
    } catch (error) {
      console.error('[FeedbackDB] 批量操作反馈失败:', error)
      return {
        success: false,
        error: error.message
      }
    }
  }
}

module.exports = new FeedbackDB()
