/**
 * 兑换码数据库操作
 */

const BaseDB = require('./base')

class RedemptionCodesDB extends BaseDB {
  constructor() {
    super('redemption-codes')
  }

  // 获取数据库实例
  getDB() {
    if (!this._db) {
      const cloud = require('wx-server-sdk')
      this._db = cloud.database()
    }
    return this._db
  }

  /**
   * 生成兑换码
   * @param {Object} codeData - 兑换码数据
   * @returns {Promise<Object>} 操作结果
   */
  async generateCode(codeData) {
    const code = this.generateRandomCode()

    const defaultData = {
      code: code,
      type: codeData.type || 'vip_days',
      value: codeData.value, // VIP天数
      pointsCost: codeData.pointsCost, // 积分成本
      createdBy: codeData.createdBy, // 创建者用户ID
      createdAt: new Date().toISOString(),
      status: 'active',
      usedBy: null,
      usedAt: null,
      ...codeData
    }

    const result = await this.create(defaultData)
    if (result.success) {
      result.data.code = code // 确保返回生成的兑换码
    }
    return result
  }

  /**
   * 生成随机兑换码
   * @returns {string} 兑换码
   */
  generateRandomCode() {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'
    let code = 'VIP-'
    
    for (let i = 0; i < 3; i++) {
      for (let j = 0; j < 4; j++) {
        code += chars.charAt(Math.floor(Math.random() * chars.length))
      }
      if (i < 2) code += '-'
    }
    
    return code
  }

  /**
   * 验证兑换码
   * @param {string} code - 兑换码
   * @returns {Promise<Object>} 验证结果
   */
  async validateCode(code) {
    try {
      const result = await this.findOne({ code })

      if (!result.success || !result.data) {
        return {
          success: false,
          message: '兑换码不存在'
        }
      }

      const codeData = result.data

      // 检查状态
      if (codeData.status !== 'active') {
        return {
          success: false,
          message: codeData.status === 'used' ? '兑换码已使用' : '兑换码已失效'
        }
      }

      return {
        success: true,
        data: codeData
      }
    } catch (error) {
      console.error('验证兑换码失败:', error)
      return {
        success: false,
        message: error.message || '验证兑换码失败'
      }
    }
  }

  /**
   * 使用兑换码
   * @param {string} code - 兑换码
   * @param {string} userId - 使用者用户ID
   * @returns {Promise<Object>} 使用结果
   */
  async useCode(code, userId) {
    try {
      // 先验证兑换码
      const validateResult = await this.validateCode(code)
      if (!validateResult.success) {
        return validateResult
      }

      const codeData = validateResult.data

      // 更新兑换码状态
      const updateResult = await this.updateById(codeData._id, {
        status: 'used',
        usedBy: userId,
        usedAt: new Date().toISOString()
      })

      if (!updateResult.success) {
        return {
          success: false,
          message: '使用兑换码失败'
        }
      }

      return {
        success: true,
        data: {
          ...codeData,
          status: 'used',
          usedBy: userId,
          usedAt: new Date().toISOString()
        }
      }
    } catch (error) {
      console.error('使用兑换码失败:', error)
      return {
        success: false,
        message: error.message || '使用兑换码失败'
      }
    }
  }

  /**
   * 获取用户创建的兑换码
   * @param {string} userId - 用户ID
   * @param {Object} options - 查询选项
   * @returns {Promise<Object>} 兑换码列表
   */
  async getUserCreatedCodes(userId, options = {}) {
    try {
      const {
        status = null, // 'active' | 'used' | 'expired' | null(全部)
        limit = 20,
        skip = 0
      } = options

      let query = { createdBy: userId }

      if (status) {
        query.status = status
      }

      const db = this.getDB()
      const result = await db.collection(this.collectionName)
        .where(query)
        .orderBy('createdAt', 'desc')
        .skip(skip)
        .limit(limit)
        .get()

      return {
        success: true,
        data: result.data || []
      }
    } catch (error) {
      console.error('获取用户创建的兑换码失败:', error)
      return {
        success: false,
        message: error.message || '获取兑换码失败'
      }
    }
  }

  /**
   * 获取用户使用的兑换码
   * @param {string} userId - 用户ID
   * @param {number} limit - 限制数量
   * @returns {Promise<Object>} 兑换码列表
   */
  async getUserUsedCodes(userId, limit = 20) {
    try {
      const db = this.getDB()
      const result = await db.collection(this.collectionName)
        .where({
          usedBy: userId,
          status: 'used'
        })
        .orderBy('usedAt', 'desc')
        .limit(limit)
        .get()

      return {
        success: true,
        data: result.data || []
      }
    } catch (error) {
      console.error('获取用户使用的兑换码失败:', error)
      return {
        success: false,
        message: error.message || '获取使用记录失败'
      }
    }
  }

  /**
   * 获取兑换码统计
   * @param {string} userId - 用户ID（可选）
   * @returns {Promise<Object>} 统计结果
   */
  async getCodeStats(userId = null) {
    try {
      const db = this.getDB()
      let query = {}
      if (userId) {
        query.createdBy = userId
      }

      const result = await db.collection(this.collectionName)
        .where(query)
        .get()

      const stats = {
        total: result.data.length,
        active: 0,
        used: 0,
        expired: 0
      }

      result.data.forEach(code => {
        stats[code.status]++
      })

      return {
        success: true,
        data: stats
      }
    } catch (error) {
      console.error('获取兑换码统计失败:', error)
      return {
        success: false,
        message: error.message || '获取统计失败'
      }
    }
  }

  /**
   * 获取用户兑换码数量（仅返回有效未使用的数量）
   * @param {string} userId - 用户ID
   * @returns {Promise<Object>} 数量结果
   */
  async getUserCodesCount(userId) {
    try {
      // 只统计active状态的兑换码
      const result = await this.count({
        createdBy: userId,
        status: 'active'
      })
      return {
        success: true,
        data: { count: result.data?.total || 0 }
      }
    } catch (error) {
      console.error('获取用户兑换码数量失败:', error)
      return {
        success: false,
        data: { count: 0 },
        message: error.message || '获取兑换码数量失败'
      }
    }
  }
}

module.exports = new RedemptionCodesDB()
