/**
 * 配置数据库操作层
 */

const cloud = require('wx-server-sdk')
const db = cloud.database()
const configCollection = db.collection('config')

class ConfigDB {
  /**
   * 获取所有启用的配置
   * @returns {Promise<Object>} 操作结果
   */
  async getAllConfigs() {
    try {
      console.log('[ConfigDB] 开始获取所有启用的配置')
      
      const result = await configCollection
        .where({ enable: true })
        .orderBy('key', 'asc')
        .get()
      
      console.log(`[ConfigDB] 获取配置成功，共 ${result.data.length} 条记录`)
      
      return {
        success: true,
        data: result.data
      }
    } catch (error) {
      console.error('[ConfigDB] 获取配置失败:', error)
      return { 
        success: false, 
        error: error.message 
      }
    }
  }

  /**
   * 获取单个配置
   * @param {string} key - 配置键
   * @returns {Promise<Object>} 操作结果
   */
  async getConfig(key) {
    try {
      console.log(`[ConfigDB] 获取配置: ${key}`)
      
      const result = await configCollection
        .where({ key, enable: true })
        .get()
      
      return {
        success: true,
        data: result.data.length > 0 ? result.data[0] : null
      }
    } catch (error) {
      console.error(`[ConfigDB] 获取配置失败: ${key}`, error)
      return { 
        success: false, 
        error: error.message 
      }
    }
  }

  /**
   * 更新配置值（管理员功能）
   * @param {string} key - 配置键
   * @param {any} value - 配置值
   * @returns {Promise<Object>} 操作结果
   */
  async updateConfig(key, value) {
    try {
      console.log(`[ConfigDB] 更新配置: ${key} = ${value}`)
      
      const result = await configCollection
        .where({ key })
        .update({
          data: {
            value,
            updatedAt: new Date()
          }
        })
      
      console.log(`[ConfigDB] 配置更新成功: ${key}`)
      
      return { 
        success: true, 
        data: result 
      }
    } catch (error) {
      console.error(`[ConfigDB] 更新配置失败: ${key}`, error)
      return { 
        success: false, 
        error: error.message 
      }
    }
  }

  /**
   * 创建配置（管理员功能）
   * @param {Object} configData - 配置数据
   * @returns {Promise<Object>} 操作结果
   */
  async createConfig(configData) {
    try {
      console.log('[ConfigDB] 创建配置:', configData.key)
      
      const data = {
        enable: configData.enable !== false, // 默认启用
        key: configData.key,
        value: configData.value,
        description: configData.description || '',
        updatedAt: new Date()
      }
      
      const result = await configCollection.add({
        data
      })
      
      console.log(`[ConfigDB] 配置创建成功: ${configData.key}`)
      
      return { 
        success: true, 
        data: result 
      }
    } catch (error) {
      console.error('[ConfigDB] 创建配置失败:', error)
      return { 
        success: false, 
        error: error.message 
      }
    }
  }

  /**
   * 删除配置（管理员功能）
   * @param {string} key - 配置键
   * @returns {Promise<Object>} 操作结果
   */
  async deleteConfig(key) {
    try {
      console.log(`[ConfigDB] 删除配置: ${key}`)
      
      const result = await configCollection
        .where({ key })
        .remove()
      
      console.log(`[ConfigDB] 配置删除成功: ${key}`)
      
      return { 
        success: true, 
        data: result 
      }
    } catch (error) {
      console.error(`[ConfigDB] 删除配置失败: ${key}`, error)
      return { 
        success: false, 
        error: error.message 
      }
    }
  }

  /**
   * 切换配置启用状态（管理员功能）
   * @param {string} key - 配置键
   * @param {boolean} enable - 是否启用
   * @returns {Promise<Object>} 操作结果
   */
  async toggleConfig(key, enable) {
    try {
      console.log(`[ConfigDB] 切换配置状态: ${key} = ${enable}`)
      
      const result = await configCollection
        .where({ key })
        .update({
          data: {
            enable,
            updatedAt: new Date()
          }
        })
      
      console.log(`[ConfigDB] 配置状态切换成功: ${key}`)
      
      return { 
        success: true, 
        data: result 
      }
    } catch (error) {
      console.error(`[ConfigDB] 切换配置状态失败: ${key}`, error)
      return { 
        success: false, 
        error: error.message 
      }
    }
  }

  /**
   * 获取所有配置（管理端）
   * @param {Object} options - 查询选项
   * @returns {Promise<Object>} 操作结果
   */
  async getAllConfigsAdmin(options = {}) {
    try {
      const {
        category = 'all',
        enable = 'all',
        keyword = '',
        sortBy = 'key',
        sortOrder = 'asc',
        limit = 50,
        skip = 0
      } = options

      console.log(`[ConfigDB] 管理端获取配置列表: category=${category}, enable=${enable}, keyword=${keyword}`)

      // 构建查询条件
      const where = {}

      if (category !== 'all') {
        where.category = category
      }

      if (enable !== 'all') {
        where.enable = enable === 'true' || enable === true
      }

      if (keyword) {
        // 支持按键名或描述搜索
        where.$or = [
          { key: new RegExp(keyword, 'i') },
          { description: new RegExp(keyword, 'i') }
        ]
      }

      // 查询总数
      const countResult = await configCollection.where(where).count()
      const total = countResult.total

      // 查询数据
      const dataResult = await configCollection
        .where(where)
        .orderBy(sortBy, sortOrder)
        .skip(skip)
        .limit(limit)
        .get()

      console.log(`[ConfigDB] 管理端配置查询成功，共 ${dataResult.data.length} 条记录，总计 ${total} 条`)

      return {
        success: true,
        data: {
          list: dataResult.data,
          total,
          hasMore: skip + dataResult.data.length < total
        }
      }
    } catch (error) {
      console.error('[ConfigDB] 管理端获取配置列表失败:', error)
      return {
        success: false,
        error: error.message
      }
    }
  }

  /**
   * 获取配置统计信息（管理端）
   * @returns {Promise<Object>} 操作结果
   */
  async getConfigStats() {
    try {
      console.log('[ConfigDB] 获取配置统计信息')

      // 总配置数
      const totalResult = await configCollection.count()
      const total = totalResult.total

      // 启用的配置数
      const enabledResult = await configCollection.where({ enable: true }).count()
      const enabled = enabledResult.total

      // 按分类统计
      const categoryStats = await configCollection
        .aggregate()
        .group({
          _id: '$category',
          count: { $sum: 1 }
        })
        .end()

      // 按数据类型统计
      const dataTypeStats = await configCollection
        .aggregate()
        .group({
          _id: '$dataType',
          count: { $sum: 1 }
        })
        .end()

      const stats = {
        total,
        enabled,
        disabled: total - enabled,
        categoryBreakdown: categoryStats.list || [],
        dataTypeBreakdown: dataTypeStats.list || [],
        generatedAt: new Date().toISOString()
      }

      console.log('[ConfigDB] 配置统计信息获取成功')

      return {
        success: true,
        data: stats
      }
    } catch (error) {
      console.error('[ConfigDB] 获取配置统计失败:', error)
      return {
        success: false,
        error: error.message
      }
    }
  }
}

module.exports = new ConfigDB()
