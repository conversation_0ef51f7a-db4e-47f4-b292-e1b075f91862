/**
 * 积分记录数据库操作
 */

const BaseDB = require('./base')

class PointsRecordsDB extends BaseDB {
  constructor() {
    super('points-records')
  }

  // 获取数据库实例
  getDB() {
    if (!this._db) {
      const cloud = require('wx-server-sdk')
      this._db = cloud.database()
    }
    return this._db
  }

  /**
   * 创建积分记录
   * @param {Object} recordData - 积分记录数据
   * @returns {Promise<Object>} 操作结果
   */
  async createRecord(recordData) {
    const defaultData = {
      userId: recordData.userId,
      userNumber: recordData.userNumber,
      type: recordData.type, // 'earn' | 'spend'
      amount: recordData.amount, // 正数为获得，负数为消费
      source: recordData.source, // 'check_in', 'purchase', 'admin', 'redeem'
      description: recordData.description,
      relatedId: recordData.relatedId || null,
      timestamp: new Date().toISOString(),
      ...recordData
    }

    return await this.create(defaultData)
  }

  /**
   * 获取用户积分记录
   * @param {string} userId - 用户ID
   * @param {Object} options - 查询选项
   * @returns {Promise<Object>} 积分记录
   */
  async getUserRecords(userId, options = {}) {
    try {
      const {
        type = null, // 'earn' | 'spend' | null(全部)
        startDate = null,
        endDate = null
      } = options

      let query = { userId }

      // 类型筛选
      if (type) {
        query.type = type
      }

      const db = this.getDB()

      // 日期筛选
      if (startDate || endDate) {
        query.timestamp = {}
        if (startDate) {
          query.timestamp[db.command.gte] = startDate
        }
        if (endDate) {
          query.timestamp[db.command.lte] = endDate
        }
      }

      const result = await db.collection(this.collectionName)
        .where(query)
        .orderBy('timestamp', 'desc')
        .get()

      return {
        success: true,
        data: result.data || []
      }
    } catch (error) {
      console.error('获取用户积分记录失败:', error)
      return {
        success: false,
        message: error.message || '获取积分记录失败'
      }
    }
  }

  /**
   * 获取用户积分统计
   * @param {string} userId - 用户ID
   * @returns {Promise<Object>} 积分统计
   */
  async getUserPointsStats(userId) {
    try {
      const db = this.getDB()

      // 获取总收入
      const earnResult = await db.collection(this.collectionName)
        .where({
          userId,
          type: 'earn'
        })
        .get()

      // 获取总支出
      const spendResult = await db.collection(this.collectionName)
        .where({
          userId,
          type: 'spend'
        })
        .get()

      const totalEarned = earnResult.data.reduce((sum, record) => sum + record.amount, 0)
      const totalSpent = spendResult.data.reduce((sum, record) => sum + Math.abs(record.amount), 0)
      const currentBalance = totalEarned - totalSpent

      return {
        success: true,
        data: {
          totalEarned,
          totalSpent,
          currentBalance,
          totalRecords: earnResult.data.length + spendResult.data.length
        }
      }
    } catch (error) {
      console.error('获取用户积分统计失败:', error)
      return {
        success: false,
        message: error.message || '获取积分统计失败'
      }
    }
  }

  /**
   * 获取积分来源统计
   * @param {string} userId - 用户ID
   * @returns {Promise<Object>} 来源统计
   */
  async getPointsSourceStats(userId) {
    try {
      const db = this.getDB()
      const result = await db.collection(this.collectionName)
        .where({
          userId,
          type: 'earn'
        })
        .get()

      const sourceStats = {}
      result.data.forEach(record => {
        const source = record.source
        if (!sourceStats[source]) {
          sourceStats[source] = {
            count: 0,
            totalPoints: 0
          }
        }
        sourceStats[source].count++
        sourceStats[source].totalPoints += record.amount
      })

      return {
        success: true,
        data: sourceStats
      }
    } catch (error) {
      console.error('获取积分来源统计失败:', error)
      return {
        success: false,
        message: error.message || '获取来源统计失败'
      }
    }
  }

  /**
   * 获取最近的积分记录
   * @param {string} userId - 用户ID
   * @param {number} limit - 限制数量
   * @returns {Promise<Object>} 最近记录
   */
  async getRecentRecords(userId, limit = 5) {
    try {
      const db = this.getDB()
      const result = await db.collection(this.collectionName)
        .where({ userId })
        .orderBy('timestamp', 'desc')
        .limit(limit)
        .get()

      return {
        success: true,
        data: result.data || []
      }
    } catch (error) {
      console.error('获取最近积分记录失败:', error)
      return {
        success: false,
        message: error.message || '获取最近记录失败'
      }
    }
  }

  /**
   * 按日期统计积分变化
   * @param {string} userId - 用户ID
   * @param {number} days - 天数
   * @returns {Promise<Object>} 日期统计
   */
  async getDailyPointsStats(userId, days = 30) {
    try {
      const db = this.getDB()
      const endDate = new Date()
      const startDate = new Date(endDate.getTime() - (days - 1) * 24 * 60 * 60 * 1000)

      const result = await db.collection(this.collectionName)
        .where({
          userId,
          timestamp: db.command.gte(startDate.toISOString()).and(db.command.lte(endDate.toISOString()))
        })
        .orderBy('timestamp', 'asc')
        .get()

      // 按日期分组统计
      const dailyStats = {}
      result.data.forEach(record => {
        const date = record.timestamp.split('T')[0]
        if (!dailyStats[date]) {
          dailyStats[date] = {
            earned: 0,
            spent: 0,
            net: 0
          }
        }
        
        if (record.type === 'earn') {
          dailyStats[date].earned += record.amount
        } else {
          dailyStats[date].spent += Math.abs(record.amount)
        }
        dailyStats[date].net = dailyStats[date].earned - dailyStats[date].spent
      })

      return {
        success: true,
        data: dailyStats
      }
    } catch (error) {
      console.error('获取日期积分统计失败:', error)
      return {
        success: false,
        message: error.message || '获取日期统计失败'
      }
    }
  }

  /**
   * 获取积分统计信息（管理端）
   * @param {string} period - 统计周期
   * @returns {Promise<Object>} 统计结果
   */
  async getPointsStatsAdmin(period = '30d') {
    try {
      console.log(`[PointsRecordsDB] 获取积分统计信息: ${period}`)

      // 计算时间范围
      let startDate = null
      const now = new Date()

      switch (period) {
        case '7d':
          startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
          break
        case '30d':
          startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
          break
        case '90d':
          startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000)
          break
        case '1y':
          startDate = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000)
          break
        case 'all':
        default:
          startDate = null
          break
      }

      const baseWhere = {}
      if (startDate) {
        baseWhere.createTime = { $gte: startDate }
      }

      // 总积分记录数
      const totalResult = await this.collection.where(baseWhere).count()
      const total = totalResult.total

      // 总积分数量
      const pointsAggregation = await this.collection
        .aggregate()
        .match(baseWhere)
        .group({
          _id: null,
          totalPoints: { $sum: '$amount' },
          earnedPoints: {
            $sum: {
              $cond: [{ $gt: ['$amount', 0] }, '$amount', 0]
            }
          },
          spentPoints: {
            $sum: {
              $cond: [{ $lt: ['$amount', 0] }, { $abs: '$amount' }, 0]
            }
          }
        })
        .end()

      const pointsData = pointsAggregation.list[0] || {
        totalPoints: 0,
        earnedPoints: 0,
        spentPoints: 0
      }

      // 按类型统计
      const typeStats = await this.collection
        .aggregate()
        .match(baseWhere)
        .group({
          _id: '$type',
          count: { $sum: 1 },
          totalAmount: { $sum: '$amount' }
        })
        .end()

      // 按来源统计
      const sourceStats = await this.collection
        .aggregate()
        .match(baseWhere)
        .group({
          _id: '$source',
          count: { $sum: 1 },
          totalAmount: { $sum: '$amount' }
        })
        .end()

      const stats = {
        total,
        totalPoints: pointsData.totalPoints,
        earnedPoints: pointsData.earnedPoints,
        spentPoints: pointsData.spentPoints,
        typeBreakdown: typeStats.list || [],
        sourceBreakdown: sourceStats.list || [],
        period,
        generatedAt: new Date().toISOString()
      }

      console.log('[PointsRecordsDB] 积分统计信息获取成功')

      return {
        success: true,
        data: stats
      }
    } catch (error) {
      console.error('[PointsRecordsDB] 获取积分统计失败:', error)
      return {
        success: false,
        error: error.message
      }
    }
  }

  /**
   * 手动调整用户积分
   * @param {Object} adjustData - 调整数据
   * @returns {Promise<Object>} 调整结果
   */
  async adjustUserPoints(adjustData) {
    try {
      const { userId, points, reason, adminId, type } = adjustData

      console.log(`[PointsRecordsDB] 调整用户积分: userId=${userId}, points=${points}`)

      // 创建积分记录
      const recordData = {
        userId,
        type,
        amount: points,
        source: 'admin',
        description: reason,
        adminId,
        relatedId: adminId
      }

      const result = await this.createRecord(recordData)

      if (!result.success) {
        throw new Error(result.message || '创建积分记录失败')
      }

      console.log(`[PointsRecordsDB] 用户积分调整成功: ${userId}`)

      return {
        success: true,
        data: result.data
      }
    } catch (error) {
      console.error('[PointsRecordsDB] 调整用户积分失败:', error)
      return {
        success: false,
        error: error.message
      }
    }
  }

  /**
   * 获取积分记录（管理端）
   * @param {Object} options - 查询选项
   * @returns {Promise<Object>} 查询结果
   */
  async getPointsRecordsAdmin(options = {}) {
    try {
      const {
        userId = 'all',
        type = 'all',
        startDate,
        endDate,
        sortBy = 'createTime',
        sortOrder = 'desc',
        limit = 20,
        skip = 0
      } = options

      console.log(`[PointsRecordsDB] 管理端获取积分记录: userId=${userId}, type=${type}`)

      // 构建查询条件
      const where = {}

      if (userId !== 'all') {
        where.userId = userId
      }

      if (type !== 'all') {
        where.type = type
      }

      if (startDate || endDate) {
        where.createTime = {}
        if (startDate) {
          where.createTime.$gte = startDate
        }
        if (endDate) {
          where.createTime.$lte = endDate
        }
      }

      // 查询总数
      const countResult = await this.collection.where(where).count()
      const total = countResult.total

      // 查询数据
      const dataResult = await this.collection
        .where(where)
        .orderBy(sortBy, sortOrder)
        .skip(skip)
        .limit(limit)
        .get()

      console.log(`[PointsRecordsDB] 管理端积分记录查询成功，共 ${dataResult.data.length} 条记录，总计 ${total} 条`)

      return {
        success: true,
        data: {
          list: dataResult.data,
          total,
          hasMore: skip + dataResult.data.length < total
        }
      }
    } catch (error) {
      console.error('[PointsRecordsDB] 管理端获取积分记录失败:', error)
      return {
        success: false,
        error: error.message
      }
    }
  }
}

module.exports = new PointsRecordsDB()
