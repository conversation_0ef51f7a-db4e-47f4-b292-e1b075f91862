# 用户端云函数 (cloud-functions)

面向小程序用户的云函数服务，提供用户功能相关的API接口。

## 📁 项目结构

```
cloud-functions/
├── api/                     # API模块
│   ├── announcements.js     # 公告相关API
│   ├── check-ins.js         # 签到相关API
│   ├── feedback.js          # 反馈相关API
│   ├── fishing-status.js    # 摸鱼状态API
│   ├── friend-apps.js       # 友情应用API
│   ├── points.js            # 积分相关API
│   ├── store.js             # 商店相关API
│   ├── user-data.js         # 用户数据API
│   └── users.js             # 用户相关API
├── db/                      # 数据库操作层
│   ├── base.js              # 基础数据库类
│   ├── cache.js             # 缓存操作
│   ├── check-ins.js         # 签到数据操作
│   ├── fishing-status.js    # 摸鱼状态数据
│   ├── friend-apps.js       # 友情应用数据
│   ├── index.js             # 数据库入口
│   ├── points-records.js    # 积分记录数据
│   ├── redemption-codes.js  # 兑换码数据
│   ├── store-items.js       # 商品数据
│   ├── user-data.js         # 用户数据存储
│   ├── users.js             # 用户基础数据
│   └── vip-records.js       # VIP记录数据
├── utils/                   # 工具函数
│   ├── date.js              # 日期处理工具
│   ├── response.js          # 响应格式化
│   └── string.js            # 字符串处理工具
└── index.js                 # 云函数入口
```

## 🔌 API接口列表

### 用户管理
- `getUserInfo`    - 获取用户信息
- `updateUserInfo` - 更新用户信息
- `getUserStats`   - 获取用户统计信息

### 公告系统
- `getAnnouncements`       - 获取公告列表
- `getAnnouncementDetail`  - 获取公告详情
- `markAnnouncementAsRead` - 标记公告已读

### 签到系统
- `checkIn`           - 用户签到
- `getCheckInStatus`  - 获取签到状态
- `getCheckInHistory` - 获取签到历史
- `getCheckInStats`   - 获取签到统计

### 积分系统
- `getUserPoints`    - 获取用户积分
- `getPointsHistory` - 获取积分历史
- `getPointsStats`   - 获取积分统计

### 商店系统
- `getStoreItems`        - 获取商品列表
- `purchaseItem`         - 购买商品
- `getMyRedemptionCodes` - 获取我的兑换码
- `redeemCode`           - 使用兑换码

### 反馈系统
- `submitFeedback`    - 提交反馈
- `getMyFeedback`     - 获取我的反馈
- `getFeedbackDetail` - 获取反馈详情

### 摸鱼功能
- `startFishingStatus`     - 开始摸鱼
- `endFishingStatus`       - 结束摸鱼
- `getCurrentFishingCount` - 获取当前摸鱼人数
- `getUserFishingStatus`   - 获取用户摸鱼状态

### 友情应用
- `getFriendApps` - 获取友情应用列表

### 用户数据
- `saveUserData` - 保存用户数据
- `getUserData`  - 获取用户数据

## 🔧 开发规范

### 1. API设计规范

#### 请求格式
```javascript
// 云函数调用
wx.cloud.callFunction({
  name: 'cloud-functions',
  data: {
    action: 'getUserInfo',  // API名称
    data: {                 // 请求参数
      userId: 'user123'
    }
  }
})
```

#### 响应格式
```javascript
// 成功响应
{
  success: true,
  message: '操作成功',
  data: {
    // 响应数据
  },
  timestamp: '2025-08-13T00:00:00.000Z'
}

// 错误响应
{
  success: false,
  message: '错误信息',
  code: 'ERROR_CODE',
  data: null,
  timestamp: '2025-08-13T00:00:00.000Z'
}
```

### 2. 错误处理规范

#### 错误码定义
- `INVALID_PARAMS`         - 参数错误
- `USER_NOT_FOUND`         - 用户不存在
- `INSUFFICIENT_POINTS`    - 积分不足
- `ALREADY_CHECKED_IN`     - 已经签到
- `ITEM_OUT_OF_STOCK`      - 商品缺货
- `CODE_NOT_FOUND`         - 兑换码不存在
- `CODE_EXPIRED`           - 兑换码已过期
- `FISHING_ALREADY_ACTIVE` - 已在摸鱼状态

#### 错误处理示例
```javascript
try {
  // 业务逻辑
  const result = await someOperation();
  return success(result, '操作成功');
} catch (error) {
  console.error('操作失败:', error);
  return error('操作失败', 'OPERATION_ERROR');
}
```

### 3. 数据验证规范

#### 参数验证
```javascript
function validateCheckInData(data) {
  if (!data.userId) {
    throw new Error('用户ID不能为空');
  }
  
  if (data.location) {
    if (typeof data.location.latitude !== 'number' || 
        typeof data.location.longitude !== 'number') {
      throw new Error('位置信息格式错误');
    }
  }
}
```

#### 数据清理
```javascript
function sanitizeUserInput(input) {
  if (typeof input !== 'string') return '';
  
  return input
    .trim()
    .replace(/[<>]/g, '') // 移除HTML标签
    .substring(0, 1000);  // 限制长度
}
```

### 4. 数据库操作规范

#### 查询优化
```javascript
// ✅ 好的做法 - 使用索引字段查询
const user = await db.collection('users').doc(userId).get();

// ✅ 好的做法 - 限制返回字段
const users = await db.collection('users')
  .field({ nickname: true, avatar: true })
  .limit(20)
  .get();

// ❌ 避免 - 全表扫描
const users = await db.collection('users')
  .where({ nickname: db.command.regex({ regexp: keyword }) })
  .get();
```

#### 事务处理
```javascript
// 积分消费事务示例
async function purchaseItem(userId, itemId, price) {
  const transaction = await db.startTransaction();
  
  try {
    // 1. 检查用户积分
    const user = await transaction.collection('users').doc(userId).get();
    if (user.data.points < price) {
      throw new Error('积分不足');
    }
    
    // 2. 扣除积分
    await transaction.collection('users').doc(userId).update({
      data: {
        points: db.command.inc(-price)
      }
    });
    
    // 3. 记录积分消费
    await transaction.collection('points-records').add({
      data: {
        userId,
        type: 'spend',
        amount: -price,
        source: 'purchase',
        description: `购买商品: ${itemId}`,
        timestamp: new Date().toISOString()
      }
    });
    
    await transaction.commit();
    return success(null, '购买成功');
    
  } catch (error) {
    await transaction.rollback();
    throw error;
  }
}
```

### 5. 缓存策略

#### 缓存使用
```javascript
const cacheDB = require('./db/cache');

async function getCachedData(key, fetchFunction, ttl = 3600) {
  // 尝试从缓存获取
  const cached = await cacheDB.get(key);
  if (cached.success && cached.data) {
    return cached.data;
  }
  
  // 缓存未命中，获取新数据
  const freshData = await fetchFunction();
  
  // 存入缓存
  const expireTime = new Date(Date.now() + ttl * 1000);
  await cacheDB.set(key, freshData, expireTime);
  
  return freshData;
}
```

### 6. 日志记录

#### 日志级别
```javascript
// 信息日志
console.log('[INFO] 用户签到成功:', { userId, date });

// 警告日志
console.warn('[WARN] 用户积分不足:', { userId, required, current });

// 错误日志
console.error('[ERROR] 数据库操作失败:', error);
```

## 🧪 测试规范

### 1. 单元测试
```javascript
// 测试签到功能
describe('签到功能', () => {
  test('正常签到', async () => {
    const result = await checkIn({ userId: 'test123' });
    expect(result.success).toBe(true);
  });
  
  test('重复签到', async () => {
    await checkIn({ userId: 'test123' });
    const result = await checkIn({ userId: 'test123' });
    expect(result.success).toBe(false);
    expect(result.code).toBe('ALREADY_CHECKED_IN');
  });
});
```

### 2. 集成测试
```javascript
// 测试完整的购买流程
test('商品购买流程', async () => {
  // 1. 获取用户积分
  const userInfo = await getUserInfo({ userId: 'test123' });
  const initialPoints = userInfo.data.points;
  
  // 2. 购买商品
  const purchaseResult = await purchaseItem({
    userId: 'test123',
    itemId: 'item456',
    price: 100
  });
  expect(purchaseResult.success).toBe(true);
  
  // 3. 验证积分扣除
  const updatedUserInfo = await getUserInfo({ userId: 'test123' });
  expect(updatedUserInfo.data.points).toBe(initialPoints - 100);
});
```

## 🚀 部署和监控

### 1. 部署流程
1. 代码提交到版本控制
2. 本地测试通过
3. 上传到云开发环境
4. 配置环境变量
5. 生产环境验证

### 2. 监控指标
- API调用次数和成功率
- 响应时间分布
- 错误率和错误类型
- 数据库查询性能
- 缓存命中率

### 3. 告警设置
- API错误率超过5%
- 响应时间超过3秒
- 数据库连接异常
- 缓存服务异常

## 📝 更新日志

### v1.0.0 (2025-08-13)
- 初始版本发布
- 完整的用户功能API
- 基础的缓存和监控
