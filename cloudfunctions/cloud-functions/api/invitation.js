/**
 * 邀请好友相关API
 */

const usersDB = require('../db/users')
const pointsRecordsDB = require('../db/points-records')
const { getCurrentUser } = require('../helpers/users')
const { success, error } = require('../utils/response')
const { wrapAsync } = require('../utils/async-wrapper')

// 邀请奖励配置
const INVITATION_REWARDS = {
  INVITER: 10, // 邀请人奖励积分
  INVITEE: 5   // 被邀请人奖励积分
}

/**
 * 处理邀请绑定
 * 当用户通过邀请链接进入小程序时调用
 */
exports.processInvitation = wrapAsync(async (params = {}) => {
  try {
    console.log('[INFO] 开始处理邀请绑定:', params)
    
    const { inviterUserId } = params
    
    if (!inviterUserId) {
      return error('邀请人信息无效')
    }

    // 获取当前用户
    const currentUserResult = await getCurrentUser()
    if (!currentUserResult.success) {
      return error(currentUserResult.message || '获取用户信息失败')
    }

    const currentUser = currentUserResult.data
    console.log('[INFO] 当前用户:', currentUser._id)

    // 检查是否为自己邀请自己
    if (currentUser._id === inviterUserId) {
      console.log('[WARN] 用户尝试邀请自己')
      return error('不能邀请自己')
    }

    // 检查当前用户是否已经被邀请过
    if (currentUser.invitation && currentUser.invitation.invitedBy) {
      console.log('[WARN] 用户已被邀请过:', currentUser.invitation.invitedBy)
      return error('您已经被其他用户邀请过了')
    }

    // 获取邀请人信息
    const inviterResult = await usersDB.findById(inviterUserId)
    if (!inviterResult.success || !inviterResult.data) {
      console.log('[ERROR] 邀请人不存在:', inviterUserId)
      return error('邀请人不存在')
    }

    const inviter = inviterResult.data
    console.log('[INFO] 邀请人信息:', inviter._id, inviter.nickname)

    // 更新被邀请人信息
    const inviteeUpdateData = {
      'invitation.invitedBy': inviterUserId,
      'invitation.invitedAt': new Date().toISOString(),
      'invitation.rewardClaimed': false
    }

    const updateInviteeResult = await usersDB.updateById(currentUser._id, inviteeUpdateData)
    if (!updateInviteeResult.success) {
      console.log('[ERROR] 更新被邀请人信息失败:', updateInviteeResult.message)
      return error('绑定邀请关系失败')
    }

    // 更新邀请人信息
    const newInvitedUsers = [...(inviter.invitation?.invitedUsers || []), currentUser._id]
    const inviterUpdateData = {
      'invitation.invitedUsers': newInvitedUsers,
      'invitation.totalInvited': newInvitedUsers.length
    }

    const updateInviterResult = await usersDB.updateById(inviterUserId, inviterUpdateData)
    if (!updateInviterResult.success) {
      console.log('[ERROR] 更新邀请人信息失败:', updateInviterResult.message)
      return error('更新邀请信息失败')
    }

    // 给邀请人发放积分奖励
    const inviterPointsResult = await addInvitationPoints(
      inviter.openid,
      INVITATION_REWARDS.INVITER,
      'invite_friend',
      `邀请好友 ${currentUser.nickname} 获得积分`,
      currentUser._id
    )

    // 给被邀请人发放积分奖励
    const inviteePointsResult = await addInvitationPoints(
      currentUser.openid,
      INVITATION_REWARDS.INVITEE,
      'invited_by_friend',
      `被 ${inviter.nickname} 邀请获得积分`,
      inviterUserId
    )

    console.log('[INFO] 邀请绑定成功，积分奖励发放结果:', {
      inviter: inviterPointsResult.success,
      invitee: inviteePointsResult.success
    })

    return success({
      inviter: {
        id: inviter._id,
        nickname: inviter.nickname,
        reward: INVITATION_REWARDS.INVITER
      },
      invitee: {
        id: currentUser._id,
        nickname: currentUser.nickname,
        reward: INVITATION_REWARDS.INVITEE
      },
      pointsRewards: {
        inviterSuccess: inviterPointsResult.success,
        inviteeSuccess: inviteePointsResult.success
      }
    }, '邀请绑定成功！双方都获得了积分奖励')

  } catch (error) {
    console.error('[ERROR] 处理邀请绑定失败:', error)
    return error(error.message || '处理邀请失败')
  }
})

/**
 * 获取邀请统计信息
 */
exports.getInvitationStats = wrapAsync(async (params = {}) => {
  try {
    console.log('[INFO] 获取邀请统计信息')

    // 获取当前用户
    const currentUserResult = await getCurrentUser()
    if (!currentUserResult.success) {
      return error(currentUserResult.message || '获取用户信息失败')
    }

    const currentUser = currentUserResult.data
    const invitation = currentUser.invitation || {}

    // 获取被邀请用户的详细信息
    let invitedUsersDetails = []
    if (invitation.invitedUsers && invitation.invitedUsers.length > 0) {
      for (const userId of invitation.invitedUsers) {
        const userResult = await usersDB.findById(userId)
        if (userResult.success && userResult.data) {
          invitedUsersDetails.push({
            id: userResult.data._id,
            nickname: userResult.data.nickname,
            avatar: userResult.data.avatar,
            invitedAt: invitation.invitedAt
          })
        }
      }
    }

    // 获取邀请人信息
    let inviterInfo = null
    if (invitation.invitedBy) {
      const inviterResult = await usersDB.findById(invitation.invitedBy)
      if (inviterResult.success && inviterResult.data) {
        inviterInfo = {
          id: inviterResult.data._id,
          nickname: inviterResult.data.nickname,
          avatar: inviterResult.data.avatar,
          invitedAt: invitation.invitedAt
        }
      }
    }

    return success({
      totalInvited: invitation.totalInvited || 0,
      invitedUsers: invitedUsersDetails,
      invitedBy: inviterInfo,
      rewards: {
        perInvitation: INVITATION_REWARDS.INVITER,
        totalEarned: (invitation.totalInvited || 0) * INVITATION_REWARDS.INVITER
      }
    }, '获取邀请统计成功')

  } catch (error) {
    console.error('[ERROR] 获取邀请统计失败:', error)
    return error(error.message || '获取邀请统计失败')
  }
})

/**
 * 内部方法：添加邀请积分
 */
async function addInvitationPoints(openid, amount, source, description, relatedId) {
  try {
    // 获取用户信息
    const userResult = await usersDB.findByOpenid(openid)
    if (!userResult.success || !userResult.data) {
      return { success: false, message: '用户不存在' }
    }

    const user = userResult.data

    // 增加积分
    const updateResult = await usersDB.updateByOpenid(openid, {
      points: (user.points || 0) + amount
    })
    if (!updateResult.success) {
      return updateResult
    }

    // 添加积分记录
    const recordResult = await pointsRecordsDB.createRecord({
      userId: user._id,
      userNumber: user.no,
      type: 'earn',
      amount: amount,
      source: source,
      description: description,
      relatedId: relatedId
    })

    return {
      success: true,
      data: {
        newBalance: (user.points || 0) + amount,
        record: recordResult.success ? recordResult.data : null
      }
    }

  } catch (error) {
    console.error('[ERROR] 添加邀请积分失败:', error)
    return {
      success: false,
      message: error.message || '添加积分失败'
    }
  }
}
