/**
 * 签到相关API
 */

const checkInsDB = require('../db/check-ins')
const usersDB = require('../db/users')
const { getCurrentUser, incrementApiCallCount } = require('../helpers/users')
const { success, error } = require('../utils/response')
const { wrapAsync } = require('../utils/async-wrapper')
const { earnPoints } = require('./points')
const { formatDate } = require('../utils/date.js')

/**
 * 执行签到
 */
exports.checkIn = wrapAsync(async (params = {}) => {
  try {
    // 增加API调用计数
    await incrementApiCallCount()

    // 获取当前用户
    const userResult = await getCurrentUser()
    if (!userResult.success) {
      return error(userResult.message || '获取用户信息失败')
    }

    const user = userResult.data
    const today = formatDate(new Date(), 'YYYY-MM-DD')

    // 检查今日是否已签到
    const userId = user._id
    const todayCheckResult = await checkInsDB.checkTodayCheckIn(userId, today)
    if (!todayCheckResult.success) {
      return error('检查签到状态失败')
    }

    if (todayCheckResult.hasCheckedIn) {
      return error('今日已签到，请明天再来')
    }

    // 计算连续签到天数
    const consecutiveDays = await checkInsDB.calculateConsecutiveDays(userId, today)

    // 计算奖励
    const reward = calculateCheckInReward(consecutiveDays)

    // 创建签到记录
    const checkInData = {
      userId: userId,
      date: today,
      checkInAt: new Date(),
      consecutiveDays,
      reward
    }

    const createResult = await checkInsDB.createCheckIn(checkInData)
    if (!createResult.success) {
      return error('签到失败，请重试')
    }

    // 更新用户签到统计
    const currentStats = user.checkInStats

    const newStats = {
      totalDays: currentStats.totalDays + 1,
      consecutiveDays: consecutiveDays,
      lastCheckInDate: today,
      longestStreak: Math.max(currentStats.longestStreak, consecutiveDays)
    }

    await usersDB.updateCheckInStats(user.openid, newStats)

    // 添加积分记录
    const pointsResult = await earnPoints(
      user.openid,
      reward,
      'check_in',
      `签到获得积分 (连续${consecutiveDays}天)`,
      createResult.data._id
    )

    if (!pointsResult.success) {
      console.error('添加签到积分失败:', pointsResult.message)
    }

    return success({
      checkIn: createResult.data,
      reward,
      consecutiveDays,
      stats: newStats,
      points: pointsResult.success ? pointsResult.data : null
    }, '签到成功！')

  } catch (error) {
    console.error('签到失败:', error)
    return error(error.message || '签到失败')
  }
})

/**
 * 获取签到状态
 */
exports.getCheckInStatus = wrapAsync(async (params = {}) => {
  try {
    // 获取当前用户
    const userResult = await getCurrentUser()
    if (!userResult.success) {
      return error(userResult.message || '获取用户信息失败')
    }

    const user = userResult.data
    const today = formatDate(new Date(), 'YYYY-MM-DD')

    // 获取签到统计
    const userId = user._id
    const statsResult = await checkInsDB.getCheckInStats(userId)
    if (!statsResult.success) {
      return error('获取签到状态失败')
    }

    // 获取用户签到统计（从用户表）
    const userStats = user.checkInStats

    // 计算下次签到奖励
    const nextReward = calculateCheckInReward(
      statsResult.data.hasCheckedInToday ? statsResult.data.consecutiveDays + 1 : statsResult.data.consecutiveDays + 1
    )

    return success({
      hasCheckedInToday: statsResult.data.hasCheckedInToday,
      consecutiveDays: statsResult.data.consecutiveDays,
      totalDays: userStats.totalDays,
      longestStreak: userStats.longestStreak,
      todayCheckIn: statsResult.data.todayCheckIn,
      nextReward,
      isVip: user.vip?.status === true
    }, '获取签到状态成功')

  } catch (error) {
    console.error('获取签到状态失败:', error)
    return error(error.message || '获取签到状态失败')
  }
})

/**
 * 获取签到日历
 */
exports.getCheckInCalendar = wrapAsync(async (params = {}) => {
  try {
    const { yearMonth } = params
    
    if (!yearMonth || !/^\d{4}-\d{2}$/.test(yearMonth)) {
      return error('请提供有效的年月参数 (YYYY-MM)')
    }

    // 获取当前用户
    const userResult = await getCurrentUser()
    if (!userResult.success) {
      return error(userResult.message || '获取用户信息失败')
    }

    const user = userResult.data
    const userId = user._id

    // 获取月度签到记录
    const monthlyResult = await checkInsDB.getMonthlyCheckIns(userId, yearMonth)
    if (!monthlyResult.success) {
      return error('获取签到日历失败')
    }

    return success({
      yearMonth,
      checkIns: monthlyResult.data
    }, '获取签到日历成功')

  } catch (error) {
    console.error('获取签到日历失败:', error)
    return error(error.message || '获取签到日历失败')
  }
})

/**
 * 获取签到历史
 */
exports.getCheckInHistory = wrapAsync(async (params = {}) => {
  try {
    const { limit = 30 } = params

    // 获取当前用户
    const userResult = await getCurrentUser()
    if (!userResult.success) {
      return error(userResult.message || '获取用户信息失败')
    }

    const user = userResult.data
    const userId = user._id

    // 获取签到历史
    const historyResult = await checkInsDB.getUserCheckInHistory(userId, limit)
    if (!historyResult.success) {
      return error('获取签到历史失败')
    }

    return success({
      history: historyResult.data
    }, '获取签到历史成功')

  } catch (error) {
    console.error('获取签到历史失败:', error)
    return error(error.message || '获取签到历史失败')
  }
})

/**
 * 计算签到奖励
 * @param {number} consecutiveDays - 连续签到天数
 * @returns {number} 奖励积分
 */
function calculateCheckInReward(consecutiveDays) {
  let baseReward = 1

  // 基础奖励规则
  if (consecutiveDays % 7 === 0) {
    baseReward = 5 // 每7天一个周期，第7天奖励5积分
  } else {
    baseReward = 1 // 普通天数奖励1积分
  }

  // 里程碑奖励
  if (consecutiveDays === 30) {
    baseReward += 10 // 连续30天额外奖励
  } else if (consecutiveDays === 100) {
    baseReward += 30 // 连续100天额外奖励
  } else if (consecutiveDays === 365) {
    baseReward += 100 // 连续365天额外奖励
  }

  return baseReward
}
