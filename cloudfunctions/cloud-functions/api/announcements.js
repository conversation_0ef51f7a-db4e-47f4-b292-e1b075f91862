/**
 * 公告相关API（仅用户端）
 */

const announcementsDB = require('../db/announcements')
const { success, error } = require('../utils/response')
const { wrapAsync } = require('../utils/async-wrapper')
const { formatAnnouncementList } = require('../utils/announcements')

/**
 * 获取公告列表（用户端）
 */
exports.getAnnouncementList = wrapAsync(async (params = {}) => {
  const {
    type = 'all',
    page = 1,
    pageSize = 20
  } = params

  // 参数验证
  if (pageSize > 50) {
    return error('每页数量不能超过50条')
  }

  const skip = (page - 1) * pageSize
  const limit = pageSize

  console.log(`[Announcements] 获取公告列表: type=${type}, page=${page}, pageSize=${pageSize}`)

  const result = await announcementsDB.getPublishedAnnouncements({
    type,
    limit,
    skip
  })

  if (!result.success) {
    return result
  }

  // 处理返回数据格式
  const processedList = formatAnnouncementList(result.data.list)

  return success({
    list: processedList,
    hasMore: result.data.hasMore,
    total: result.data.total,
    currentPage: page,
    pageSize
  }, '获取公告列表成功')
})


