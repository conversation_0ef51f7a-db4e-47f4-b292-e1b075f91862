/**
 * 配置API接口层
 */

const { success, error } = require('../utils/response')
const { wrapAsync } = require('../utils/async-wrapper')
const { validateRequired } = require('../utils/validators')
const configDB = require('../db/config')

/**
 * 获取所有配置
 * 返回格式：{ key1: value1, key2: value2, ... }
 */
exports.getAllConfigs = wrapAsync(async (params = {}) => {
  console.log('[ConfigAPI] 获取所有配置')
  
  const result = await configDB.getAllConfigs()
  
  if (!result.success) {
    console.error('[ConfigAPI] 获取配置失败:', result.error)
    return error('获取配置失败', 'CONFIG_FETCH_ERROR')
  }

  // 转换为 key-value 对象格式
  const configMap = {}
  result.data.forEach(config => {
    configMap[config.key] = config.value
  })

  console.log(`[ConfigAPI] 配置获取成功，共 ${Object.keys(configMap).length} 个配置`)
  
  return success(configMap, '获取配置成功')
})


