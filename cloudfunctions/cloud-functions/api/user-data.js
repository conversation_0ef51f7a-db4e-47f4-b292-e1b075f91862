/**
 * 数据同步相关API
 */

const userDataDB = require('../db/user-data')
const { getCurrentUser } = require('../helpers/users')
const { success, error } = require('../utils/response')
const { wrapAsync } = require('../utils/async-wrapper')
const { validateRequired } = require('../utils/validators')
const { calculateUserDataStats } = require('../utils/data-stats')

/**
 * 获取云端数据信息
 */
exports.getCloudDataInfo = wrapAsync(async (params = {}) => {
  // 获取当前用户
  const userResult = await getCurrentUser()
  if (!userResult.success) {
    return userResult
  }
  const user = userResult.data

  // 获取用户数据信息
  const dataResult = await userDataDB.getUserData(user._id)
  if (!dataResult.success) {
    return dataResult
  }

  // 如果没有数据，返回基本信息
  if (!dataResult.hasData || !dataResult.data) {
    return success({
      hasData: false,
      timestamp: null,
      lastModified: null
    }, '获取云端数据信息成功')
  }

  return success({
    hasData: dataResult.hasData,
    timestamp: dataResult.timestamp || null,
    lastModified: dataResult.lastModified || null
  }, '获取云端数据信息成功')
})

/**
 * 下载用户数据
 */
exports.downloadUserData = wrapAsync(async (params = {}) => {
  // 获取当前用户
  const userResult = await getCurrentUser()
  if (!userResult.success) {
    return userResult
  }

  const user = userResult.data

  // 获取用户数据
  const dataResult = await userDataDB.getUserData(user._id)

  if (!dataResult.success) {
    return dataResult
  }

  if (!dataResult.hasData) {
    return success(null, '暂无云端数据')
  }

  return success(dataResult.data, '下载用户数据成功')
})

/**
 * 上传用户数据
 */
exports.uploadUserData = wrapAsync(async (params = {}) => {
  // 获取当前用户
  const userResult = await getCurrentUser()
  if (!userResult.success) {
    return userResult
  }

  const user = userResult.data

  // 保存用户数据
  const saveResult = await userDataDB.saveUserData(user._id, params.data)

  if (!saveResult.success) {
    return saveResult
  }

  return success(null, '上传用户数据成功')
})

/**
 * 获取历史数据列表（用于加载历史数据）
 */
exports.getHistoryDataList = wrapAsync(async (params = {}) => {
  // 获取当前用户
  const userResult = await getCurrentUser()
  if (!userResult.success) {
    return userResult
  }

  const user = userResult.data

  // 限制获取数量
  params.limit = 7

  // 获取历史数据列表
  const historyResult = await userDataDB.getUserDataHistory(user._id, params)

  if (!historyResult.success) {
    return historyResult
  }

  // 为每个历史数据计算统计信息
  const historyDataWithStats = historyResult.data.map(historyItem => {
    return {
      ...historyItem,
      stats: calculateUserDataStats(historyItem.data)
    }
  })

  return success(historyDataWithStats, '获取历史数据列表成功')
})

/**
 * 获取指定的历史数据
 */
exports.getHistoryData = wrapAsync(async (params = {}) => {
  // 支持通过 dataId 或 timestamp 查询
  if (!params.dataId && !params.timestamp) {
    return error('缺少必需参数：dataId 或 timestamp')
  }

  // 获取当前用户
  const userResult = await getCurrentUser()
  if (!userResult.success) {
    return userResult
  }

  const user = userResult.data

  let dataResult

  if (params.dataId) {
    // 通过 dataId 查询
    dataResult = await userDataDB.getHistoryDataById(params.dataId, user._id)
  } else {
    // 通过 timestamp 查询
    dataResult = await userDataDB.getHistoryDataByTimestamp(params.timestamp, user._id)
  }

  if (!dataResult.success) {
    return dataResult
  }

  return success(dataResult.data, '获取历史数据成功')
})

/**
 * 获取云端数据统计信息
 */
exports.getCloudDataStats = wrapAsync(async (params = {}) => {
  // 获取当前用户
  const userResult = await getCurrentUser()
  if (!userResult.success) {
    return userResult
  }
  const user = userResult.data

  // 获取用户数据
  const dataResult = await userDataDB.getUserData(user._id)
  if (!dataResult.success) {
    return dataResult
  }

  // 如果没有数据，返回空统计
  if (!dataResult.hasData || !dataResult.data) {
    return success({
      hasData: false,
      lastModified: null,
      stats: {
        workHistoryCount: 0,
        timeSegmentCount: 0,
        fishingRecordCount: 0,
        incomeAdjustmentCount: 0
      }
    }, '获取云端数据统计成功')
  }

  const userData = dataResult.data

  // 计算统计数据
  const stats = calculateUserDataStats(userData)

  return success({
    hasData: true,
    lastModified: dataResult.lastModified,
    stats: stats
  }, '获取云端数据统计成功')
})

/**
 * 清空云端数据（软删除）
 */
exports.clearCloudData = wrapAsync(async (params = {}) => {
  // 获取当前用户
  const userResult = await getCurrentUser()
  if (!userResult.success) {
    return userResult
  }
  const user = userResult.data

  // 删除用户的所有数据
  const deleteResult = await userDataDB.deleteUserData(user._id)

  if (!deleteResult.success) {
    return deleteResult
  }

  return success(null, '云端数据清空成功')
})
