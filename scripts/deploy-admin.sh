#!/bin/bash

# 管理端云函数部署脚本

echo "🚀 开始部署管理端云函数..."

echo "📦 安装管理端云函数依赖..."
cd cloudfunctions/cloud-functions-admin
npm install

echo "✅ 依赖安装完成"

echo "📋 部署提示："
echo "1. 请在微信开发者工具中右键点击 cloud-functions-admin 云函数"
echo "2. 选择 '上传并部署：云端安装依赖'"
echo "3. 等待部署完成"
echo "4. 在云开发控制台配置环境变量 ADMIN_SECRET_KEYS"
echo "5. 配置HTTP触发器，路径为 /admin"

echo "🔑 生成示例密钥："
node -e "console.log('ADMIN_SECRET_KEYS=' + require('crypto').randomBytes(32).toString('hex'))"

echo "🎉 准备工作完成！"
