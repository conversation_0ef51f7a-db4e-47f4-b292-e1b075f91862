# 数据结构修复报告

## 📋 问题概述

根据 `cloudfunctions/README.md` 中的正确数据结构，修复了前端和云函数中的字段名和集合名不一致问题。

## ❌ **问题根因**

### **字段名不一致**
- **错误使用**: `_openid` 
- **正确字段**: `openid`

### **集合名不一致**
- **错误使用**: `points_records`, `vip_records`, `check_in_records`
- **正确集合**: `points-records`, `vip-records`, `check-ins`

## ✅ **修复内容**

### **1. 前端字段名修复**

#### **用户详情页面参数传递**
```javascript
// 修复前
const params = {
  userId: currentUser.value._openid
}

// 修复后
const params = {
  userId: currentUser.value.openid || currentUser.value._openid
}
```

#### **涉及的功能模块**
- ✅ **签到历史加载**: `loadCheckinHistory()`
- ✅ **积分历史加载**: `loadPointsHistory()`
- ✅ **VIP记录加载**: `loadVipHistory()`
- ✅ **用户编辑功能**: `saveUserEdit()`
- ✅ **VIP管理功能**: `grantVip()`, `renewVip()`, `revokeVip()`

#### **界面显示修复**
- ✅ **用户详情对话框**: OpenID 显示
- ✅ **用户编辑对话框**: OpenID 显示
- ✅ **VIP记录对话框**: OpenID 显示

### **2. 云函数数据库查询修复**

#### **用户查询字段**
```javascript
// 修复前
.where({ _openid: userId })

// 修复后
.where({ openid: userId })
```

#### **记录查询字段**
```javascript
// 修复前 - 签到记录
const where = { _openid: userId }

// 修复后 - 签到记录
const where = { userId: userId }

// 修复前 - 积分记录
const where = { _openid: userId }

// 修复后 - 积分记录
const where = { userId: userId }

// 修复前 - VIP记录
where._openid = userId

// 修复后 - VIP记录
where.userId = userId
```

#### **集合名修复**
```javascript
// 修复前
this.db.collection('points_records')
this.db.collection('vip_records')

// 修复后
this.db.collection('points-records')
this.db.collection('vip-records')
```

### **3. 数据结构对照表**

#### **用户数据结构**
| 字段名 | 类型 | 说明 |
|--------|------|------|
| `openid` | String | 用户唯一标识 |
| `nickname` | String | 用户昵称 |
| `avatar` | String | 头像URL |
| `points` | Number | 积分余额 |
| `vip.status` | Boolean | VIP状态 |
| `vip.expiredAt` | Date | VIP过期时间 |

#### **签到记录数据结构**
| 字段名 | 类型 | 说明 |
|--------|------|------|
| `userId` | String | 用户ID |
| `date` | String | 签到日期 (YYYY-MM-DD) |
| `checkInAt` | Date | 签到时间 |
| `consecutiveDays` | Number | 连续签到天数 |
| `reward` | Number | 获得积分 |

#### **积分记录数据结构**
| 字段名 | 类型 | 说明 |
|--------|------|------|
| `userId` | String | 用户ID |
| `type` | String | 类型: earn, spend |
| `amount` | Number | 积分数量 |
| `source` | String | 来源: check_in, purchase, admin |
| `description` | String | 描述 |
| `timestamp` | Date | 时间戳 |

#### **VIP记录数据结构**
| 字段名 | 类型 | 说明 |
|--------|------|------|
| `userId` | String | 用户ID |
| `action` | String | 操作: activate, extend, expire, cancel |
| `duration` | Number | 时长(天数) |
| `startDate` | Date | 开始时间 |
| `endDate` | Date | 结束时间 |
| `source` | String | 来源: purchase, gift, manual |
| `description` | String | 描述 |

### **4. 集合名称对照表**

| 功能模块 | 错误集合名 | 正确集合名 |
|----------|------------|------------|
| 用户信息 | `users` | `users` ✅ |
| 签到记录 | `check_in_records` | `check-ins` |
| 积分记录 | `points_records` | `points-records` |
| VIP记录 | `vip_records` | `vip-records` |
| 商店商品 | `store_items` | `store-items` |
| 兑换码 | `redemption_codes` | `redemption-codes` |
| 用户反馈 | `feedbacks` | `feedbacks` ✅ |
| 系统公告 | `announcements` | `announcements` ✅ |

## 🔧 **修复的文件列表**

### **前端文件**
- ✅ `admin-app/src/views/Users/<USER>
  - 修复所有用户ID字段引用
  - 修复界面显示的OpenID字段
  - 添加兼容性处理 (`openid || _openid`)

### **云函数文件**
- ✅ `cloudfunctions/cloud-functions-admin/db/users.js`
  - 修复用户查询字段名
  - 修复VIP操作中的字段名
  - 修复积分记录查询字段名
  - 修复集合名称

- ✅ `cloudfunctions/cloud-functions-admin/db/check-ins.js`
  - 修复签到记录查询字段名
  - 确保日期处理正确

## 🎯 **兼容性处理**

为了确保向后兼容，在前端使用了兼容性处理：

```javascript
// 兼容旧字段名和新字段名
userId: currentUser.value.openid || currentUser.value._openid
```

这样即使数据库中存在使用旧字段名的数据，也能正常工作。

## 📊 **API 参数格式**

### **签到历史接口**
```javascript
// 请求参数
{
  userId: "user_openid",      // 用户openid
  startDate: "2024-01-01",    // 可选
  endDate: "2024-12-31"       // 可选
}
```

### **积分历史接口**
```javascript
// 请求参数
{
  userId: "user_openid",      // 用户openid
  type: "all",               // all/earn/spend
  page: 1,
  pageSize: 20,
  startDate: "2024-01-01",   // 可选
  endDate: "2024-12-31"      // 可选
}
```

### **VIP记录接口**
```javascript
// 请求参数
{
  userId: "user_openid",      // 用户openid（可选）
  type: "all",               // all/grant/renew/revoke
  page: 1,
  pageSize: 20
}
```

## ✅ **修复验证**

### **数据库查询验证**
- ✅ 用户查询使用 `openid` 字段
- ✅ 记录查询使用 `userId` 字段
- ✅ 集合名称使用连字符格式

### **前端参数验证**
- ✅ 所有API调用使用正确的用户ID
- ✅ 界面显示使用兼容性字段
- ✅ 参数传递格式正确

### **API接口验证**
- ✅ 参数验证逻辑正确
- ✅ 数据库操作使用正确字段
- ✅ 返回数据格式统一

## 🚀 **下一步操作**

1. **部署云函数**：通过微信开发者工具部署更新
2. **测试功能**：验证所有历史记录功能正常工作
3. **数据迁移**：如果需要，迁移现有数据到新的字段格式

现在所有数据结构都已经与 `cloudfunctions/README.md` 中的规范保持一致！
