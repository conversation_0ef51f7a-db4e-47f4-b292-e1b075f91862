# 摸鱼记账小程序 - 开发规范与流程

本文档定义了摸鱼记账小程序项目的开发规范、工作流程和最佳实践。

## 📋 项目架构概览

```
mp-md/
├── miniprogram/                   # 小程序前端
│   ├── pages/                     # 页面目录
│   │   ├── index/                 # 首页
│   │   ├── profile/               # 个人中心
│   │   ├── points/                # 积分页面
│   │   ├── check-in/              # 签到页面
│   │   ├── store/                 # 商店页面
│   │   ├── feedback/              # 反馈页面
│   │   ├── announcements/         # 公告页面
│   │   └── ...                    # 其他页面
│   ├── components/                # 组件目录
│   ├── utils/                     # 工具函数
│   ├── app.js                     # 小程序入口
│   └── app.json                   # 小程序配置
├── cloudfunctions/                # 云函数
│   ├── cloud-functions/           # 用户端云函数
│   │   ├── index.js               # 云函数入口
│   │   ├── api/                   # API 模块
│   │   │   ├── user.js            # 用户相关 API
│   │   │   ├── points.js          # 积分相关 API
│   │   │   ├── check-in.js        # 签到相关 API
│   │   │   ├── store.js           # 商店相关 API
│   │   │   └── ...                # 其他 API
│   │   ├── middleware/            # 中间件
│   │   └── utils/                 # 工具函数
│   └── cloud-functions-admin/     # 管理端云函数
│       ├── index.js               # 云函数入口
│       ├── api/                   # 管理 API
│       │   ├── user-admin.js      # 用户管理 API
│       │   ├── points-admin.js    # 积分管理 API
│       │   ├── system-admin.js    # 系统管理 API
│       │   ├── feedback-admin.js  # 反馈管理 API
│       │   └── ...                # 其他管理 API
│       ├── middleware/            # 权限验证中间件
│       └── utils/                 # 工具函数
├── admin-app/                     # 后台管理应用
└── docs/                          # 项目文档
```

### 🏗️ 系统架构

```mermaid
graph TB
    A[小程序前端] --> B[用户端云函数]
    C[后台管理应用] --> D[管理端云函数]
    B --> E[微信云开发数据库]
    D --> E
    B --> F[微信云存储]
    D --> F
```

## 🔄 开发流程规范

### 1. 功能开发流程

无论从哪个模块开始开发，都需要按照以下顺序同步修改：

#### 🎯 标准开发顺序

1. **数据库设计/修改** → 2. **用户端云函数** → 3. **小程序前端** → 4. **管理端云函数** → 5. **后台管理应用**

#### 🔄 同步修改原则

**从小程序开始修改时**：
```
小程序前端 → 用户端云函数 → 管理端云函数 → 后台管理应用
```

**从后台管理开始修改时**：
```
后台管理应用 → 管理端云函数 → 用户端云函数 → 小程序前端
```

**从云函数开始修改时**：
```
云函数 → 小程序前端 & 后台管理应用（并行）
```

### 2. 具体开发步骤

#### 步骤 1: 数据库设计
```bash
# 1. 设计数据表结构
# 2. 定义字段类型和索引
# 3. 考虑数据关联关系
# 4. 规划数据访问权限
```

#### 步骤 2: 用户端云函数开发
```bash
cd cloudfunctions/cloud-functions

# 1. 添加新的 API 函数
# 2. 实现数据库操作逻辑
# 3. 添加权限验证
# 4. 编写错误处理
# 5. 部署云函数
npm run deploy
```

#### 步骤 3: 小程序前端开发
```bash
cd miniprogram

# 1. 添加页面组件
# 2. 实现 API 调用
# 3. 添加用户界面
# 4. 测试功能完整性
# 5. 上传小程序代码
```

#### 步骤 4: 管理端云函数开发
```bash
cd cloudfunctions/cloud-functions-admin

# 1. 添加管理 API 函数
# 2. 实现数据统计逻辑
# 3. 添加管理权限验证
# 4. 编写数据导出功能
# 5. 部署云函数
npm run deploy
```

#### 步骤 5: 后台管理应用开发
```bash
cd admin-app

# 1. 添加管理页面
# 2. 实现数据展示
# 3. 添加操作功能
# 4. 测试管理功能
# 5. 构建应用
pnpm tauri build
```

## 📝 代码规范

### 1. 命名规范

#### 文件命名
```bash
# 小程序页面
pages/profile/index.js
pages/points/index.js
pages/check-in/index.js

# 云函数 API 模块（按功能分组）
api/user.js              # 用户相关 API
api/points.js            # 积分相关 API
api/check-in.js          # 签到相关 API
api/user-admin.js        # 用户管理 API（管理端）
api/points-admin.js      # 积分管理 API（管理端）

# 后台管理页面
views/Users/<USER>
views/Points/index.vue
views/CheckIn/index.vue
```

#### 变量命名
```javascript
// 驼峰命名法
const userName = 'John'
const pointBalance = 100

// 常量大写
const API_BASE_URL = 'https://api.example.com'
const MAX_RETRY_COUNT = 3

// 组件名 PascalCase
const UserProfile = {}
const PointHistory = {}
```

#### 函数命名
```javascript
// 动词开头，描述功能
function getUserInfo() {}
function updatePointBalance() {}
function deleteUserRecord() {}

// 布尔值返回用 is/has/can 开头
function isUserVip() {}
function hasPermission() {}
function canAccess() {}
```

### 2. API 接口规范

#### 云函数 API 命名
```javascript
// 用户端云函数 (cloud-functions) - 通过 type 参数调用
getUserInfo           // 获取用户信息
updateUserInfo        // 更新用户信息
getPointsBalance      // 获取积分余额
getPointsRecords      // 获取积分记录
checkIn               // 执行签到
getCheckInStatus      // 获取签到状态
getStoreItems         // 获取商店商品
purchaseItem          // 购买商品

// 管理端云函数 (cloud-functions-admin) - 通过 type 参数调用
getUserList           // 管理端获取用户列表
getUserStats          // 管理端获取用户统计
updateUserStatus      // 管理端更新用户状态
getSystemStats        // 获取系统统计
getDashboardStats     // 获取仪表板数据
getPointsStatsAdmin   // 获取积分统计（管理端）
```

#### API 参数规范
```javascript
// 用户端云函数调用结构
{
  type: 'getUserInfo',       // API 类型
  data: {                    // 业务参数（可选）
    fields: ['nickname', 'avatar']
  },
  version: '1.0.0'          // 版本号（必需）
}

// 管理端云函数调用结构
{
  type: 'getUserList',       // API 类型
  data: {                    // 业务参数
    page: 1,
    limit: 20,
    keyword: ''
  },
  secretKey: 'ADMIN_KEY'     // 管理端密钥（必需）
}

// 统一的返回结构
{
  success: true,             // 操作是否成功
  message: '操作成功',       // 提示信息
  data: {                    // 返回数据
    // 具体数据
  },
  code: 200                  // 状态码（可选）
}
```

### 3. 错误处理规范

#### 云函数错误处理
```javascript
// 统一错误处理函数
function handleError(error, context = '') {
  console.error(`[${context}] 错误:`, error)
  
  return {
    success: false,
    message: error.message || '操作失败',
    code: error.code || 500
  }
}

// 使用示例
try {
  const result = await db.collection('users').get()
  return {
    success: true,
    data: result.data
  }
} catch (error) {
  return handleError(error, 'getUserList')
}
```

#### 前端错误处理
```javascript
// 小程序错误处理
async function callCloudFunction(name, data) {
  try {
    const result = await wx.cloud.callFunction({
      name,
      data
    })
    
    if (result.result.success) {
      return result.result
    } else {
      wx.showToast({
        title: result.result.message,
        icon: 'error'
      })
      throw new Error(result.result.message)
    }
  } catch (error) {
    console.error('云函数调用失败:', error)
    wx.showToast({
      title: '网络错误，请重试',
      icon: 'error'
    })
    throw error
  }
}

// 后台管理错误处理
import { ElMessage } from 'element-plus'

async function callCloudFunction(type, data, options = {}) {
  try {
    const result = await apiCall(type, data)
    
    if (result.success) {
      if (options.showSuccess) {
        ElMessage.success(result.message)
      }
      return result
    } else {
      if (options.showError !== false) {
        ElMessage.error(result.message)
      }
      throw new Error(result.message)
    }
  } catch (error) {
    if (options.showError !== false) {
      ElMessage.error(error.message || '操作失败')
    }
    throw error
  }
}
```

## 🗄️ 数据库规范

### 1. 集合命名规范

```javascript
// 用户相关
users              // 用户基本信息
vip_records        // VIP记录
user_data          // 用户数据

// 积分相关
points_records     // 积分记录

// 签到相关
check_in_records   // 签到记录

// 商店相关
store_items        // 商品信息
redemption_codes   // 兑换码

// 反馈相关
feedbacks          // 用户反馈

// 公告相关
announcements      // 系统公告

// 友情应用
friend_apps        // 友情应用

// 摸鱼状态
fishing_status     // 摸鱼状态记录

// 系统相关
configs            // 系统配置
```

### 2. 字段命名规范

```javascript
// 用户表 (users)
{
  _id: '',           // 文档ID
  _openid: '',       // 用户openid
  nickname: '',      // 用户昵称
  avatar: '',        // 头像URL
  isVip: false,      // 是否VIP
  vipExpireAt: null, // VIP过期时间
  pointsBalance: 0,  // 积分余额
  totalCheckIns: 0,  // 总签到次数
  createdAt: Date,   // 创建时间
  updatedAt: Date    // 更新时间
}

// 积分记录表 (points_records)
{
  _id: '',
  _openid: '',       // 用户openid
  amount: 0,         // 积分数量（正数为获得，负数为消费）
  type: '',          // 类型：checkin, purchase, admin_adjust
  description: '',   // 描述
  relatedId: '',     // 关联ID（如商品ID）
  createdAt: Date
}
```

### 3. 索引规范

```javascript
// 用户表索引
db.collection('users').createIndex({
  _openid: 1,        // 用户查询（唯一）
  isVip: 1,          // VIP筛选
  pointsBalance: -1, // 积分排序
  createdAt: -1      // 时间排序
})

// 积分记录表索引
db.collection('points_records').createIndex({
  _openid: 1,
  createdAt: -1
})

// 签到记录表索引
db.collection('check_in_records').createIndex({
  _openid: 1,
  date: -1           // 按日期查询
})

// 反馈表索引
db.collection('feedbacks').createIndex({
  status: 1,         // 状态筛选
  createdAt: -1      // 时间排序
})
```

## 🔐 权限管理规范

### 1. 数据库权限

```javascript
// 用户端权限（仅能访问自己的数据）
{
  "read": "auth.openid == resource.data._openid",
  "write": "auth.openid == resource.data._openid"
}

// 管理端权限（通过云函数访问）
{
  "read": false,   // 禁止直接读取
  "write": false   // 禁止直接写入
}
```

### 2. 云函数权限验证

```javascript
// 用户端权限验证
function validateUser(event, context) {
  if (!context.OPENID) {
    throw new Error('用户未登录')
  }
  return context.OPENID
}

// 管理端权限验证
function validateAdmin(event) {
  const { secretKey } = event
  const validKeys = process.env.ADMIN_SECRET_KEYS?.split(',') || []
  
  if (!secretKey || !validKeys.includes(secretKey)) {
    throw new Error('管理权限验证失败')
  }
}
```

## 📦 部署规范

### 1. 云函数部署

```bash
# 开发环境部署
cd cloudfunctions/cloud-functions
npm run deploy:dev

cd cloudfunctions/cloud-functions-admin  
npm run deploy:dev

# 生产环境部署
npm run deploy:prod
```

### 2. 小程序发布

```bash
# 1. 代码检查
npm run lint

# 2. 构建优化
npm run build

# 3. 上传代码
# 使用微信开发者工具上传

# 4. 提交审核
# 在微信公众平台提交审核
```

### 3. 后台管理应用发布

```bash
cd admin-app

# 1. 构建桌面端
pnpm tauri build

# 2. 构建 Android 端
pnpm tauri android build

# 3. 构建 iOS 端（macOS）
pnpm tauri ios build

# 4. 分发应用
# 将构建产物分发给管理员
```

## 📚 文档规范

### 1. 代码注释

```javascript
/**
 * 获取用户积分历史记录
 * @param {Object} params - 查询参数
 * @param {string} params.userId - 用户ID
 * @param {number} params.page - 页码
 * @param {number} params.limit - 每页数量
 * @returns {Promise<Object>} 积分历史记录
 */
async function getPointHistory(params) {
  // 实现逻辑
}
```

### 2. API 文档

```markdown
## 获取用户信息

### 请求参数
- `userId` (string): 用户ID

### 返回数据
```json
{
  "success": true,
  "data": {
    "userId": "user123",
    "userName": "张三",
    "pointBalance": 100
  }
}
```

### 错误码
- `401`: 用户未登录
- `404`: 用户不存在
- `500`: 服务器错误
```

### 3. 更新日志

```markdown
## [1.1.0] - 2024-08-14

### 新增
- 添加用户积分系统
- 添加商店购买功能
- 添加后台管理应用

### 修复
- 修复用户登录问题
- 修复数据统计错误

### 优化
- 优化页面加载速度
- 优化数据库查询性能
```

## 🚨 注意事项

### 1. 安全规范

- **敏感信息**：不要在代码中硬编码密钥、密码等敏感信息
- **权限验证**：所有 API 都必须进行权限验证
- **数据验证**：对所有输入数据进行验证和过滤
- **错误信息**：不要在错误信息中暴露敏感信息

### 2. 性能规范

- **数据库查询**：避免 N+1 查询，合理使用索引
- **缓存策略**：对频繁查询的数据进行缓存
- **图片优化**：压缩图片，使用适当的格式
- **代码分割**：合理分割代码，避免单个文件过大

### 3. 兼容性规范

- **小程序兼容**：确保在不同版本的微信中正常运行
- **移动端适配**：确保在不同尺寸的设备上正常显示
- **浏览器兼容**：后台管理应用支持主流浏览器

## 📞 技术支持

如有问题，请参考：

1. **项目文档**：查看各模块的 README.md
2. **API 文档**：查看云函数接口文档
3. **开发规范**：遵循本文档的规范要求
4. **问题反馈**：通过 GitHub Issue 反馈问题

---

## 🔧 开发工具配置

### 1. VS Code 推荐插件

```json
{
  "recommendations": [
    "vue.volar",                    // Vue 3 支持
    "tauri-apps.tauri-vscode",      // Tauri 支持
    "rust-lang.rust-analyzer",     // Rust 支持
    "esbenp.prettier-vscode",       // 代码格式化
    "dbaeumer.vscode-eslint",       // ESLint
    "bradlc.vscode-tailwindcss",    // Tailwind CSS
    "ms-vscode.vscode-json",        // JSON 支持
    "redhat.vscode-yaml"            // YAML 支持
  ]
}
```

### 2. 开发环境配置

```bash
# Node.js 版本管理
nvm install 18.17.0
nvm use 18.17.0

# Rust 工具链
rustup update stable
rustup target add aarch64-linux-android
rustup target add aarch64-apple-ios

# 全局工具
npm install -g @tauri-apps/cli
npm install -g miniprogram-cli
```

### 3. 编辑器配置

```json
// .vscode/settings.json
{
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true
  },
  "files.associations": {
    "*.wxml": "html",
    "*.wxss": "css"
  },
  "emmet.includeLanguages": {
    "wxml": "html"
  }
}
```

## 📋 开发检查清单

### 新功能开发检查清单

#### 📱 小程序端
- [ ] 网络请求添加加载状态
- [ ] 错误处理和用户提示
- [ ] 数据缓存和本地存储
- [ ] 性能优化（图片压缩、代码分割）
- [ ] 微信小程序规范检查

#### ☁️ 云函数端
- [ ] 输入参数验证
- [ ] 权限验证（用户端/管理端）
- [ ] 错误处理和日志记录
- [ ] 性能优化（查询优化、缓存）
- [ ] API 文档更新
- [ ] 单元测试编写

#### 🖥️ 后台管理端
- [ ] 响应式布局（桌面端/移动端）
- [ ] 数据表格分页和筛选
- [ ] 表单验证和提交
- [ ] 权限控制和路由守卫
- [ ] 错误处理和日志记录
- [ ] 跨平台兼容性测试

### 发布前检查清单

#### 🔍 代码质量
- [ ] ESLint 检查通过
- [ ] 代码格式化完成
- [ ] 无 console.log 调试代码
- [ ] 无硬编码的敏感信息
- [ ] 注释完整且准确

#### 🧪 功能测试
- [ ] 核心功能正常工作
- [ ] 边界情况处理
- [ ] 错误场景测试
- [ ] 性能测试通过
- [ ] 兼容性测试完成

#### 📚 文档更新
- [ ] API 文档更新
- [ ] 用户手册更新
- [ ] 更新日志记录
- [ ] 部署文档检查

**记住：无论从哪个模块开始修改，都要及时同步修改其他相关模块，确保整个系统的一致性！**

## 📞 技术支持与资源

### 官方文档
- [微信小程序开发文档](https://developers.weixin.qq.com/miniprogram/dev/framework/)
- [微信云开发文档](https://developers.weixin.qq.com/miniprogram/dev/wxcloud/basis/getting-started.html)
- [Tauri 官方文档](https://tauri.app/zh-cn/)
- [Vue 3 官方文档](https://cn.vuejs.org/)
- [Element Plus 文档](https://element-plus.org/zh-CN/)

### 开发工具
- [微信开发者工具](https://developers.weixin.qq.com/miniprogram/dev/devtools/download.html)
- [VS Code](https://code.visualstudio.com/)
- [Android Studio](https://developer.android.com/studio)
- [Xcode](https://developer.apple.com/xcode/)

### 社区资源
- [微信开放社区](https://developers.weixin.qq.com/community/minihome)
- [Tauri Discord](https://discord.com/invite/tauri)
- [Vue.js 中文社区](https://vue3js.cn/)

记住：保持代码质量，遵循开发规范，及时同步修改，确保项目的可维护性和一致性！
