# 文档修正说明

本文档记录了对项目文档的修正内容，确保文档与实际项目结构保持一致。

## 🔧 修正内容总览

### 1. 文件命名规范修正

#### ❌ 修正前（错误）
```bash
# 云函数
getUserProfile.js
getPointHistory.js
```

#### ✅ 修正后（正确）
```bash
# 云函数 API 模块（按功能分组）
api/user.js              # 用户相关 API
api/points.js            # 积分相关 API
api/check-in.js          # 签到相关 API
api/user-admin.js        # 用户管理 API（管理端）
api/points-admin.js      # 积分管理 API（管理端）
```

**说明**：云函数采用模块化设计，按功能分组而不是单个函数一个文件。

### 2. API 命名规范修正

#### ❌ 修正前（错误）
```javascript
getUserProfile        // 获取用户信息
addAccountRecord      // 添加记账记录
```

#### ✅ 修正后（正确）
```javascript
// 用户端云函数 API
getUserInfo           // 获取用户信息
getPointsBalance      // 获取积分余额
checkIn               // 执行签到
purchaseItem          // 购买商品

// 管理端云函数 API
getUserList           // 管理端获取用户列表
getSystemStats        // 获取系统统计
getDashboardStats     // 获取仪表板数据
```

**说明**：API 命名更符合实际功能，去掉了不存在的记账功能。

### 3. 数据库集合命名修正

#### ❌ 修正前（错误）
```javascript
user_profiles      // 用户详细资料
user_settings      // 用户设置
points_rules       // 积分规则
shop_items         // 商品信息
shop_orders        // 订单记录
system_configs     // 系统配置
system_logs        // 系统日志
```

#### ✅ 修正后（正确）
```javascript
users              // 用户基本信息
vip_records        // VIP记录
points_records     // 积分记录
check_in_records   // 签到记录
store_items        // 商品信息
redemption_codes   // 兑换码
feedbacks          // 用户反馈
announcements      // 系统公告
friend_apps        // 友情应用
fishing_status     // 摸鱼状态记录
configs            // 系统配置
user_data          // 用户数据备份
```

**说明**：集合命名更符合实际项目的功能模块。

### 4. 项目结构完善

#### 修正前：简化结构
```
mp-md/
├── miniprogram/              # 小程序前端
├── cloudfunctions/           # 云函数
├── admin-app/               # 后台管理应用
└── docs/                    # 项目文档
```

#### 修正后：详细结构
```
mp-md/
├── miniprogram/              # 小程序前端
│   ├── pages/               # 页面文件
│   │   ├── index/           # 首页
│   │   ├── profile/         # 个人中心
│   │   ├── points/          # 积分页面
│   │   ├── check-in/        # 签到页面
│   │   ├── store/           # 商店页面
│   │   ├── feedback/        # 反馈页面
│   │   ├── announcements/   # 公告页面
│   │   └── ...              # 其他页面
│   ├── components/          # 组件文件
│   ├── utils/               # 工具函数
│   ├── app.js               # 小程序入口
│   └── app.json             # 小程序配置
├── cloudfunctions/           # 云函数
│   ├── cloud-functions/      # 用户端云函数
│   │   ├── index.js         # 云函数入口
│   │   ├── api/             # API 模块
│   │   │   ├── user.js      # 用户相关 API
│   │   │   ├── points.js    # 积分相关 API
│   │   │   ├── check-in.js  # 签到相关 API
│   │   │   ├── store.js     # 商店相关 API
│   │   │   └── ...          # 其他 API
│   │   ├── middleware/      # 中间件
│   │   └── utils/           # 工具函数
│   └── cloud-functions-admin/# 管理端云函数
│       ├── index.js         # 云函数入口
│       ├── api/             # 管理 API
│       │   ├── user-admin.js      # 用户管理 API
│       │   ├── points-admin.js    # 积分管理 API
│       │   ├── system-admin.js    # 系统管理 API
│       │   ├── feedback-admin.js  # 反馈管理 API
│       │   └── ...                # 其他管理 API
│       ├── middleware/      # 权限验证中间件
│       └── utils/           # 工具函数
├── admin-app/               # 后台管理应用
│   ├── src/                 # 前端源码
│   │   ├── views/           # 页面组件
│   │   │   ├── Dashboard/   # 仪表板
│   │   │   ├── Users/       # 用户管理
│   │   │   ├── Points/      # 积分管理
│   │   │   ├── CheckIn/     # 签到管理
│   │   │   ├── Shop/        # 商店管理
│   │   │   ├── Feedback/    # 反馈管理
│   │   │   ├── Announcements/# 公告管理
│   │   │   ├── FriendApps/  # 友情应用
│   │   │   ├── FishingStatus/# 摸鱼状态
│   │   │   ├── VIP/         # VIP管理
│   │   │   ├── Settings/    # 系统设置
│   │   │   └── Setup/       # 初始配置
│   │   ├── components/      # 公共组件
│   │   ├── api/             # API 接口
│   │   │   ├── wechat-api.js# 微信云开发 API
│   │   │   ├── tauri-api.js # Tauri 原生 API
│   │   │   └── dashboard.js # 仪表板 API
│   │   ├── router/          # 路由配置
│   │   ├── stores/          # 状态管理
│   │   └── utils/           # 工具函数
│   ├── src-tauri/           # Tauri 后端
│   │   ├── src/             # Rust 源码
│   │   │   ├── lib.rs       # 主模块
│   │   │   └── http_client.rs# HTTP 客户端
│   │   ├── capabilities/    # 权限配置
│   │   ├── gen/             # 生成的文件
│   │   └── Cargo.toml       # Rust 依赖
│   ├── public/              # 静态资源
│   ├── dist/                # 构建输出
│   └── README.md            # 后台应用文档
├── docs/                    # 项目文档
├── DEVELOPMENT_GUIDE.md     # 开发规范
└── README.md                # 项目说明
```

**说明**：提供了完整的项目结构，包含所有主要文件和目录。

### 5. API 调用示例修正

#### ❌ 修正前（错误）
```javascript
// 获取用户信息
await wx.cloud.callFunction({
  name: 'cloud-functions',
  data: {
    type: 'getUserProfile',
    data: {}
  }
})

// 添加记账记录
await wx.cloud.callFunction({
  name: 'cloud-functions',
  data: {
    type: 'addAccountRecord',
    data: {
      amount: 100,
      category: 'food',
      type: 'expense'
    }
  }
})
```

#### ✅ 修正后（正确）
```javascript
// 获取用户信息
await wx.cloud.callFunction({
  name: 'cloud-functions',
  data: {
    type: 'getUserInfo',
    version: '1.0.0'
  }
})

// 获取积分余额
await wx.cloud.callFunction({
  name: 'cloud-functions',
  data: {
    type: 'getPointsBalance',
    version: '1.0.0'
  }
})

// 执行签到
await wx.cloud.callFunction({
  name: 'cloud-functions',
  data: {
    type: 'checkIn',
    version: '1.0.0'
  }
})

// 购买商品
await wx.cloud.callFunction({
  name: 'cloud-functions',
  data: {
    type: 'purchaseItem',
    data: {
      itemId: 'item123',
      quantity: 1
    },
    version: '1.0.0'
  }
})
```

**说明**：
1. 移除了不存在的记账功能
2. 使用实际存在的 API 类型
3. 添加了必需的 version 参数

### 6. 管理端 API 调用示例修正

#### ❌ 修正前（错误）
```javascript
// 获取用户列表
await callCloudFunction('getUsersAdmin', {
  page: 1,
  limit: 20,
  keyword: ''
})

// 更新用户信息
await callCloudFunction('updateUserAdmin', {
  userId: 'user123',
  updates: {
    isVip: true
  }
})
```

#### ✅ 修正后（正确）
```javascript
// 获取用户列表
await callCloudFunction('getUserList', {
  page: 1,
  limit: 20,
  keyword: ''
})

// 获取系统统计
await callCloudFunction('getSystemStats', {})

// 获取仪表板数据
await callCloudFunction('getDashboardStats', {})

// 更新用户状态
await callCloudFunction('updateUserStatus', {
  userId: 'user123',
  isVip: true,
  vipExpireAt: '2024-12-31'
})
```

**说明**：使用实际存在的管理端 API 函数名。

## 📋 修正的文档文件

1. **DEVELOPMENT_GUIDE.md**
   - 文件命名规范
   - API 命名规范
   - 数据库集合命名
   - 项目结构
   - 索引规范

2. **README.md**
   - 项目结构
   - API 接口示例
   - 功能模块说明

3. **admin-app/README.md**
   - 云函数调用示例
   - API 接口说明

## ✅ 修正验证

所有修正都基于实际项目结构：

1. **云函数结构**：检查了 `cloudfunctions/cloud-functions/api/` 和 `cloudfunctions/cloud-functions-admin/api/` 目录
2. **小程序页面**：检查了 `miniprogram/pages/` 目录结构
3. **后台管理应用**：检查了 `admin-app/src/` 目录结构
4. **API 函数名**：基于实际云函数中的 API 类型

## 🎯 修正目标

确保文档：
1. **准确性**：与实际项目结构完全一致
2. **完整性**：涵盖所有主要功能模块
3. **实用性**：提供可直接使用的代码示例
4. **一致性**：所有文档使用统一的命名规范

现在文档已经与实际项目结构保持一致，开发者可以根据文档准确地进行开发工作。
