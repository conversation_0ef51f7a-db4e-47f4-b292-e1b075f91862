# 微信云函数API集成指南

## 🔄 新的调用流程

我们已经重新设计了API调用流程，现在使用微信官方的正确调用方式：

### 1. 获取 Access Token
```
GET https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=<APPID>&secret=<APPSECRET>
```

### 2. 调用云函数
```
POST https://api.weixin.qq.com/tcb/invokecloudfunction?access_token=<ACCESS_TOKEN>&env=<ENV>&name=<FUNCTION_NAME>
```

## 🔧 配置方式变更

### 之前（环境变量方式）
```env
VITE_API_BASE_URL=http://localhost:3000/admin
VITE_ADMIN_SECRET_KEY=dev-secret-key-123456
```

### 现在（应用内配置方式）
用户在应用内填写以下信息：
- **AppID**: 小程序的唯一标识符
- **AppSecret**: 小程序的密钥
- **云环境ID**: 云开发环境的唯一标识
- **云函数名称**: 管理端云函数名称（默认：cloud-functions-admin）
- **管理密钥**: 管理端API的访问密钥

## 📱 使用流程

### 1. 首次启动
1. 打开应用，自动跳转到配置页面
2. 填写微信小程序相关信息
3. 点击"测试连接"验证配置
4. 测试成功后点击"保存配置"
5. 自动跳转到仪表板

### 2. 后续使用
- 配置信息保存在本地，无需重复输入
- 如需修改配置，可以在设置中重新配置
- 支持清除配置重新设置

## 🔐 安全特性

### 1. 本地存储
- 配置信息保存在本地存储中
- 不会上传到服务器
- 支持清除配置

### 2. Access Token 缓存
- 自动缓存 Access Token
- 提前5分钟刷新，避免过期
- 失效时自动重新获取

### 3. 错误处理
- 完整的错误处理机制
- 用户友好的错误提示
- 自动重试机制

## 🛠️ 技术实现

### API调用封装
```javascript
// 新的API调用方式
import { callCloudFunction } from '@/api/wechat-api.js'

// 调用示例
const result = await callCloudFunction('getDashboardStats', { period: '30d' })
```

### 配置管理
```javascript
// 设置配置
setWechatConfig({
  appId: 'your-app-id',
  appSecret: 'your-app-secret',
  env: 'your-env-id',
  functionName: 'cloud-functions-admin',
  secretKey: 'your-secret-key'
})

// 测试连接
const result = await testConnection()
```

## 📋 部署清单

### 1. 云函数部署
```bash
cd cloudfunctions/cloud-functions-admin
npm install
# 使用微信开发者工具部署
```

### 2. 环境变量配置
在微信云开发控制台配置：
```
ADMIN_SECRET_KEYS=your-secret-key-1,your-secret-key-2
```

### 3. HTTP触发器配置
- 路径：`/admin`
- 方法：`GET, POST`
- 超时时间：60秒

### 4. 前端应用
```bash
cd admin-app
pnpm install
pnpm dev  # 开发模式
pnpm build  # 生产构建
```

## 🔍 故障排除

### 常见错误

#### 1. Access Token 获取失败
**错误**: `获取Access Token失败: invalid appid`
**解决**: 检查AppID是否正确

#### 2. 云函数调用失败
**错误**: `微信API错误: invalid env`
**解决**: 检查云环境ID是否正确

#### 3. 管理密钥验证失败
**错误**: `无效的访问密钥`
**解决**: 检查管理密钥是否与云函数环境变量一致

### 调试方法

#### 1. 查看浏览器控制台
- 打开开发者工具
- 查看Console面板的错误信息
- 查看Network面板的请求详情

#### 2. 查看云函数日志
- 进入微信云开发控制台
- 选择云函数 → cloud-functions-admin
- 查看调用日志

#### 3. 测试连接功能
- 在配置页面使用"测试连接"功能
- 查看详细的错误信息
- 根据错误信息调整配置

## 📊 API调用示例

### 获取仪表板统计
```javascript
const stats = await callCloudFunction('getDashboardStats', {
  period: '30d'
})

console.log(stats)
// {
//   success: true,
//   data: {
//     users: 1234,
//     announcements: 56,
//     feedback: 89,
//     points: 12345
//   }
// }
```

### 获取公告列表
```javascript
const announcements = await callCloudFunction('getAnnouncementListAdmin', {
  type: 'all',
  status: 'published',
  page: 1,
  pageSize: 20
})
```

### 创建公告
```javascript
const result = await callCloudFunction('createAnnouncement', {
  title: '新公告',
  content: '公告内容',
  type: 'announcement',
  priority: 5
})
```

## 🎯 优势

### 1. 官方标准
- 使用微信官方推荐的调用方式
- 符合微信云开发最佳实践
- 更好的稳定性和兼容性

### 2. 用户友好
- 无需手动配置环境变量
- 图形化配置界面
- 本地保存配置信息

### 3. 安全可靠
- 配置信息本地存储
- Access Token 自动管理
- 完整的错误处理

### 4. 易于部署
- 支持打包为移动应用
- 配置信息随应用分发
- 无需服务器部署

## 🚀 下一步

1. **部署云函数**：按照部署指南部署管理端云函数
2. **配置应用**：在应用中填写微信相关信息
3. **测试功能**：验证所有管理功能是否正常
4. **打包分发**：使用Tauri打包为桌面或移动应用

现在您拥有了一个完全符合微信官方标准的后台管理系统！🎉
