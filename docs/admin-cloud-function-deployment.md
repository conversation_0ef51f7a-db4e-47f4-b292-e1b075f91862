# 管理端云函数部署指南

## 📋 部署前准备

### 1. 环境变量配置

在微信云开发控制台中配置以下环境变量：

```
ADMIN_SECRET_KEYS = "your-secret-key-1,your-secret-key-2,your-secret-key-3"
```

**重要说明：**
- 支持多个密钥，用逗号分隔
- 密钥应该足够复杂和安全
- 建议使用UUID或随机生成的长字符串
- 定期更换密钥以提高安全性

### 2. 生成安全密钥示例

```javascript
// 可以使用以下方式生成安全密钥
const crypto = require('crypto');

// 方法1：生成UUID风格的密钥
function generateUUID() {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c == 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

// 方法2：生成随机字符串
function generateRandomKey(length = 32) {
  return crypto.randomBytes(length).toString('hex');
}

// 示例密钥
const key1 = generateUUID(); // 例如：a1b2c3d4-e5f6-4789-a012-b3c4d5e6f789
const key2 = generateRandomKey(32); // 例如：a1b2c3d4e5f6789a012b3c4d5e6f789a012b3c4d5e6f789a012b3c4d5e6f789
```

## 🚀 部署步骤

### 1. 创建云函数

```bash
# 在项目根目录执行
cd cloudfunctions/cloud-functions-admin

# 安装依赖
npm install

# 上传并部署云函数
# 使用微信开发者工具或命令行工具
```

### 2. 配置HTTP触发器

在微信云开发控制台中：

1. 进入云函数管理
2. 找到 `cloud-functions-admin` 函数
3. 配置HTTP触发器：
   - 路径：`/admin`
   - 方法：`GET, POST`
   - 超时时间：60秒

### 3. 设置环境变量

在云函数设置中添加环境变量：
```
ADMIN_SECRET_KEYS = "your-generated-secret-keys"
```

## 🔧 API调用方式

### HTTP API调用格式

```javascript
// POST 请求到云函数HTTP触发器
const response = await fetch('https://your-cloud-function-url/admin', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    type: 'createAnnouncement',
    secretKey: 'your-secret-key',
    data: {
      title: '新公告标题',
      content: '公告内容',
      type: 'announcement'
    }
  })
});

const result = await response.json();
```

### 请求参数说明

```javascript
{
  "type": "API类型",           // 必填：要调用的API类型
  "secretKey": "访问密钥",     // 必填：管理员访问密钥
  "data": {                   // 可选：API参数
    // 具体的API参数
  }
}
```

### 响应格式

```javascript
// 成功响应
{
  "success": true,
  "message": "操作成功",
  "data": { /* 返回数据 */ },
  "timestamp": "2024-01-01T00:00:00.000Z"
}

// 错误响应
{
  "success": false,
  "message": "错误信息",
  "code": "ERROR_CODE",
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

## 📚 可用的管理API

### 公告管理
- `createAnnouncement` - 创建公告
- `updateAnnouncement` - 更新公告
- `deleteAnnouncement` - 删除公告
- `getAnnouncementListAdmin` - 获取公告列表
- `getAnnouncementStats` - 获取公告统计

### 配置管理
- `createConfig` - 创建配置
- `updateConfig` - 更新配置
- `deleteConfig` - 删除配置
- `getAllConfigsAdmin` - 获取所有配置
- `getConfigStats` - 获取配置统计

### 用户管理
- `getUserList` - 获取用户列表
- `getUserDetail` - 获取用户详情
- `updateUserStatus` - 更新用户状态
- `getUserStats` - 获取用户统计
- `exportUserData` - 导出用户数据

### 反馈管理
- `getFeedbackListAdmin` - 获取反馈列表
- `replyFeedback` - 回复反馈
- `updateFeedbackStatus` - 更新反馈状态
- `getFeedbackStatsAdmin` - 获取反馈统计
- `exportFeedbackData` - 导出反馈数据

### 其他管理功能
- 积分管理、商店管理、签到管理、数据管理等

## 🔒 安全注意事项

### 1. 密钥安全
- 不要在客户端代码中硬编码密钥
- 定期更换密钥
- 使用环境变量存储密钥
- 监控密钥使用情况

### 2. 访问控制
- 只在安全的环境中调用管理API
- 使用HTTPS进行通信
- 记录所有管理操作日志
- 定期审查访问日志

### 3. 错误处理
- 不要在错误信息中暴露敏感信息
- 统一的错误响应格式
- 详细的服务端日志记录

## 🧪 测试验证

### 1. 基础连通性测试

```javascript
// 测试API连通性
const testResponse = await fetch('https://your-cloud-function-url/admin', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    type: 'getSystemStats',
    secretKey: 'your-secret-key'
  })
});

console.log(await testResponse.json());
```

### 2. 密钥验证测试

```javascript
// 测试无效密钥
const invalidKeyResponse = await fetch('https://your-cloud-function-url/admin', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    type: 'getSystemStats',
    secretKey: 'invalid-key'
  })
});

// 应该返回认证失败错误
console.log(await invalidKeyResponse.json());
```

## 📊 监控和维护

### 1. 日志监控
- 查看云函数执行日志
- 监控API调用频率
- 关注错误率和响应时间

### 2. 性能优化
- 定期清理过期数据
- 优化数据库查询
- 监控内存和CPU使用

### 3. 安全审计
- 定期检查访问日志
- 监控异常访问模式
- 更新安全策略

## 🔄 版本更新

### 更新流程
1. 在开发环境测试新功能
2. 更新云函数代码
3. 重新部署云函数
4. 验证功能正常
5. 更新API文档

### 向后兼容
- 保持API接口向后兼容
- 新增功能使用新的API类型
- 逐步废弃旧接口
