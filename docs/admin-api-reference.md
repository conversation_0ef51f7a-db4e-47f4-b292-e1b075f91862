# 管理端API参考文档

## 🔐 认证方式

所有管理端API都需要SECRET_KEY验证：

```javascript
{
  "type": "API_NAME",
  "secretKey": "your-secret-key",
  "data": {
    // API参数
  }
}
```

## 📊 系统管理API

### 获取系统统计
```javascript
// 请求
{
  "type": "getSystemStats",
  "secretKey": "your-secret-key"
}

// 响应
{
  "success": true,
  "data": {
    "timestamp": "2024-01-01T00:00:00.000Z",
    "environment": "production",
    "version": "1.0.0",
    "uptime": 12345,
    "memory": {...}
  }
}
```

### 获取仪表板统计
```javascript
// 请求
{
  "type": "getDashboardStats",
  "secretKey": "your-secret-key",
  "data": {
    "period": "30d" // 7d, 30d, 90d, 1y, all
  }
}

// 响应
{
  "success": true,
  "data": {
    "announcements": 56,
    "configs": 12,
    "feedback": 89,
    "points": 12345,
    "users": 1234,
    "period": "30d",
    "generatedAt": "2024-01-01T00:00:00.000Z"
  }
}
```

### 获取系统健康状态
```javascript
// 请求
{
  "type": "getSystemHealth",
  "secretKey": "your-secret-key"
}

// 响应
{
  "success": true,
  "data": {
    "status": "healthy", // healthy, degraded, unhealthy
    "checks": {
      "database": "ok",
      "memory": "ok",
      "uptime": "ok"
    },
    "metrics": {...}
  }
}
```

## 📢 公告管理API

### 获取公告列表
```javascript
// 请求
{
  "type": "getAnnouncementListAdmin",
  "secretKey": "your-secret-key",
  "data": {
    "type": "all", // all, announcement, notice, update, maintenance, promotion
    "status": "all", // all, draft, published, archived
    "keyword": "",
    "page": 1,
    "pageSize": 20,
    "sortBy": "createTime",
    "sortOrder": "desc"
  }
}

// 响应
{
  "success": true,
  "data": [...],
  "pagination": {
    "total": 100,
    "page": 1,
    "pageSize": 20,
    "totalPages": 5,
    "hasNext": true,
    "hasPrev": false
  }
}
```

### 创建公告
```javascript
// 请求
{
  "type": "createAnnouncement",
  "secretKey": "your-secret-key",
  "data": {
    "title": "公告标题",
    "content": "公告内容",
    "type": "announcement",
    "priority": 5,
    "status": "published",
    "isVisible": true,
    "expiryTime": null
  }
}
```

### 更新公告
```javascript
// 请求
{
  "type": "updateAnnouncement",
  "secretKey": "your-secret-key",
  "data": {
    "id": "announcement_id",
    "title": "新标题",
    "content": "新内容",
    "status": "published"
  }
}
```

### 删除公告
```javascript
// 请求
{
  "type": "deleteAnnouncement",
  "secretKey": "your-secret-key",
  "data": {
    "id": "announcement_id"
  }
}
```

### 获取公告统计
```javascript
// 请求
{
  "type": "getAnnouncementStats",
  "secretKey": "your-secret-key",
  "data": {
    "period": "30d"
  }
}

// 响应
{
  "success": true,
  "data": {
    "total": 56,
    "published": 45,
    "draft": 11,
    "period": "30d"
  }
}
```

## ⚙️ 配置管理API

### 获取配置列表
```javascript
// 请求
{
  "type": "getAllConfigsAdmin",
  "secretKey": "your-secret-key",
  "data": {
    "category": "all", // all, general, system, user
    "enable": "all", // all, true, false
    "keyword": "",
    "page": 1,
    "pageSize": 50
  }
}
```

### 创建配置
```javascript
// 请求
{
  "type": "createConfig",
  "secretKey": "your-secret-key",
  "data": {
    "key": "config_key",
    "value": "config_value",
    "description": "配置描述",
    "category": "general",
    "dataType": "string",
    "enable": true
  }
}
```

### 更新配置
```javascript
// 请求
{
  "type": "updateConfig",
  "secretKey": "your-secret-key",
  "data": {
    "key": "config_key",
    "value": "new_value",
    "description": "新描述"
  }
}
```

### 删除配置
```javascript
// 请求
{
  "type": "deleteConfig",
  "secretKey": "your-secret-key",
  "data": {
    "key": "config_key"
  }
}
```

## 👥 用户管理API

### 获取用户列表
```javascript
// 请求
{
  "type": "getUserList",
  "secretKey": "your-secret-key",
  "data": {
    "status": "all", // all, active, inactive
    "vipStatus": "all", // all, true, false
    "keyword": "",
    "page": 1,
    "pageSize": 20
  }
}
```

### 获取用户详情
```javascript
// 请求
{
  "type": "getUserDetail",
  "secretKey": "your-secret-key",
  "data": {
    "userId": "user_id"
  }
}
```

### 更新用户状态
```javascript
// 请求
{
  "type": "updateUserStatus",
  "secretKey": "your-secret-key",
  "data": {
    "userId": "user_id",
    "status": "active", // active, inactive, banned
    "reason": "更新原因"
  }
}
```

### 获取用户统计
```javascript
// 请求
{
  "type": "getUserStats",
  "secretKey": "your-secret-key",
  "data": {
    "period": "30d"
  }
}

// 响应
{
  "success": true,
  "data": {
    "total": 1234,
    "active": 1100,
    "inactive": 134,
    "vip": 56,
    "period": "30d"
  }
}
```

## 💬 反馈管理API

### 获取反馈列表
```javascript
// 请求
{
  "type": "getFeedbackListAdmin",
  "secretKey": "your-secret-key",
  "data": {
    "status": "all", // all, pending, processing, replied, resolved, closed
    "type": "all", // all, bug, feature, complaint, suggestion
    "keyword": "",
    "page": 1,
    "pageSize": 20
  }
}
```

### 回复反馈
```javascript
// 请求
{
  "type": "replyFeedback",
  "secretKey": "your-secret-key",
  "data": {
    "id": "feedback_id",
    "reply": "回复内容",
    "adminId": "admin_id"
  }
}
```

### 更新反馈状态
```javascript
// 请求
{
  "type": "updateFeedbackStatus",
  "secretKey": "your-secret-key",
  "data": {
    "id": "feedback_id",
    "status": "resolved",
    "priority": 3,
    "tags": ["bug", "urgent"]
  }
}
```

## 🪙 积分管理API

### 获取积分统计
```javascript
// 请求
{
  "type": "getPointsStatsAdmin",
  "secretKey": "your-secret-key",
  "data": {
    "period": "30d"
  }
}

// 响应
{
  "success": true,
  "data": {
    "total": 100,
    "totalPoints": 12345,
    "earnedPoints": 10000,
    "spentPoints": 2345,
    "period": "30d"
  }
}
```

### 手动调整用户积分
```javascript
// 请求
{
  "type": "adjustUserPoints",
  "secretKey": "your-secret-key",
  "data": {
    "userId": "user_id",
    "points": 100, // 正数为增加，负数为扣除
    "reason": "管理员调整",
    "adminId": "admin_id"
  }
}
```

### 获取积分记录
```javascript
// 请求
{
  "type": "getPointsRecordsAdmin",
  "secretKey": "your-secret-key",
  "data": {
    "userId": "all", // all 或具体用户ID
    "type": "all", // all, earn, spend, admin_add, admin_deduct
    "page": 1,
    "pageSize": 20
  }
}
```

## 📤 数据导出API

### 导出反馈数据
```javascript
// 请求
{
  "type": "exportFeedbackData",
  "secretKey": "your-secret-key",
  "data": {
    "format": "json", // json, csv
    "status": "all",
    "startDate": "2024-01-01",
    "endDate": "2024-01-31",
    "limit": 1000
  }
}
```

### 导出积分数据
```javascript
// 请求
{
  "type": "exportPointsData",
  "secretKey": "your-secret-key",
  "data": {
    "format": "json",
    "userId": "all",
    "type": "all",
    "limit": 1000
  }
}
```

## 🔧 批量操作API

### 批量操作反馈
```javascript
// 请求
{
  "type": "batchOperateFeedback",
  "secretKey": "your-secret-key",
  "data": {
    "ids": ["id1", "id2", "id3"],
    "action": "updateStatus", // updateStatus, delete, archive
    "status": "resolved" // 当action为updateStatus时需要
  }
}
```

### 批量调整积分
```javascript
// 请求
{
  "type": "batchAdjustPoints",
  "secretKey": "your-secret-key",
  "data": {
    "adjustments": [
      {"userId": "user1", "points": 100},
      {"userId": "user2", "points": -50}
    ],
    "reason": "批量调整",
    "adminId": "admin_id"
  }
}
```

## 📋 响应格式

### 成功响应
```javascript
{
  "success": true,
  "message": "操作成功",
  "data": {...},
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

### 错误响应
```javascript
{
  "success": false,
  "message": "错误描述",
  "code": "ERROR_CODE",
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

### 分页响应
```javascript
{
  "success": true,
  "data": [...],
  "pagination": {
    "total": 100,
    "page": 1,
    "pageSize": 20,
    "totalPages": 5,
    "hasNext": true,
    "hasPrev": false
  }
}
```

## 🛍️ 商店管理API

### 创建商品
```javascript
// 请求
{
  "type": "createStoreItem",
  "secretKey": "your-secret-key",
  "data": {
    "name": "商品名称",
    "description": "商品描述",
    "price": 100,
    "originalPrice": 150,
    "category": "virtual",
    "type": "virtual",
    "stock": 100,
    "images": ["image1.jpg"],
    "tags": ["热门", "推荐"]
  }
}
```

### 获取商品列表
```javascript
// 请求
{
  "type": "getStoreItemListAdmin",
  "secretKey": "your-secret-key",
  "data": {
    "category": "all",
    "status": "all",
    "keyword": "",
    "page": 1,
    "pageSize": 20
  }
}
```

### 创建兑换码
```javascript
// 请求
{
  "type": "createRedemptionCode",
  "secretKey": "your-secret-key",
  "data": {
    "code": "GIFT2024",
    "type": "points", // points, item, discount
    "value": 100,
    "description": "新年礼品码",
    "maxUses": 100,
    "expiryTime": "2024-12-31T23:59:59.000Z"
  }
}
```

### 获取兑换码列表
```javascript
// 请求
{
  "type": "getRedemptionCodeListAdmin",
  "secretKey": "your-secret-key",
  "data": {
    "status": "all",
    "type": "all",
    "keyword": "",
    "page": 1,
    "pageSize": 20
  }
}
```

## ✅ 签到管理API

### 获取签到统计
```javascript
// 请求
{
  "type": "getCheckInStatsAdmin",
  "secretKey": "your-secret-key",
  "data": {
    "period": "30d"
  }
}

// 响应
{
  "success": true,
  "data": {
    "total": 1500,
    "todayCount": 45,
    "averageDaily": 50,
    "period": "30d"
  }
}
```

### 获取用户签到历史
```javascript
// 请求
{
  "type": "getUserCheckInHistoryAdmin",
  "secretKey": "your-secret-key",
  "data": {
    "userId": "all", // all 或具体用户ID
    "startDate": "2024-01-01",
    "endDate": "2024-01-31",
    "page": 1,
    "pageSize": 20
  }
}
```

### 导出签到数据
```javascript
// 请求
{
  "type": "exportCheckInData",
  "secretKey": "your-secret-key",
  "data": {
    "format": "json",
    "userId": "all",
    "startDate": "2024-01-01",
    "endDate": "2024-01-31",
    "limit": 1000
  }
}
```

## 📊 用户数据管理API

### 获取所有用户数据
```javascript
// 请求
{
  "type": "getAllUserDataAdmin",
  "secretKey": "your-secret-key",
  "data": {
    "dataType": "all", // all, profile, settings, activity
    "userId": "all",
    "keyword": "",
    "page": 1,
    "pageSize": 20
  }
}
```

### 获取用户数据统计
```javascript
// 请求
{
  "type": "getUserDataStatsAdmin",
  "secretKey": "your-secret-key",
  "data": {
    "period": "30d"
  }
}

// 响应
{
  "success": true,
  "data": {
    "total": 5000,
    "todayCount": 120,
    "averageDaily": 167,
    "period": "30d"
  }
}
```

### 清理用户数据
```javascript
// 请求
{
  "type": "cleanupUserDataAdmin",
  "secretKey": "your-secret-key",
  "data": {
    "dataType": "all",
    "olderThan": 90, // 清理90天前的数据
    "dryRun": true // 试运行，不实际删除
  }
}

// 响应
{
  "success": true,
  "data": {
    "dryRun": true,
    "wouldDelete": 150,
    "cleanupDate": "2023-10-01T00:00:00.000Z"
  }
}
```

### 导出所有数据
```javascript
// 请求
{
  "type": "exportAllDataAdmin",
  "secretKey": "your-secret-key",
  "data": {
    "format": "json",
    "dataType": "all",
    "startDate": "2024-01-01",
    "endDate": "2024-01-31",
    "limit": 1000
  }
}
```

## 🚨 错误代码

- `INVALID_SECRET_KEY`: 无效的访问密钥
- `INVALID_PARAM`: 参数错误
- `NOT_FOUND`: 资源不存在
- `PERMISSION_DENIED`: 权限不足
- `SERVER_ERROR`: 服务器内部错误
- `RATE_LIMITED`: 请求过于频繁
- `UNSUPPORTED_API`: 不支持的API类型

## 📈 API统计

**总计：40+ 个管理端API接口**

- 📊 系统管理API: 6个
- 📢 公告管理API: 5个
- ⚙️ 配置管理API: 4个
- 👥 用户管理API: 4个
- 💬 反馈管理API: 3个
- 🪙 积分管理API: 8个
- 🛍️ 商店管理API: 4个
- ✅ 签到管理API: 3个
- 📊 用户数据管理API: 4个
- 📤 数据导出API: 多个
- 🔧 批量操作API: 多个
