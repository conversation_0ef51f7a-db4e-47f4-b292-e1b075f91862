# 部署指南

## 🚀 管理端云函数部署

### 1. 准备工作

#### 生成安全密钥
```bash
# 方法1：使用Node.js生成
node -e "console.log(require('crypto').randomBytes(32).toString('hex'))"

# 方法2：使用在线工具
# 访问 https://www.uuidgenerator.net/ 生成UUID

# 示例密钥（请勿在生产环境使用）
# a1b2c3d4e5f6789a012b3c4d5e6f789a012b3c4d5e6f789a012b3c4d5e6f789a
```

#### 配置环境变量
在微信云开发控制台中配置：
```
ADMIN_SECRET_KEYS = "your-secret-key-1,your-secret-key-2,your-secret-key-3"
```

### 2. 部署步骤

#### 使用部署脚本（推荐）

```bash
# 运行部署脚本
chmod +x scripts/deploy-admin.sh
./scripts/deploy-admin.sh
```

#### 手动部署步骤

1. **安装公共模块依赖**
   ```bash
   cd cloudfunctions/cloud-functions-admin
   npm install
   ```

2. **使用微信开发者工具部署**
   - 打开微信开发者工具
   - 导入项目：选择项目根目录
   - 选择云函数：在左侧文件树中找到 `cloudfunctions/cloud-functions-admin`
   - 右键点击：选择"上传并部署：云端安装依赖"
   - 等待部署完成

3. **验证公共模块**
   ```bash
   # 在云函数目录中测试模块
   cd cloudfunctions/cloud-functions-admin
   node test-modules.js
   ```

#### 使用命令行部署（如果支持）

```bash
# 进入云函数目录
cd cloudfunctions/cloud-functions-admin

# 安装依赖
npm install

# 使用微信云开发CLI部署（需要先安装和配置）
# npm install -g @cloudbase/cli
# tcb login
# tcb functions:deploy cloud-functions-admin
```

### 3. 配置HTTP触发器

1. **进入云开发控制台**
2. **选择云函数**：找到 `cloud-functions-admin`
3. **配置触发器**：
   - 触发方式：HTTP触发
   - 路径：`/admin`
   - 方法：`GET, POST`
   - 超时时间：60秒

4. **获取触发器URL**：
   - 复制HTTP触发器URL
   - 格式类似：`https://xxx-xxx.service.tcloudbase.com/admin`

### 4. 更新前端配置

编辑 `admin-app/.env` 文件：
```env
# 更新为实际的云函数URL
VITE_API_BASE_URL=https://your-cloud-function-url/admin

# 更新为实际的密钥
VITE_ADMIN_SECRET_KEY=your-secret-key
```

### 5. 测试部署

#### 测试API连通性
```bash
# 使用curl测试
curl -X POST "https://your-cloud-function-url/admin" \
  -H "Content-Type: application/json" \
  -d '{
    "type": "getSystemStats",
    "secretKey": "your-secret-key"
  }'

# 预期响应
{
  "success": true,
  "message": "获取系统统计成功",
  "data": {
    "timestamp": "2024-01-01T00:00:00.000Z",
    "environment": "production",
    "version": "1.0.0"
  },
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

#### 测试前端连接
1. 启动前端应用：`pnpm dev`
2. 访问登录页面：`http://localhost:1420/login`
3. 查看API连接状态（应该显示绿色"正常"）
4. 使用测试账号登录：`admin` / `admin123`
5. 查看仪表板数据是否正常加载

## 🔧 故障排除

### 常见问题

#### 1. API连接失败
**症状**：前端显示"API连接失败"
**解决方案**：
- 检查云函数是否部署成功
- 检查HTTP触发器是否配置正确
- 检查环境变量是否设置
- 检查网络连接

#### 2. 密钥验证失败
**症状**：返回"无效的访问密钥"错误
**解决方案**：
- 检查前端配置的密钥是否正确
- 检查云函数环境变量中的密钥配置
- 确保密钥没有多余的空格或特殊字符

#### 3. 云函数超时
**症状**：请求超时或返回超时错误
**解决方案**：
- 增加云函数超时时间（最大60秒）
- 优化数据库查询性能
- 检查数据库连接是否正常

#### 4. 数据库连接失败
**症状**：返回数据库相关错误
**解决方案**：
- 检查云数据库是否正常运行
- 检查云函数权限配置
- 检查数据库集合是否存在

### 调试方法

#### 查看云函数日志
1. 进入云开发控制台
2. 选择"云函数"
3. 点击函数名称
4. 查看"调用日志"

#### 本地调试
```bash
# 在云函数目录中
cd cloudfunctions/cloud-functions-admin

# 创建测试文件
cat > test.js << 'EOF'
const { main } = require('./index.js')

async function test() {
  const event = {
    type: 'getSystemStats',
    secretKey: 'your-secret-key'
  }
  
  const result = await main(event, {})
  console.log('Result:', JSON.stringify(result, null, 2))
}

test().catch(console.error)
EOF

# 运行测试
node test.js
```

## 📊 监控和维护

### 性能监控
- 监控云函数调用次数和响应时间
- 监控数据库查询性能
- 监控错误率和异常情况

### 安全维护
- 定期更换SECRET_KEY
- 监控异常访问模式
- 定期检查访问日志

### 数据备份
- 定期备份云数据库
- 备份重要配置信息
- 制定灾难恢复计划

## 🔄 更新部署

### 更新云函数
1. 修改代码
2. 重新部署云函数
3. 测试新功能
4. 监控运行状态

### 更新前端
1. 修改前端代码
2. 构建生产版本：`pnpm build`
3. 部署到服务器或使用Tauri打包桌面应用

### 版本管理
- 使用Git管理代码版本
- 为每个发布版本打标签
- 记录变更日志

## 📞 技术支持

如果遇到部署问题：
1. 检查本文档的故障排除部分
2. 查看云函数和前端的错误日志
3. 确认所有配置步骤都已正确完成
4. 测试API连通性和数据流

记住：部署成功后，您就拥有了一个完整的、安全的后台管理系统！
