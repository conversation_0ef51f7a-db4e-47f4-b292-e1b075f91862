# 云函数架构重构设计方案

## 🎯 目标

1. 分离用户端和管理端接口
2. 实现SECRET_KEY验证机制
3. 为所有模块添加完整的管理接口
4. 保持向后兼容性

## 🏗️ 新架构设计

### 1. 双入口架构

```
cloudfunctions/
├── cloud-functions/           # 用户端云函数（小程序调用）
│   ├── index.js              # 用户端入口
│   ├── api/                  # 用户端API
│   └── ...
└── cloud-functions-admin/    # 管理端云函数（HTTP API调用）
    ├── index.js              # 管理端入口
    ├── middleware/           # 中间件
    │   └── auth.js          # SECRET_KEY验证
    ├── api/                 # 管理端API
    └── ...
```

### 2. SECRET_KEY验证机制

#### 环境变量配置
```javascript
// 在云函数环境变量中配置
ADMIN_SECRET_KEYS = "key1,key2,key3"  // 支持多个密钥
```

#### 验证中间件
```javascript
// middleware/auth.js
function validateSecretKey(event) {
  const { secretKey } = event
  const validKeys = process.env.ADMIN_SECRET_KEYS?.split(',') || []
  
  if (!secretKey || !validKeys.includes(secretKey)) {
    throw new Error('无效的访问密钥')
  }
}
```

### 3. 管理端API设计

#### 3.1 公告管理 (announcements-admin.js)
- `createAnnouncement` - 创建公告
- `updateAnnouncement` - 更新公告
- `deleteAnnouncement` - 删除公告
- `getAnnouncementList` - 获取公告列表（管理端）
- `getAnnouncementStats` - 获取公告统计

#### 3.2 配置管理 (config-admin.js)
- `createConfig` - 创建配置
- `updateConfig` - 更新配置
- `deleteConfig` - 删除配置
- `getAllConfigs` - 获取所有配置（包括禁用的）
- `getConfigStats` - 获取配置统计

#### 3.3 用户管理 (user-admin.js)
- `getUserList` - 获取用户列表
- `getUserDetail` - 获取用户详情
- `updateUserStatus` - 更新用户状态
- `getUserStats` - 获取用户统计
- `exportUserData` - 导出用户数据

#### 3.4 反馈管理 (feedback-admin.js)
- `getFeedbackList` - 获取所有反馈
- `replyFeedback` - 回复反馈
- `updateFeedbackStatus` - 更新反馈状态
- `getFeedbackStats` - 获取反馈统计
- `exportFeedbackData` - 导出反馈数据

#### 3.5 积分管理 (points-admin.js)
- `getPointsStats` - 获取积分统计
- `adjustUserPoints` - 调整用户积分
- `getPointsRecords` - 获取积分记录（全局）
- `exportPointsData` - 导出积分数据

#### 3.6 商店管理 (store-admin.js)
- `createStoreItem` - 创建商品
- `updateStoreItem` - 更新商品
- `deleteStoreItem` - 删除商品
- `getStoreItemList` - 获取商品列表
- `getStoreStats` - 获取商店统计
- `createRedemptionCode` - 创建兑换码
- `getRedemptionCodeList` - 获取兑换码列表

#### 3.7 签到管理 (check-in-admin.js)
- `getCheckInStats` - 获取签到统计
- `getUserCheckInHistory` - 获取用户签到历史
- `exportCheckInData` - 导出签到数据

#### 3.8 数据管理 (user-data-admin.js)
- `getAllUserData` - 获取所有用户数据
- `getUserDataStats` - 获取数据统计
- `cleanupUserData` - 清理用户数据
- `exportAllData` - 导出所有数据

### 4. 统一响应格式

```javascript
// 成功响应
{
  success: true,
  message: "操作成功",
  data: {...},
  timestamp: "2024-01-01T00:00:00.000Z"
}

// 错误响应
{
  success: false,
  message: "错误信息",
  code: "ERROR_CODE",
  timestamp: "2024-01-01T00:00:00.000Z"
}
```

### 5. 部署策略

#### 阶段1：创建管理端云函数
1. 创建新的云函数 `cloud-functions-admin`
2. 实现SECRET_KEY验证中间件
3. 复制共享的数据库操作层

#### 阶段2：实现管理接口
1. 为每个模块实现完整的管理接口
2. 添加数据验证和权限检查
3. 实现统计和导出功能

#### 阶段3：测试和优化
1. 单元测试和集成测试
2. 性能优化
3. 文档完善

#### 阶段4：后台管理应用开发
1. 开发Tauri应用
2. 实现HTTP API调用
3. 开发管理界面

## 🔧 技术实现细节

### SECRET_KEY验证流程
```javascript
exports.main = async (event, context) => {
  try {
    // 1. 验证SECRET_KEY
    validateSecretKey(event)
    
    // 2. 路由到具体API
    const { type, data } = event
    return await routeToApi(type, data)
    
  } catch (error) {
    return {
      success: false,
      message: error.message,
      code: 'AUTH_ERROR'
    }
  }
}
```

### 共享数据库层
- 复用现有的数据库操作类
- 确保数据一致性
- 添加管理端特有的查询方法

### 错误处理
- 统一的错误处理机制
- 详细的错误日志
- 安全的错误信息返回

## 📊 预期收益

1. **安全性提升**：SECRET_KEY验证保护管理接口
2. **功能完整**：所有模块都有完整的管理功能
3. **架构清晰**：用户端和管理端分离
4. **易于维护**：模块化设计，便于扩展
5. **向后兼容**：不影响现有小程序功能
