# API模块重构计划

## 🎯 重构目标

1. 保持用户端API不变，确保小程序正常运行
2. 为管理端创建完整的CRUD接口
3. 统一响应格式和错误处理
4. 添加完善的参数验证

## 📋 重构清单

### ✅ 已完成
- [x] SECRET_KEY验证中间件
- [x] 管理端云函数框架
- [x] 公告管理API示例

### 🔄 需要重构的模块

#### 1. 配置管理 (config-admin.js)
**现状：** 只有 `getAllConfigs` 用户端接口
**需要添加：**
- `createConfig` - 创建配置
- `updateConfig` - 更新配置
- `deleteConfig` - 删除配置
- `getAllConfigsAdmin` - 管理端获取所有配置（包括禁用的）
- `getConfigStats` - 配置统计

#### 2. 用户管理 (user-admin.js)
**现状：** 只有用户自己的信息管理
**需要添加：**
- `getUserList` - 获取用户列表
- `getUserDetail` - 获取用户详情
- `updateUserStatus` - 更新用户状态（VIP、管理员等）
- `getUserStats` - 用户统计
- `exportUserData` - 导出用户数据

#### 3. 反馈管理 (feedback-admin.js)
**现状：** 用户只能管理自己的反馈
**需要添加：**
- `getFeedbackListAdmin` - 获取所有反馈
- `replyFeedback` - 回复反馈
- `updateFeedbackStatus` - 更新反馈状态
- `getFeedbackStatsAdmin` - 反馈统计
- `exportFeedbackData` - 导出反馈数据

#### 4. 积分管理 (points-admin.js)
**现状：** 只有用户端的积分查询
**需要添加：**
- `getPointsStatsAdmin` - 全局积分统计
- `adjustUserPoints` - 手动调整用户积分
- `getPointsRecordsAdmin` - 获取所有积分记录
- `exportPointsData` - 导出积分数据

#### 5. 商店管理 (store-admin.js)
**现状：** 只有 `initializeStoreItems` 管理接口
**需要添加：**
- `createStoreItem` - 创建商品
- `updateStoreItem` - 更新商品
- `deleteStoreItem` - 删除商品
- `getStoreItemListAdmin` - 管理端商品列表
- `getStoreStatsAdmin` - 商店统计
- `createRedemptionCode` - 创建兑换码
- `getRedemptionCodeListAdmin` - 兑换码列表

#### 6. 签到管理 (check-in-admin.js)
**现状：** 只有用户端签到功能
**需要添加：**
- `getCheckInStatsAdmin` - 签到统计
- `getUserCheckInHistoryAdmin` - 用户签到历史（管理端）
- `exportCheckInData` - 导出签到数据

#### 7. 数据管理 (user-data-admin.js)
**现状：** 用户只能管理自己的数据
**需要添加：**
- `getAllUserDataAdmin` - 获取所有用户数据
- `getUserDataStatsAdmin` - 数据统计
- `cleanupUserDataAdmin` - 清理数据
- `exportAllDataAdmin` - 导出所有数据

#### 8. 友情应用管理
**现状：** 已有完整的管理接口
**需要做：** 复用现有接口，确保在管理端云函数中可用

#### 9. 摸鱼状态管理
**现状：** 只有 `cleanupExpiredFishingStatus` 管理接口
**需要添加：**
- `getFishingStatsAdmin` - 摸鱼统计

## 🔧 重构实施步骤

### 阶段1：数据库层扩展
为每个数据库操作类添加管理端需要的方法：

```javascript
// 例如：announcements.js
class AnnouncementsDB extends BaseDB {
  // 现有方法...
  
  // 新增管理端方法
  async getAnnouncementsAdmin(options) { /* 管理端查询 */ }
  async getAnnouncementStats(period) { /* 统计信息 */ }
  async updateAnnouncement(id, data) { /* 更新公告 */ }
  async deleteAnnouncement(id) { /* 删除公告 */ }
}
```

### 阶段2：管理端API实现
为每个模块创建对应的管理端API文件：

```
cloudfunctions/cloud-functions-admin/api/
├── announcements-admin.js  ✅ 已完成
├── config-admin.js         🔄 待实现
├── user-admin.js           🔄 待实现
├── feedback-admin.js       🔄 待实现
├── points-admin.js         🔄 待实现
├── store-admin.js          🔄 待实现
├── check-in-admin.js       🔄 待实现
└── user-data-admin.js      🔄 待实现
```

### 阶段3：统一响应格式
确保所有API使用统一的响应格式：

```javascript
// 成功响应
{
  success: true,
  message: "操作成功",
  data: {...},
  timestamp: "2024-01-01T00:00:00.000Z"
}

// 分页响应
{
  success: true,
  message: "查询成功",
  data: [...],
  pagination: {
    total: 100,
    page: 1,
    pageSize: 20,
    totalPages: 5,
    hasNext: true,
    hasPrev: false
  },
  timestamp: "2024-01-01T00:00:00.000Z"
}

// 统计响应
{
  success: true,
  message: "统计数据获取成功",
  data: {...},
  stats: {
    generatedAt: "2024-01-01T00:00:00.000Z",
    period: "30d",
    metrics: ["total", "active", "growth"]
  },
  timestamp: "2024-01-01T00:00:00.000Z"
}
```

### 阶段4：参数验证标准化
为所有API添加统一的参数验证：

```javascript
// 分页参数验证
const paginationValidation = validatePagination(params, 100)
if (!paginationValidation.success) {
  return paginationValidation
}

// 排序参数验证
const sortingValidation = validateSorting(params, ['createTime', 'updateTime', 'title'])
if (!sortingValidation.success) {
  return sortingValidation
}

// 日期范围验证
const dateValidation = validateDateRange(params)
if (!dateValidation.success) {
  return dateValidation
}
```

## 📊 数据库扩展需求

### 1. 公告表 (announcements)
```javascript
// 需要添加的字段和索引
{
  // 现有字段...
  status: 'published', // draft, published, archived, deleted
  priority: 5,         // 1-10 优先级
  expiryTime: null,    // 过期时间
  deletedAt: null,     // 软删除时间
  
  // 索引
  indexes: [
    { status: 1, createTime: -1 },
    { type: 1, status: 1 },
    { priority: -1, createTime: -1 }
  ]
}
```

### 2. 配置表 (config)
```javascript
// 需要添加的字段
{
  // 现有字段...
  description: '',     // 配置描述
  category: 'general', // 配置分类
  dataType: 'string',  // 数据类型
  validation: null,    // 验证规则
  
  // 索引
  indexes: [
    { category: 1, enable: 1 },
    { key: 1 } // 唯一索引
  ]
}
```

### 3. 用户表 (users)
```javascript
// 需要添加的字段
{
  // 现有字段...
  status: 'active',    // active, inactive, banned
  lastLoginTime: null, // 最后登录时间
  loginCount: 0,       // 登录次数
  tags: [],           // 用户标签
  
  // 索引
  indexes: [
    { status: 1, createTime: -1 },
    { 'vip.status': 1, createTime: -1 },
    { isAdmin: 1 }
  ]
}
```

## 🧪 测试计划

### 1. 单元测试
- 每个管理端API的参数验证
- 数据库操作的正确性
- 错误处理的完整性

### 2. 集成测试
- 完整的API调用流程
- SECRET_KEY验证
- 权限控制

### 3. 性能测试
- 大数据量查询性能
- 并发访问测试
- 内存使用监控

## 📈 预期收益

1. **功能完整性**：所有模块都有完整的管理功能
2. **安全性提升**：SECRET_KEY验证保护管理接口
3. **易于维护**：统一的代码结构和响应格式
4. **扩展性强**：模块化设计便于添加新功能
5. **向后兼容**：不影响现有小程序功能
