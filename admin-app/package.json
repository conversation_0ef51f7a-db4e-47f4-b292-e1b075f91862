{"name": "admin-app", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "tauri": "tauri"}, "dependencies": {"@element-plus/icons-vue": "^2.3.2", "@tauri-apps/api": "^2", "@tauri-apps/plugin-http": "^2.5.1", "@tauri-apps/plugin-opener": "^2", "@vueup/vue-quill": "^1.2.0", "axios": "^1.11.0", "dayjs": "^1.11.13", "echarts": "^6.0.0", "element-plus": "^2.10.7", "highlight.js": "^11.11.1", "lodash-es": "^4.17.21", "lucide-vue-next": "^0.539.0", "marked": "^16.1.2", "pinia": "^3.0.3", "vue": "^3.5.13", "vue-countup-v3": "^1.4.2", "vue-echarts": "^7.0.3", "vue-router": "^4.5.1"}, "devDependencies": {"@tauri-apps/cli": "^2", "@types/lodash-es": "^4.17.12", "@vitejs/plugin-vue": "^5.2.1", "vite": "^6.0.3"}}