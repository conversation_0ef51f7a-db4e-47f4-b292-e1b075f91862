/* 响应式布局样式 */

/* 断点定义 */
:root {
  --breakpoint-xs: 480px;
  --breakpoint-sm: 768px;
  --breakpoint-md: 1024px;
  --breakpoint-lg: 1280px;
  --breakpoint-xl: 1536px;
}

/* 基础响应式工具类 */
.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 16px;
}

@media (min-width: 768px) {
  .container {
    padding: 0 24px;
  }
}

@media (min-width: 1024px) {
  .container {
    padding: 0 32px;
  }
}

/* 网格系统 */
.grid {
  display: grid;
  gap: 16px;
}

.grid-cols-1 {
  grid-template-columns: repeat(1, 1fr);
}

.grid-cols-2 {
  grid-template-columns: repeat(2, 1fr);
}

.grid-cols-3 {
  grid-template-columns: repeat(3, 1fr);
}

.grid-cols-4 {
  grid-template-columns: repeat(4, 1fr);
}

/* 响应式网格 */
@media (max-width: 767px) {
  .grid-cols-2,
  .grid-cols-3,
  .grid-cols-4 {
    grid-template-columns: 1fr;
  }
  
  .grid-cols-2.mobile-2 {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 768px) and (max-width: 1023px) {
  .grid-cols-3,
  .grid-cols-4 {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .grid-cols-4.lg-3 {
    grid-template-columns: repeat(3, 1fr);
  }
}

/* 弹性布局 */
.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.flex-wrap {
  flex-wrap: wrap;
}

.items-center {
  align-items: center;
}

.justify-between {
  justify-content: space-between;
}

.justify-center {
  justify-content: center;
}

.gap-2 {
  gap: 8px;
}

.gap-4 {
  gap: 16px;
}

.gap-6 {
  gap: 24px;
}

/* 间距工具类 */
.p-2 {
  padding: 8px;
}

.p-4 {
  padding: 16px;
}

.p-6 {
  padding: 24px;
}

.px-4 {
  padding-left: 16px;
  padding-right: 16px;
}

.py-2 {
  padding-top: 8px;
  padding-bottom: 8px;
}

.m-2 {
  margin: 8px;
}

.m-4 {
  margin: 16px;
}

.mb-4 {
  margin-bottom: 16px;
}

.mt-4 {
  margin-top: 16px;
}

/* 响应式间距 */
@media (max-width: 767px) {
  .mobile-p-2 {
    padding: 8px;
  }
  
  .mobile-p-4 {
    padding: 16px;
  }
  
  .mobile-gap-2 {
    gap: 8px;
  }
}

/* 显示/隐藏工具类 */
.hidden {
  display: none;
}

.block {
  display: block;
}

.inline-block {
  display: inline-block;
}

/* 响应式显示 */
@media (max-width: 767px) {
  .mobile-hidden {
    display: none !important;
  }
  
  .mobile-block {
    display: block !important;
  }
}

@media (min-width: 768px) {
  .desktop-hidden {
    display: none !important;
  }
  
  .desktop-block {
    display: block !important;
  }
}

/* 文字大小 */
.text-xs {
  font-size: 12px;
}

.text-sm {
  font-size: 14px;
}

.text-base {
  font-size: 16px;
}

.text-lg {
  font-size: 18px;
}

.text-xl {
  font-size: 20px;
}

.text-2xl {
  font-size: 24px;
}

/* 响应式文字 */
@media (max-width: 767px) {
  .mobile-text-sm {
    font-size: 14px;
  }
  
  .mobile-text-base {
    font-size: 16px;
  }
}

/* 宽度工具类 */
.w-full {
  width: 100%;
}

.w-auto {
  width: auto;
}

.max-w-full {
  max-width: 100%;
}

/* 响应式宽度 */
@media (max-width: 767px) {
  .mobile-w-full {
    width: 100% !important;
  }
}

/* 高度工具类 */
.h-auto {
  height: auto;
}

.min-h-screen {
  min-height: 100vh;
}

/* 溢出处理 */
.overflow-hidden {
  overflow: hidden;
}

.overflow-auto {
  overflow: auto;
}

.overflow-x-auto {
  overflow-x: auto;
}

/* 表格响应式 */
.table-responsive {
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}

@media (max-width: 767px) {
  .table-responsive table {
    min-width: 600px;
  }
}

/* 卡片响应式 */
.card-responsive {
  background: white;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

@media (min-width: 768px) {
  .card-responsive {
    padding: 24px;
    border-radius: 12px;
  }
}

/* 按钮响应式 */
.btn-responsive {
  padding: 8px 16px;
  font-size: 14px;
  border-radius: 6px;
  transition: all 0.2s;
}

@media (max-width: 767px) {
  .btn-responsive {
    padding: 12px 20px;
    font-size: 16px;
    min-height: 44px; /* 触摸友好的最小高度 */
  }
}

/* 表单响应式 */
.form-responsive .el-form-item {
  margin-bottom: 16px;
}

@media (max-width: 767px) {
  .form-responsive .el-form-item {
    margin-bottom: 20px;
  }
  
  .form-responsive .el-form-item__label {
    font-size: 16px;
    margin-bottom: 8px;
  }
  
  .form-responsive .el-input__inner,
  .form-responsive .el-textarea__inner {
    font-size: 16px; /* 防止iOS缩放 */
    padding: 12px;
  }
}

/* 对话框响应式 */
@media (max-width: 767px) {
  .el-dialog {
    width: 95% !important;
    margin: 0 auto !important;
  }
  
  .el-dialog__header {
    padding: 16px 20px 8px !important;
  }
  
  .el-dialog__body {
    padding: 8px 20px 16px !important;
  }
  
  .el-dialog__footer {
    padding: 8px 20px 16px !important;
  }
}

/* 抽屉响应式 */
@media (max-width: 767px) {
  .el-drawer {
    width: 100% !important;
  }
}

/* 导航响应式 */
.nav-responsive {
  display: flex;
  align-items: center;
  gap: 16px;
}

@media (max-width: 767px) {
  .nav-responsive {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }
}

/* 统计卡片响应式 */
.stats-responsive {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
}

@media (max-width: 767px) {
  .stats-responsive {
    grid-template-columns: 1fr;
    gap: 12px;
  }
}

/* 工具栏响应式 */
.toolbar-responsive {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 12px;
}

@media (max-width: 767px) {
  .toolbar-responsive {
    flex-direction: column;
    align-items: stretch;
  }
  
  .toolbar-responsive > * {
    width: 100%;
  }
}

/* 触摸优化 */
@media (max-width: 767px) {
  /* 增加可点击区域 */
  .el-button {
    min-height: 44px;
    padding: 12px 20px;
  }
  
  .el-button--small {
    min-height: 36px;
    padding: 8px 16px;
  }
  
  /* 表格行高 */
  .el-table .el-table__row {
    height: 56px;
  }
  
  /* 选择器高度 */
  .el-select .el-input__inner {
    height: 44px;
    line-height: 44px;
  }
  
  /* 日期选择器 */
  .el-date-editor .el-input__inner {
    height: 44px;
  }
}

/* 滚动条优化 */
@media (max-width: 767px) {
  /* 隐藏滚动条但保持功能 */
  .mobile-scroll-hidden {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  
  .mobile-scroll-hidden::-webkit-scrollbar {
    display: none;
  }
}

/* 安全区域适配 */
@supports (padding: max(0px)) {
  .safe-area-inset {
    padding-left: max(16px, env(safe-area-inset-left));
    padding-right: max(16px, env(safe-area-inset-right));
    padding-bottom: max(16px, env(safe-area-inset-bottom));
  }
}
