/**
 * 系统状态管理
 * 管理API连接状态和系统信息
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useSystemStore = defineStore('system', () => {
  // 状态
  const apiConnected = ref(false)
  const lastConnectionCheck = ref(null)
  const systemInfo = ref({
    version: '1.0.0',
    environment: 'development'
  })

  // 计算属性
  const connectionStatus = computed(() => {
    if (!lastConnectionCheck.value) return 'unknown'
    return apiConnected.value ? 'connected' : 'disconnected'
  })

  const needsReconnect = computed(() => {
    if (!lastConnectionCheck.value) return true
    const now = Date.now()
    const lastCheck = new Date(lastConnectionCheck.value).getTime()
    return now - lastCheck > 5 * 60 * 1000 // 5分钟
  })

  // 方法
  function setApiConnected(connected) {
    apiConnected.value = connected
    lastConnectionCheck.value = new Date().toISOString()
  }

  function updateSystemInfo(info) {
    systemInfo.value = { ...systemInfo.value, ...info }
  }

  function resetConnection() {
    apiConnected.value = false
    lastConnectionCheck.value = null
  }

  return {
    // 状态
    apiConnected,
    lastConnectionCheck,
    systemInfo,
    
    // 计算属性
    connectionStatus,
    needsReconnect,
    
    // 方法
    setApiConnected,
    updateSystemInfo,
    resetConnection
  }
})
