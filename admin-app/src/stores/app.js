/**
 * 应用状态管理
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useAppStore = defineStore('app', () => {
  // 状态
  const sidebarCollapsed = ref(false)
  const loading = ref(false)
  const theme = ref('light')
  const language = ref('zh-CN')
  const breadcrumbs = ref([])
  const notifications = ref([])

  // 计算属性
  const isDarkTheme = computed(() => theme.value === 'dark')
  const unreadNotifications = computed(() => 
    notifications.value.filter(n => !n.read).length
  )

  // 方法
  function toggleSidebar() {
    sidebarCollapsed.value = !sidebarCollapsed.value
    saveSettings()
  }

  function setSidebarCollapsed(collapsed) {
    sidebarCollapsed.value = collapsed
    saveSettings()
  }

  function setLoading(isLoading) {
    loading.value = isLoading
  }

  function setTheme(newTheme) {
    theme.value = newTheme
    document.documentElement.setAttribute('data-theme', newTheme)
    saveSettings()
  }

  function setLanguage(lang) {
    language.value = lang
    saveSettings()
  }

  function setBreadcrumbs(crumbs) {
    breadcrumbs.value = crumbs
  }

  function addNotification(notification) {
    const newNotification = {
      id: Date.now().toString(),
      timestamp: new Date().toISOString(),
      read: false,
      ...notification
    }
    notifications.value.unshift(newNotification)
    
    // 限制通知数量
    if (notifications.value.length > 50) {
      notifications.value = notifications.value.slice(0, 50)
    }
    
    saveSettings()
  }

  function markNotificationRead(id) {
    const notification = notifications.value.find(n => n.id === id)
    if (notification) {
      notification.read = true
      saveSettings()
    }
  }

  function markAllNotificationsRead() {
    notifications.value.forEach(n => n.read = true)
    saveSettings()
  }

  function removeNotification(id) {
    const index = notifications.value.findIndex(n => n.id === id)
    if (index > -1) {
      notifications.value.splice(index, 1)
      saveSettings()
    }
  }

  function clearNotifications() {
    notifications.value = []
    saveSettings()
  }

  // 保存设置到本地存储
  function saveSettings() {
    try {
      const settings = {
        sidebarCollapsed: sidebarCollapsed.value,
        theme: theme.value,
        language: language.value,
        notifications: notifications.value
      }
      localStorage.setItem('admin_settings', JSON.stringify(settings))
    } catch (error) {
      console.error('保存设置失败:', error)
    }
  }

  // 从本地存储加载设置
  function loadSettings() {
    try {
      const saved = localStorage.getItem('admin_settings')
      if (saved) {
        const settings = JSON.parse(saved)
        
        if (typeof settings.sidebarCollapsed === 'boolean') {
          sidebarCollapsed.value = settings.sidebarCollapsed
        }
        
        if (settings.theme) {
          theme.value = settings.theme
          document.documentElement.setAttribute('data-theme', settings.theme)
        }
        
        if (settings.language) {
          language.value = settings.language
        }
        
        if (Array.isArray(settings.notifications)) {
          notifications.value = settings.notifications
        }
      }
    } catch (error) {
      console.error('加载设置失败:', error)
    }
  }

  // 重置设置
  function resetSettings() {
    sidebarCollapsed.value = false
    theme.value = 'light'
    language.value = 'zh-CN'
    notifications.value = []
    document.documentElement.setAttribute('data-theme', 'light')
    localStorage.removeItem('admin_settings')
  }

  // 显示成功消息
  function showSuccess(message, title = '成功') {
    addNotification({
      type: 'success',
      title,
      message
    })
  }

  // 显示错误消息
  function showError(message, title = '错误') {
    addNotification({
      type: 'error',
      title,
      message
    })
  }

  // 显示警告消息
  function showWarning(message, title = '警告') {
    addNotification({
      type: 'warning',
      title,
      message
    })
  }

  // 显示信息消息
  function showInfo(message, title = '信息') {
    addNotification({
      type: 'info',
      title,
      message
    })
  }

  return {
    // 状态
    sidebarCollapsed,
    loading,
    theme,
    language,
    breadcrumbs,
    notifications,
    
    // 计算属性
    isDarkTheme,
    unreadNotifications,
    
    // 方法
    toggleSidebar,
    setSidebarCollapsed,
    setLoading,
    setTheme,
    setLanguage,
    setBreadcrumbs,
    addNotification,
    markNotificationRead,
    markAllNotificationsRead,
    removeNotification,
    clearNotifications,
    saveSettings,
    loadSettings,
    resetSettings,
    showSuccess,
    showError,
    showWarning,
    showInfo
  }
})
