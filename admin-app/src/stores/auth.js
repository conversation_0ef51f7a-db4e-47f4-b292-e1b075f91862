/**
 * 认证状态管理
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const isAuthenticated = ref(false)
  const user = ref(null)
  const apiConnected = ref(false)
  const lastConnectionCheck = ref(null)

  // 计算属性
  const isLoggedIn = computed(() => isAuthenticated.value && user.value)
  const needsReconnect = computed(() => {
    if (!lastConnectionCheck.value) return true
    const now = Date.now()
    const lastCheck = new Date(lastConnectionCheck.value).getTime()
    return now - lastCheck > 5 * 60 * 1000 // 5分钟
  })

  // 方法
  async function login(credentials) {
    try {
      // 这里可以实现实际的登录逻辑
      // 暂时使用简单的验证
      if (credentials.username && credentials.password) {
        user.value = {
          id: '1',
          username: credentials.username,
          name: '管理员',
          role: 'admin',
          avatar: '',
          loginTime: new Date().toISOString()
        }
        isAuthenticated.value = true
        
        // 检查API连接
        await checkConnection()
        
        return { success: true }
      } else {
        throw new Error('用户名或密码不能为空')
      }
    } catch (error) {
      console.error('登录失败:', error)
      throw error
    }
  }

  function logout() {
    isAuthenticated.value = false
    user.value = null
    apiConnected.value = false
    lastConnectionCheck.value = null
  }

  function setApiConnected(connected) {
    apiConnected.value = connected
    lastConnectionCheck.value = new Date().toISOString()
  }

  function updateUser(userData) {
    if (user.value) {
      user.value = { ...user.value, ...userData }
    }
  }

  // 初始化时检查本地存储
  function initAuth() {
    try {
      const savedAuth = localStorage.getItem('admin_auth')
      if (savedAuth) {
        const authData = JSON.parse(savedAuth)
        if (authData.user && authData.isAuthenticated) {
          user.value = authData.user
          isAuthenticated.value = authData.isAuthenticated
        }
      }
    } catch (error) {
      console.error('初始化认证状态失败:', error)
      logout()
    }
  }

  // 保存认证状态到本地存储
  function saveAuth() {
    try {
      const authData = {
        user: user.value,
        isAuthenticated: isAuthenticated.value,
        timestamp: Date.now()
      }
      localStorage.setItem('admin_auth', JSON.stringify(authData))
    } catch (error) {
      console.error('保存认证状态失败:', error)
    }
  }

  // 监听状态变化并保存
  function setupAutoSave() {
    // 这里可以使用 watch 来监听状态变化
    // 为了简化，暂时在需要的地方手动调用 saveAuth
  }

  return {
    // 状态
    isAuthenticated,
    user,
    apiConnected,
    lastConnectionCheck,
    
    // 计算属性
    isLoggedIn,
    needsReconnect,
    
    // 方法
    login,
    logout,
    setApiConnected,
    updateUser,
    initAuth,
    saveAuth,
    setupAutoSave
  }
})
