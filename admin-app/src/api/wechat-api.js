/**
 * 微信云函数API调用封装
 * 实现正确的微信云函数调用流程
 */

import axios from 'axios'
import { ElMessage } from 'element-plus'
import {
  isTauri,
  testWechatConnection as tauriTestConnection,
  callCloudFunction as tauriCallCloudFunction
} from './tauri-api.js'

// 微信API基础URL
// 在 Tauri 环境下直接使用 HTTPS，在浏览器开发环境下使用代理
const WECHAT_API_BASE = isTauri
  ? 'https://api.weixin.qq.com'
  : (import.meta.env.DEV ? '/api/wechat' : 'https://api.weixin.qq.com')

// 在浏览器环境下使用 axios 的简单包装函数
async function makeHttpRequest(config) {
  console.log('使用 axios 发送请求:', config.url)
  return await axios(config)
}

// Access Token 缓存
let accessTokenCache = {
  token: null,
  expiresAt: 0,
  isRefreshing: false // 防止并发刷新
}

// 配置信息（从本地存储获取）
let wechatConfig = {
  appId: '',
  appSecret: '',
  env: '',
  functionName: '',
  secretKey: ''
}

/**
 * 设置微信配置信息
 * @param {Object} config - 配置对象
 */
export function setWechatConfig(config) {
  wechatConfig = { ...config }
  
  // 保存到本地存储
  localStorage.setItem('wechat_config', JSON.stringify(config))
  
  console.log('微信配置已更新')
}

/**
 * 从本地存储加载配置
 */
export function loadWechatConfig() {
  try {
    const saved = localStorage.getItem('wechat_config')
    if (saved) {
      wechatConfig = JSON.parse(saved)
      console.log('已加载本地配置')

      // 同时加载Access Token缓存
      loadAccessTokenCache()
      return true
    }
  } catch (error) {
    console.error('加载本地配置失败:', error)
  }
  return false
}

/**
 * 加载Access Token缓存
 */
function loadAccessTokenCache() {
  try {
    const saved = localStorage.getItem('access_token_cache')
    if (saved) {
      const cache = JSON.parse(saved)
      // 检查是否过期
      if (cache.expiresAt > Date.now()) {
        accessTokenCache = { ...cache, isRefreshing: false }
        console.log('已加载有效的Access Token缓存')
      } else {
        console.log('Access Token缓存已过期，将重新获取')
        localStorage.removeItem('access_token_cache')
      }
    }
  } catch (error) {
    console.error('加载Access Token缓存失败:', error)
    localStorage.removeItem('access_token_cache')
  }
}

/**
 * 保存Access Token缓存
 */
function saveAccessTokenCache() {
  try {
    const cacheData = {
      token: accessTokenCache.token,
      expiresAt: accessTokenCache.expiresAt
    }
    localStorage.setItem('access_token_cache', JSON.stringify(cacheData))
  } catch (error) {
    console.error('保存Access Token缓存失败:', error)
  }
}

/**
 * 获取当前配置
 */
export function getWechatConfig() {
  return { ...wechatConfig }
}

/**
 * 清除配置
 */
export function clearWechatConfig() {
  wechatConfig = {
    appId: '',
    appSecret: '',
    env: '',
    functionName: '',
    secretKey: ''
  }
  localStorage.removeItem('wechat_config')
  localStorage.removeItem('access_token_cache')
  accessTokenCache = { token: null, expiresAt: 0, isRefreshing: false }
}

/**
 * 检查配置是否完整
 */
export function isConfigValid() {
  return !!(
    wechatConfig.appId &&
    wechatConfig.appSecret &&
    wechatConfig.env &&
    wechatConfig.functionName &&
    wechatConfig.secretKey
  )
}

/**
 * 获取Access Token状态
 * @returns {Object} Token状态信息
 */
export function getTokenStatus() {
  const now = Date.now()
  return {
    hasToken: !!accessTokenCache.token,
    isValid: accessTokenCache.token && accessTokenCache.expiresAt > now,
    expiresAt: accessTokenCache.expiresAt,
    expiresIn: Math.max(0, Math.floor((accessTokenCache.expiresAt - now) / 1000)),
    isRefreshing: accessTokenCache.isRefreshing
  }
}

/**
 * 获取Access Token
 * @returns {Promise<string>} Access Token
 */
async function getAccessToken() {
  const now = Date.now()

  // 检查缓存是否有效（提前1分钟过期）
  if (accessTokenCache.token && accessTokenCache.expiresAt > now + 60000) {
    console.log('使用缓存的Access Token')
    return accessTokenCache.token
  }

  // 防止并发刷新
  if (accessTokenCache.isRefreshing) {
    console.log('Access Token正在刷新中，等待...')
    // 等待刷新完成
    let retries = 0
    while (accessTokenCache.isRefreshing && retries < 30) {
      await new Promise(resolve => setTimeout(resolve, 100))
      retries++
    }

    if (accessTokenCache.token && accessTokenCache.expiresAt > now) {
      return accessTokenCache.token
    }
  }

  try {
    accessTokenCache.isRefreshing = true
    console.log('获取新的Access Token...')

    const response = await makeHttpRequest({
      method: 'GET',
      url: `${WECHAT_API_BASE}/cgi-bin/token`,
      params: {
        grant_type: 'client_credential',
        appid: wechatConfig.appId,
        secret: wechatConfig.appSecret
      },
      timeout: 10000
    })

    const data = response.data

    if (data.errcode) {
      throw new Error(`获取Access Token失败: ${data.errmsg} (${data.errcode})`)
    }

    // 缓存Token（提前5分钟过期）
    accessTokenCache = {
      token: data.access_token,
      expiresAt: now + (data.expires_in - 300) * 1000,
      obtainedAt: now,
      isRefreshing: false
    }

    // 保存到本地存储
    saveAccessTokenCache()

    console.log('Access Token获取成功，有效期:', data.expires_in, '秒')
    return data.access_token

  } catch (error) {
    accessTokenCache.isRefreshing = false
    console.error('获取Access Token失败:', error)

    if (error.response?.data) {
      const { errcode, errmsg } = error.response.data
      throw new Error(`微信API错误: ${errmsg} (${errcode})`)
    }

    throw new Error(`网络错误: ${error.message}`)
  }
}

/**
 * 检查是否是Access Token过期错误
 * @param {Object} error - 错误对象
 * @returns {boolean} 是否是Token过期错误
 */
function isTokenExpiredError(error) {
  // 检查微信API返回的错误码
  if (error.response?.data?.errcode === 40001) {
    return true
  }

  // 检查错误消息中是否包含Token相关关键词
  const errorMessage = error.message || error.response?.data?.errmsg || ''
  const tokenErrorKeywords = [
    'invalid credential',
    'access_token is invalid',
    'access_token过期',
    'token过期'
  ]

  return tokenErrorKeywords.some(keyword =>
    errorMessage.toLowerCase().includes(keyword.toLowerCase())
  )
}

/**
 * 强制刷新Access Token
 * @returns {Promise<string>} 新的Access Token
 */
async function forceRefreshAccessToken() {
  console.log('强制刷新Access Token...')

  // 清除当前缓存
  accessTokenCache = {
    token: null,
    expiresAt: 0,
    isRefreshing: false
  }
  localStorage.removeItem('access_token_cache')

  // 获取新Token
  return await getAccessToken()
}

/**
 * 执行云函数调用（内部方法）
 * @param {string} type - API类型
 * @param {Object} data - 请求数据
 * @param {string} accessToken - Access Token
 * @returns {Promise<Object>} 响应结果
 */
async function executeCloudFunctionCall(type, data, accessToken) {
  // 构建请求体
  const requestBody = {
    type,
    data,
    secretKey: wechatConfig.secretKey
  }

  console.log('调用云函数:', {
    type,
    env: wechatConfig.env,
    functionName: wechatConfig.functionName,
    data
  })

  // 调用云函数
  const response = await makeHttpRequest({
    method: 'POST',
    url: `${WECHAT_API_BASE}/tcb/invokecloudfunction`,
    data: requestBody,
    params: {
      access_token: accessToken,
      env: wechatConfig.env,
      name: wechatConfig.functionName
    },
    timeout: 30000,
    headers: {
      'Content-Type': 'application/json'
    }
  })

  const result = response.data

  // 检查微信API层面的错误
  if (result.errcode && result.errcode !== 0) {
    const error = new Error(`微信API错误: ${result.errmsg} (${result.errcode})`)
    error.response = { data: result }
    throw error
  }

  // 解析云函数返回的结果
  let cloudResult
  try {
    cloudResult = typeof result.resp_data === 'string'
      ? JSON.parse(result.resp_data)
      : result.resp_data
  } catch (parseError) {
    console.error('解析云函数响应失败:', parseError)
    throw new Error('云函数响应格式错误')
  }

  return cloudResult
}

/**
 * 调用云函数（带自动重试机制）
 * @param {string} type - API类型
 * @param {Object} data - 请求数据
 * @param {Object} options - 选项
 * @returns {Promise<Object>} 响应结果
 */
export async function callCloudFunction(type, data = {}, options = {}) {
  const {
    showSuccess = false,
    showError = true,
    maxRetries = 1,
    retryDelay = 1000
  } = options

  try {
    // 检查配置
    if (!isConfigValid()) {
      throw new Error('微信配置不完整，请先配置相关信息')
    }

    // 在 Tauri 环境下使用新的 API
    // 强制检测 Tauri 环境（Android 端可能检测失败）
    const isActuallyTauri = isTauri || window.location.hostname === 'tauri.localhost'

    if (isActuallyTauri) {
      console.log('使用 Tauri 原生云函数调用 (强制检测):', type)
      const result = await tauriCallCloudFunction(type, data, options)

      if (result.success && showSuccess) {
        ElMessage.success(result.message || '操作成功')
      }

      return result
    }

    let lastError = null
    let retryCount = 0

    // 重试循环
    while (retryCount <= maxRetries) {
      try {
        // 获取Access Token
        const accessToken = await getAccessToken()

        // 执行云函数调用
        const cloudResult = await executeCloudFunctionCall(type, data, accessToken)

        console.log('云函数调用成功:', cloudResult)

        // 显示成功消息
        if (showSuccess && cloudResult.success) {
          ElMessage.success(cloudResult.message || '操作成功')
        }

        return cloudResult

      } catch (error) {
        lastError = error

        // 检查是否是Token过期错误
        if (isTokenExpiredError(error) && retryCount < maxRetries) {
          console.warn(`Access Token过期，准备重试 (${retryCount + 1}/${maxRetries + 1})`)

          try {
            // 强制刷新Token
            await forceRefreshAccessToken()

            // 等待一段时间后重试
            if (retryDelay > 0) {
              await new Promise(resolve => setTimeout(resolve, retryDelay))
            }

            retryCount++
            continue

          } catch (refreshError) {
            console.error('刷新Access Token失败:', refreshError)
            // 如果刷新失败，直接抛出原错误
            break
          }
        } else {
          // 非Token错误或已达到最大重试次数
          break
        }
      }
    }

    // 所有重试都失败了，抛出最后一个错误
    throw lastError

  } catch (error) {
    console.error('云函数调用失败:', error)

    // 显示错误消息
    if (showError) {
      const errorMessage = error.message || '调用失败'

      // 如果是Token相关错误，提供更友好的提示
      if (isTokenExpiredError(error)) {
        ElMessage.error('访问凭证已过期，请检查配置或稍后重试')
      } else {
        ElMessage.error(errorMessage)
      }
    }

    // 返回错误格式的响应
    return {
      success: false,
      message: error.message || '调用失败',
      code: error.response?.data?.errcode || 'UNKNOWN_ERROR',
      error: error
    }
  }
}

/**
 * 带重试的云函数调用（便捷方法）
 * @param {string} type - API类型
 * @param {Object} data - 请求数据
 * @param {Object} options - 选项
 * @returns {Promise<Object>} 响应结果
 */
export async function callCloudFunctionWithRetry(type, data = {}, options = {}) {
  return await callCloudFunction(type, data, {
    maxRetries: 2,
    retryDelay: 1000,
    ...options
  })
}

/**
 * 静默调用云函数（不显示错误消息）
 * @param {string} type - API类型
 * @param {Object} data - 请求数据
 * @returns {Promise<Object>} 响应结果
 */
export async function callCloudFunctionSilent(type, data = {}) {
  return await callCloudFunction(type, data, {
    showError: false,
    showSuccess: false,
    maxRetries: 1
  })
}

/**
 * 测试连接
 * @returns {Promise<Object>} 测试结果
 */
export async function testConnection() {
  try {
    console.log('测试微信云函数连接...')

    // 检查配置完整性
    if (!isConfigValid()) {
      return {
        success: false,
        message: '配置不完整，请检查所有必填项是否已填写'
      }
    }

    // 检测 Tauri 环境
    console.log('环境检测:', {
      isTauri,
      hasWindow: typeof window !== 'undefined',
      hasTauriAPI: typeof window !== 'undefined' && !!window.__TAURI__,
      userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : 'unknown'
    })

    // 在 Tauri 环境下使用新的 API
    // 强制检测 Tauri 环境（Android 端可能检测失败）
    const isActuallyTauri = isTauri || window.location.hostname === 'tauri.localhost'

    if (isActuallyTauri) {
      console.log('使用 Tauri 原生网络请求 (强制检测)')
      return await tauriTestConnection(wechatConfig)
    }

    // 浏览器环境下使用原有逻辑
    console.log('使用浏览器网络请求')
    console.log('当前配置:', {
      appId: wechatConfig.appId ? `${wechatConfig.appId.slice(0, 4)}****${wechatConfig.appId.slice(-4)}` : '未配置',
      env: wechatConfig.env || '未配置',
      functionName: wechatConfig.functionName || '未配置',
      hasSecretKey: !!wechatConfig.secretKey,
      isTauri: isTauri,
      userAgent: navigator.userAgent,
      hostname: window.location.hostname,
      protocol: window.location.protocol,
      hasTauriGlobal: !!window.__TAURI__,
      hasTauriInternals: !!window.__TAURI_INTERNALS__
    })

    // 调用一个简单的系统统计API来测试连接
    const result = await callCloudFunction('getSystemStats', {
      test: true  // 添加一个测试标识
    }, {
      showError: false,
      maxRetries: 1  // 测试时减少重试次数
    })

    if (result.success) {
      console.log('连接测试成功:', result)
      return {
        success: true,
        message: '连接正常，云函数响应正常',
        data: result.data
      }
    } else {
      // 直接使用原始错误信息
      throw new Error(result.message || '连接测试失败')
    }

  } catch (error) {
    console.error('连接测试失败:', error)

    return {
      success: false,
      message: error.message || '连接测试失败'
    }
  }
}

/**
 * 手动刷新Access Token
 * @returns {Promise<Object>} 刷新结果
 */
export async function refreshAccessToken() {
  try {
    console.log('手动刷新Access Token...')

    const newToken = await forceRefreshAccessToken()

    return {
      success: true,
      message: 'Access Token刷新成功',
      data: {
        token: newToken,
        expiresAt: accessTokenCache.expiresAt
      }
    }

  } catch (error) {
    console.error('手动刷新Access Token失败:', error)
    return {
      success: false,
      message: error.message || 'Access Token刷新失败'
    }
  }
}

/**
 * 获取Access Token信息
 * @returns {Object} Token信息
 */
export function getAccessTokenInfo() {
  return {
    token: accessTokenCache.token,
    expiresAt: accessTokenCache.expiresAt,
    obtainedAt: accessTokenCache.obtainedAt || null,
    isRefreshing: accessTokenCache.isRefreshing,
    isValid: accessTokenCache.token && accessTokenCache.expiresAt > Date.now()
  }
}

/**
 * 检查Token是否需要刷新
 * @param {number} bufferTime - 缓冲时间（毫秒）
 * @returns {boolean} 是否需要刷新
 */
export function needsTokenRefresh(bufferTime = 5 * 60 * 1000) {
  if (!accessTokenCache.token) return true

  const now = Date.now()
  const expiresAt = accessTokenCache.expiresAt || 0

  return (expiresAt - now) <= bufferTime
}

/**
 * 批量调用云函数
 * @param {Array} requests - 请求列表
 * @param {Object} options - 批量调用选项
 * @returns {Promise<Array>} 结果列表
 */
export async function batchCallCloudFunction(requests, options = {}) {
  const {
    concurrent = 5,  // 并发数量
    retryFailed = true,  // 是否重试失败的请求
    showProgress = false  // 是否显示进度
  } = options

  try {
    console.log(`开始批量调用 ${requests.length} 个API...`)

    const results = []

    // 分批处理，避免并发过多
    for (let i = 0; i < requests.length; i += concurrent) {
      const batch = requests.slice(i, i + concurrent)

      if (showProgress) {
        console.log(`处理批次 ${Math.floor(i / concurrent) + 1}/${Math.ceil(requests.length / concurrent)}`)
      }

      const promises = batch.map(request =>
        callCloudFunction(request.type, request.data, {
          showError: false,
          maxRetries: retryFailed ? 1 : 0
        })
      )

      const batchResults = await Promise.allSettled(promises)

      const processedResults = batchResults.map((result, index) => {
        if (result.status === 'fulfilled') {
          return result.value
        } else {
          return {
            success: false,
            message: result.reason?.message || '调用失败',
            request: batch[index]
          }
        }
      })

      results.push(...processedResults)

      // 批次间稍作延迟，避免请求过于频繁
      if (i + concurrent < requests.length) {
        await new Promise(resolve => setTimeout(resolve, 100))
      }
    }

    const successCount = results.filter(r => r.success).length
    const failureCount = results.length - successCount

    console.log(`批量调用完成: 成功 ${successCount}, 失败 ${failureCount}`)

    return results

  } catch (error) {
    console.error('批量调用失败:', error)
    throw error
  }
}

/**
 * 重试失败的批量请求
 * @param {Array} failedResults - 失败的结果列表
 * @returns {Promise<Array>} 重试结果
 */
export async function retryFailedRequests(failedResults) {
  const failedRequests = failedResults
    .filter(result => !result.success && result.request)
    .map(result => result.request)

  if (failedRequests.length === 0) {
    return []
  }

  console.log(`重试 ${failedRequests.length} 个失败的请求...`)

  return await batchCallCloudFunction(failedRequests, {
    concurrent: 3,
    retryFailed: false,  // 避免无限重试
    showProgress: true
  })
}

// 初始化时加载配置
loadWechatConfig()
