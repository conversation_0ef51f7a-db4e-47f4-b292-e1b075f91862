/**
 * 用户管理API
 */

import { callAdminAPI } from './index.js'

/**
 * 获取用户列表
 * @param {Object} params - 查询参数
 * @returns {Promise} 用户列表
 */
export function getUserList(params = {}) {
  return callAdminAPI('getUserList', params)
}

/**
 * 获取用户详情
 * @param {string} userId - 用户ID
 * @returns {Promise} 用户详情
 */
export function getUserDetail(userId) {
  return callAdminAPI('getUserDetail', { userId })
}

/**
 * 更新用户状态
 * @param {Object} data - 更新数据
 * @returns {Promise} 更新结果
 */
export function updateUserStatus(data) {
  return callAdminAPI('updateUserStatus', data, { showSuccess: true })
}

/**
 * 获取用户统计
 * @param {string} period - 统计周期
 * @returns {Promise} 统计数据
 */
export function getUserStats(period = '30d') {
  return callAdminAPI('getUserStats', { period })
}

/**
 * 导出用户数据
 * @param {Object} params - 导出参数
 * @returns {Promise} 导出结果
 */
export function exportUserData(params = {}) {
  return callAdminAPI('exportUserData', params)
}

/**
 * 批量操作用户
 * @param {Array} userIds - 用户ID列表
 * @param {string} action - 操作类型
 * @param {Object} data - 操作数据
 * @returns {Promise} 操作结果
 */
export function batchOperateUsers(userIds, action, data = {}) {
  return callAdminAPI('batchOperateUsers', { userIds, action, ...data }, { showSuccess: true })
}
