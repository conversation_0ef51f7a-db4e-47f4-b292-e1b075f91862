/**
 * 仪表板统计API
 */

import { callCloudFunction, batchCallCloudFunction } from './wechat-api.js'

/**
 * 获取仪表板统计数据
 * @param {string} period - 统计周期
 * @returns {Promise} 统计数据
 */
export async function getDashboardStats(period = '30d') {
  return callCloudFunction('getDashboardStats', { period })
}

/**
 * 获取系统状态
 * @returns {Promise} 系统状态
 */
export function getSystemStatus() {
  return callCloudFunction('getSystemHealth')
}

/**
 * 获取最近活动
 * @param {number} limit - 限制数量
 * @returns {Promise} 最近活动
 */
export async function getRecentActivities(limit = 10) {
  try {
    // 获取最近的反馈、用户注册等活动
    const requests = [
      { 
        type: 'getFeedbackListAdmin', 
        data: { 
          page: 1, 
          pageSize: limit, 
          sortBy: 'createTime', 
          sortOrder: 'desc' 
        } 
      },
      { 
        type: 'getUserList', 
        data: { 
          page: 1, 
          pageSize: limit, 
          sortBy: 'createTime', 
          sortOrder: 'desc' 
        } 
      }
    ]

    const results = await batchCallAdminAPI(requests)
    
    const activities = []

    results.forEach((result, index) => {
      if (result.success && result.data) {
        switch (requests[index].type) {
          case 'getFeedbackListAdmin':
            result.data.forEach(item => {
              activities.push({
                type: 'feedback',
                title: '新反馈',
                description: item.content?.substring(0, 50) + '...',
                time: item.createTime,
                status: item.status
              })
            })
            break
          case 'getUserList':
            result.data.forEach(item => {
              activities.push({
                type: 'user',
                title: '新用户注册',
                description: item.nickname || item.username,
                time: item.createTime,
                status: item.status
              })
            })
            break
        }
      }
    })

    // 按时间排序并限制数量
    activities.sort((a, b) => new Date(b.time) - new Date(a.time))
    
    return {
      success: true,
      data: activities.slice(0, limit)
    }
  } catch (error) {
    console.error('获取最近活动失败:', error)
    return {
      success: false,
      message: error.message || '获取最近活动失败'
    }
  }
}

/**
 * 获取趋势数据
 * @param {string} period - 统计周期
 * @returns {Promise} 趋势数据
 */
export async function getTrendData(period = '7d') {
  try {
    const result = await callCloudFunction('getTrendData', { period })

    if (result.success) {
      return {
        success: true,
        data: result.data
      }
    } else {
      throw new Error(result.message || '获取趋势数据失败')
    }
  } catch (error) {
    console.error('获取趋势数据失败:', error)

    // 如果API调用失败，返回默认数据
    const defaultTrendData = {
      users: {
        current: 0,
        change: '0%',
        trend: 'stable'
      },
      announcements: {
        current: 0,
        change: '0%',
        trend: 'stable'
      },
      feedback: {
        current: 0,
        change: '0%',
        trend: 'stable'
      },
      points: {
        current: 0,
        change: '0%',
        trend: 'stable'
      }
    }

    return {
      success: false,
      message: error.message || '获取趋势数据失败',
      data: defaultTrendData
    }
  }
}
