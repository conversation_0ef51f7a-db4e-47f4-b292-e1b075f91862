/**
 * 反馈管理API
 */

import { callAdminAPI } from './index.js'

/**
 * 获取反馈列表（管理端）
 * @param {Object} params - 查询参数
 * @returns {Promise} 反馈列表
 */
export function getFeedbackList(params = {}) {
  return callAdminAPI('getFeedbackListAdmin', params)
}

/**
 * 回复反馈
 * @param {Object} data - 回复数据
 * @returns {Promise} 回复结果
 */
export function replyFeedback(data) {
  return callAdminAPI('replyFeedback', data, { showSuccess: true })
}

/**
 * 更新反馈状态
 * @param {Object} data - 更新数据
 * @returns {Promise} 更新结果
 */
export function updateFeedbackStatus(data) {
  return callAdminAPI('updateFeedbackStatus', data, { showSuccess: true })
}

/**
 * 获取反馈统计
 * @param {string} period - 统计周期
 * @returns {Promise} 统计数据
 */
export function getFeedbackStats(period = '30d') {
  return callAdminAPI('getFeedbackStatsAdmin', { period })
}

/**
 * 导出反馈数据
 * @param {Object} params - 导出参数
 * @returns {Promise} 导出结果
 */
export function exportFeedbackData(params = {}) {
  return callAdminAPI('exportFeedbackData', params)
}

/**
 * 批量操作反馈
 * @param {Array} ids - 反馈ID列表
 * @param {string} action - 操作类型
 * @param {Object} data - 操作数据
 * @returns {Promise} 操作结果
 */
export function batchOperateFeedback(ids, action, data = {}) {
  return callAdminAPI('batchOperateFeedback', { ids, action, ...data }, { showSuccess: true })
}
