import { invoke } from '@tauri-apps/api/core'
import { fetch as tauriFetch } from '@tauri-apps/plugin-http'

/**
 * 检测是否在 Tauri 环境中
 */
export const isTauri = typeof window !== 'undefined' && (
  window.__TAURI__ ||
  window.__TAURI_INTERNALS__ ||
  window.location.protocol === 'tauri:' ||
  window.location.hostname === 'tauri.localhost' ||
  navigator.userAgent.includes('Tauri')
)

/**
 * 通用 HTTP 请求函数（使用 Tauri HTTP 插件）
 */
export async function httpRequest(config) {
  if (!isTauri) {
    throw new Error('此函数只能在 Tauri 环境中使用')
  }

  try {
    console.log('发送 Tauri HTTP 请求:', config.url)

    // 构建完整的 URL（包含查询参数）
    let fullUrl = config.url
    if (config.params) {
      const searchParams = new URLSearchParams(config.params)
      fullUrl += (fullUrl.includes('?') ? '&' : '?') + searchParams.toString()
    }

    // 准备请求选项
    const options = {
      method: config.method || 'GET',
      headers: {
        'User-Agent': 'Mozilla/5.0 (Linux; Android 10; Mobile) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36',
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        ...config.headers
      },
      timeout: config.timeout || 30000
    }

    // 设置请求体
    if (config.data) {
      if (typeof config.data === 'string') {
        options.body = config.data
      } else {
        // 为 Android 端添加特殊处理
        const jsonString = JSON.stringify(config.data)
        console.log('原始数据对象:', config.data)
        console.log('JSON 序列化结果:', jsonString)

        // 确保 JSON 字符串正确
        try {
          const parsed = JSON.parse(jsonString)
          console.log('JSON 解析验证成功:', parsed)
          console.log('type 字段存在:', 'type' in parsed)
          console.log('type 字段值:', parsed.type)
        } catch (e) {
          console.error('JSON 序列化验证失败:', e)
        }

        options.body = jsonString
        options.headers['Content-Type'] = 'application/json; charset=utf-8'
      }
      console.log('HTTP 请求体内容:', options.body)
      console.log('HTTP 请求体类型:', typeof options.body)
      console.log('HTTP 请求体长度:', options.body.length)
    }

    console.log('Tauri HTTP 请求详情:', { url: fullUrl, ...options })

    // 尝试发送请求
    let response
    try {
      response = await tauriFetch(fullUrl, options)
      console.log('Tauri fetch 调用成功')
    } catch (fetchError) {
      console.error('Tauri fetch 调用失败:', fetchError)

      // 如果是 POST 请求且有请求体，尝试使用 FormData 格式
      if (config.method === 'POST' && config.data) {
        console.log('尝试使用 FormData 格式重新发送请求')

        const formData = new FormData()
        if (typeof config.data === 'object') {
          // 将 JSON 对象转换为 FormData
          for (const [key, value] of Object.entries(config.data)) {
            formData.append(key, typeof value === 'object' ? JSON.stringify(value) : value)
          }
        }

        const formOptions = {
          method: config.method,
          body: formData,
          timeout: config.timeout || 30000
        }

        console.log('FormData 请求选项:', formOptions)
        response = await tauriFetch(fullUrl, formOptions)
        console.log('FormData 请求成功')
      } else {
        throw fetchError
      }
    }

    console.log('Tauri HTTP 响应状态:', response.status)

    // 解析响应体
    const text = await response.text()
    let data
    try {
      data = JSON.parse(text)
    } catch {
      data = text
    }

    console.log('Tauri HTTP 响应数据:', data)

    return {
      data,
      status: response.status,
      statusText: response.statusText,
      headers: Object.fromEntries(response.headers.entries())
    }
  } catch (error) {
    console.error('Tauri HTTP 请求失败:', error)
    throw error
  }
}

/**
 * 获取微信 Access Token
 */
export async function getWechatAccessToken(appId, appSecret) {
  if (!isTauri) {
    throw new Error('此函数只能在 Tauri 环境中使用')
  }

  try {
    console.log('获取微信 Access Token:', appId)

    const response = await httpRequest({
      method: 'GET',
      url: 'https://api.weixin.qq.com/cgi-bin/token',
      params: {
        grant_type: 'client_credential',
        appid: appId,
        secret: appSecret
      },
      timeout: 10000
    })

    if (response.data.access_token) {
      console.log('Access Token 获取成功')
      return response.data.access_token
    } else if (response.data.errcode) {
      throw new Error(`微信API错误: ${response.data.errmsg} (${response.data.errcode})`)
    } else {
      throw new Error('无效的响应格式')
    }
  } catch (error) {
    console.error('获取 Access Token 失败:', error)
    throw error
  }
}

/**
 * 调用微信云函数
 */
export async function callWechatCloudFunction(config, functionType, data) {
  if (!isTauri) {
    throw new Error('此函数只能在 Tauri 环境中使用')
  }

  try {
    console.log('调用微信云函数:', functionType)

    // 1. 获取 Access Token
    const accessToken = await getWechatAccessToken(config.appId, config.appSecret)

    // 2. 构建云函数请求体
    // 根据微信云函数 API 文档，请求体应该是一个 JSON 字符串
    const functionData = {
      type: functionType,
      data: data || {},
      secretKey: config.secretKey
    }

    // 为 Android 端尝试不同的请求体格式
    console.log('检测平台信息:', {
      userAgent: navigator.userAgent,
      platform: navigator.platform,
      isMobile: /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)
    })

    console.log('云函数请求体:', functionData)
    console.log('请求体 JSON:', JSON.stringify(functionData, null, 2))

    // 3. 调用云函数
    // 尝试多种请求格式来解决 Android 端问题
    let response

    try {
      // 首先尝试标准的 JSON 格式
      console.log('尝试标准 JSON 格式请求')
      response = await httpRequest({
        method: 'POST',
        url: 'https://api.weixin.qq.com/tcb/invokecloudfunction',
        params: {
          access_token: accessToken,
          env: config.env,
          name: config.functionName
        },
        data: functionData,
        headers: {
          'Content-Type': 'application/json'
        },
        timeout: 30000
      })
    } catch (error) {
      console.error('标准 JSON 格式请求失败:', error)

      // 如果失败，尝试将数据作为字符串发送
      console.log('尝试字符串格式请求')
      const jsonString = JSON.stringify(functionData)

      response = await httpRequest({
        method: 'POST',
        url: 'https://api.weixin.qq.com/tcb/invokecloudfunction',
        params: {
          access_token: accessToken,
          env: config.env,
          name: config.functionName
        },
        data: jsonString,  // 直接传递字符串
        headers: {
          'Content-Type': 'application/json; charset=utf-8'
        },
        timeout: 30000
      })
    }

    console.log('云函数原始响应:', response.data)

    // 检查微信API层面的错误
    if (response.data.errcode && response.data.errcode !== 0) {
      return {
        success: false,
        message: `微信API错误: ${response.data.errmsg} (${response.data.errcode})`,
        data: null
      }
    }

    // 解析云函数返回的结果
    if (response.data.resp_data) {
      let cloudResult
      if (typeof response.data.resp_data === 'string') {
        try {
          cloudResult = JSON.parse(response.data.resp_data)
        } catch {
          cloudResult = { success: false, message: '云函数返回格式错误' }
        }
      } else {
        cloudResult = response.data.resp_data
      }

      console.log('云函数解析结果:', cloudResult)

      return {
        success: cloudResult.success || false,
        message: cloudResult.message || '云函数执行完成',
        data: cloudResult.data || null
      }
    } else {
      return {
        success: false,
        message: '云函数无响应数据',
        data: null
      }
    }
  } catch (error) {
    console.error('调用云函数失败:', error)
    return {
      success: false,
      message: error.message || '网络请求失败',
      data: null
    }
  }
}

/**
 * 测试微信云函数连接
 */
export async function testWechatConnection(config) {
  try {
    console.log('测试微信云函数连接...')
    
    // 检查配置完整性
    if (!config.appId || !config.appSecret || !config.env || !config.functionName || !config.secretKey) {
      return {
        success: false,
        message: '配置不完整，请检查所有必填项是否已填写'
      }
    }

    console.log('当前配置:', {
      appId: config.appId ? `${config.appId.slice(0, 4)}****${config.appId.slice(-4)}` : '未配置',
      env: config.env || '未配置',
      functionName: config.functionName || '未配置',
      hasSecretKey: !!config.secretKey,
      isTauri: isTauri
    })

    // 先验证配置
    await invoke('validate_config', {
      config: {
        app_id: config.appId,
        app_secret: config.appSecret,
        env: config.env,
        function_name: config.functionName,
        secret_key: config.secretKey
      }
    })

    // 调用系统统计API来测试连接
    console.log('准备调用云函数，配置:', config)
    const result = await callWechatCloudFunction(config, 'getSystemStats', { test: true })

    if (result.success) {
      console.log('连接测试成功:', result)
      return {
        success: true,
        message: '连接正常，云函数响应正常',
        data: result.data
      }
    } else {
      // 直接使用原始错误信息
      return {
        success: false,
        message: result.message || '连接测试失败'
      }
    }

  } catch (error) {
    console.error('连接测试失败:', error)

    return {
      success: false,
      message: error.message || '连接测试失败'
    }
  }
}

/**
 * 通用云函数调用函数
 */
export async function callCloudFunction(functionType, data = {}, options = {}) {
  if (!isTauri) {
    throw new Error('此函数只能在 Tauri 环境中使用')
  }

  try {
    // 从本地存储获取配置
    const configStr = localStorage.getItem('wechat_config')
    if (!configStr) {
      throw new Error('微信配置未找到，请先完成配置')
    }

    const config = JSON.parse(configStr)
    
    // 验证配置
    if (!config.appId || !config.appSecret || !config.env || !config.functionName || !config.secretKey) {
      throw new Error('微信配置不完整，请重新配置')
    }

    const result = await callWechatCloudFunction(config, functionType, data)
    
    if (!result.success && !options.showError === false) {
      console.error('云函数调用失败:', result.message)
    }

    return result
  } catch (error) {
    console.error('云函数调用失败:', error)
    if (!options.showError === false) {
      // 这里可以显示错误提示
    }
    throw error
  }
}
