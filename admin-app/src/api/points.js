/**
 * 积分管理API
 */

import { callAdminAPI } from './index.js'

/**
 * 获取积分统计（管理端）
 * @param {string} period - 统计周期
 * @returns {Promise} 统计数据
 */
export function getPointsStats(period = '30d') {
  return callAdminAPI('getPointsStatsAdmin', { period })
}

/**
 * 手动调整用户积分
 * @param {Object} data - 调整数据
 * @returns {Promise} 调整结果
 */
export function adjustUserPoints(data) {
  return callAdminAPI('adjustUserPoints', data, { showSuccess: true })
}

/**
 * 获取积分记录（管理端）
 * @param {Object} params - 查询参数
 * @returns {Promise} 积分记录
 */
export function getPointsRecords(params = {}) {
  return callAdminAPI('getPointsRecordsAdmin', params)
}

/**
 * 导出积分数据
 * @param {Object} params - 导出参数
 * @returns {Promise} 导出结果
 */
export function exportPointsData(params = {}) {
  return callAdminAPI('exportPointsData', params)
}

/**
 * 获取积分排行榜
 * @param {Object} params - 查询参数
 * @returns {Promise} 排行榜数据
 */
export function getPointsLeaderboard(params = {}) {
  return callAdminAPI('getPointsLeaderboard', params)
}

/**
 * 批量调整积分
 * @param {Object} data - 批量调整数据
 * @returns {Promise} 调整结果
 */
export function batchAdjustPoints(data) {
  return callAdminAPI('batchAdjustPoints', data, { showSuccess: true })
}

/**
 * 获取积分配置
 * @returns {Promise} 配置数据
 */
export function getPointsConfig() {
  return callAdminAPI('getPointsConfig')
}

/**
 * 更新积分配置
 * @param {Object} config - 配置数据
 * @returns {Promise} 更新结果
 */
export function updatePointsConfig(config) {
  return callAdminAPI('updatePointsConfig', { config }, { showSuccess: true })
}
