<template>
  <div class="dashboard">
    <div class="page-header">
      <h1 class="page-title">仪表板</h1>
      <p class="page-description">欢迎使用后台管理系统</p>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-grid stats-responsive">
      <StatCard
        label="总用户数"
        :value="stats.users || 0"
        :icon="User"
        icon-class="users"
        :trend="formatTrend(trends.users)"
        :loading="loading"
      />

      <StatCard
        label="公告数量"
        :value="stats.announcements || 0"
        :icon="Bell"
        icon-class="announcements"
        :trend="formatTrend(trends.announcements)"
        :loading="loading"
      />

      <StatCard
        label="反馈数量"
        :value="stats.feedback || 0"
        :icon="ChatDotRound"
        icon-class="feedback"
        :trend="formatTrend(trends.feedback)"
        :loading="loading"
      />

      <StatCard
        label="总积分"
        :value="stats.points || 0"
        :icon="Coin"
        icon-class="points"
        :trend="formatTrend(trends.points)"
        :loading="loading"
      />
    </div>

    <!-- 快速操作 -->
    <div class="quick-actions">
      <div class="card">
        <div class="card-header">
          <h3 class="card-title">快速操作</h3>
        </div>
        <div class="card-body">
          <div class="action-grid grid grid-cols-2 mobile-gap-2">
            <el-button
              type="primary"
              :icon="Plus"
              @click="goToCreateAnnouncement"
              class="btn-responsive"
            >
              创建公告
            </el-button>
            <el-button
              type="success"
              :icon="Setting"
              @click="$router.push('/settings')"
              class="btn-responsive"
            >
              系统设置
            </el-button>
            <el-button
              type="warning"
              :icon="User"
              @click="$router.push('/users')"
              class="btn-responsive"
            >
              用户管理
            </el-button>
            <el-button
              type="info"
              :icon="DataAnalysis"
              @click="$router.push('/settings')"
              class="btn-responsive"
            >
              系统监控
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 数据概览 -->
    <div class="data-overview">
      <div class="card">
        <div class="card-header">
          <h3 class="card-title">今日数据概览</h3>
          <el-button
            type="text"
            :icon="Refresh"
            @click="loadStats"
            :loading="loading"
          >
            刷新
          </el-button>
        </div>
        <div class="card-body">
          <div class="overview-grid">
            <div class="overview-item">
              <div class="overview-icon">
                <UserIcon :size="24" />
              </div>
              <div class="overview-content">
                <div class="overview-label">今日新增用户</div>
                <div class="overview-value">{{ todayStats.newUsers || 0 }}</div>
                <div class="overview-change positive">较昨日 +{{ todayStats.newUsersChange || 0 }}%</div>
              </div>
            </div>
            <div class="overview-item">
              <div class="overview-icon">
                <UserCheckIcon :size="24" />
              </div>
              <div class="overview-content">
                <div class="overview-label">今日签到人数</div>
                <div class="overview-value">{{ todayStats.checkIns || 0 }}</div>
                <div class="overview-change positive">较昨日 +{{ todayStats.checkInsChange || 0 }}%</div>
              </div>
            </div>
            <div class="overview-item">
              <div class="overview-icon">
                <CoinIcon :size="24" />
              </div>
              <div class="overview-content">
                <div class="overview-label">今日积分发放</div>
                <div class="overview-value">{{ todayStats.pointsIssued || 0 }}</div>
                <div class="overview-change positive">较昨日 +{{ todayStats.pointsIssuedChange || 0 }}%</div>
              </div>
            </div>
            <div class="overview-item">
              <div class="overview-icon">
                <BellIcon :size="24" />
              </div>
              <div class="overview-content">
                <div class="overview-label">今日反馈数量</div>
                <div class="overview-value">{{ todayStats.feedback || 0 }}</div>
                <div class="overview-change negative">较昨日 {{ todayStats.feedbackChange || 0 }}%</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  User,
  Bell,
  ChatDotRound,
  Coin,
  Plus,
  Setting,
  DataAnalysis,
  Refresh
} from '@element-plus/icons-vue'
import {
  User as UserIcon,
  UserCheck as UserCheckIcon,
  Coins as CoinIcon,
  Bell as BellIcon
} from 'lucide-vue-next'
import { useAuthStore } from '@/stores/auth.js'
import { useAppStore } from '@/stores/app.js'
import { getDashboardStats, getSystemStatus, getTrendData } from '@/api/dashboard.js'
import StatCard from '@/components/Charts/StatCard.vue'
import TrendChart from '@/components/Charts/TrendChart.vue'
import dayjs from 'dayjs'

const router = useRouter()
const authStore = useAuthStore()
const appStore = useAppStore()

const statusLoading = ref(false)
const stats = reactive({
  users: 0,
  announcements: 0,
  feedback: 0,
  points: 0
})

const trends = reactive({
  users: { change: '+0%', trend: 'up' },
  announcements: { change: '+0%', trend: 'up' },
  feedback: { change: '+0%', trend: 'up' },
  points: { change: '+0%', trend: 'up' }
})

const todayStats = reactive({
  newUsers: 0,
  newUsersChange: 0,
  checkIns: 0,
  checkInsChange: 0,
  pointsIssued: 0,
  pointsIssuedChange: 0,
  feedback: 0,
  feedbackChange: 0
})

// 格式化时间
function formatTime(timestamp) {
  if (!timestamp) return '未知'
  return dayjs(timestamp).format('YYYY-MM-DD HH:mm:ss')
}

// 加载统计数据
async function loadStats() {
  try {
    appStore.setLoading(true)

    // 并行加载统计数据和趋势数据
    const [statsResult, trendsResult] = await Promise.all([
      getDashboardStats('30d'),
      getTrendData('30d')
    ])

    if (statsResult.success) {
      Object.assign(stats, statsResult.data)
      console.log('统计数据加载成功:', statsResult.data)
    } else {
      throw new Error(statsResult.message || '获取统计数据失败')
    }

    if (trendsResult.success) {
      Object.assign(trends, trendsResult.data)
      console.log('趋势数据加载成功:', trendsResult.data)
    }

    // 加载今日统计数据
    await loadTodayStats()
  } catch (error) {
    console.error('加载统计数据失败:', error)
    ElMessage.error(error.message || '加载统计数据失败')

    // 失败时使用默认值
    stats.users = 0
    stats.announcements = 0
    stats.feedback = 0
    stats.points = 0
  } finally {
    appStore.setLoading(false)
  }
}

// 加载今日统计数据
async function loadTodayStats() {
  try {
    // 获取今日统计数据
    const today = new Date().toISOString().split('T')[0]

    // 模拟今日数据（实际应该调用真实API）
    const todayData = {
      newUsers: Math.floor(Math.random() * 50) + 10,
      checkIns: Math.floor(Math.random() * 200) + 50,
      pointsIssued: Math.floor(Math.random() * 5000) + 1000,
      feedback: Math.floor(Math.random() * 20) + 5
    }

    const yesterdayData = {
      newUsers: Math.floor(Math.random() * 40) + 8,
      checkIns: Math.floor(Math.random() * 180) + 40,
      pointsIssued: Math.floor(Math.random() * 4500) + 800,
      feedback: Math.floor(Math.random() * 25) + 3
    }

    // 计算变化百分比
    todayStats.newUsers = todayData.newUsers
    todayStats.newUsersChange = yesterdayData.newUsers > 0
      ? Math.round(((todayData.newUsers - yesterdayData.newUsers) / yesterdayData.newUsers) * 100)
      : 0

    todayStats.checkIns = todayData.checkIns
    todayStats.checkInsChange = yesterdayData.checkIns > 0
      ? Math.round(((todayData.checkIns - yesterdayData.checkIns) / yesterdayData.checkIns) * 100)
      : 0

    todayStats.pointsIssued = todayData.pointsIssued
    todayStats.pointsIssuedChange = yesterdayData.pointsIssued > 0
      ? Math.round(((todayData.pointsIssued - yesterdayData.pointsIssued) / yesterdayData.pointsIssued) * 100)
      : 0

    todayStats.feedback = todayData.feedback
    todayStats.feedbackChange = yesterdayData.feedback > 0
      ? Math.round(((todayData.feedback - yesterdayData.feedback) / yesterdayData.feedback) * 100)
      : 0

  } catch (error) {
    console.error('加载今日统计数据失败:', error)
  }
}

// 刷新系统状态
async function refreshStatus() {
  statusLoading.value = true
  try {
    // 并行刷新连接状态和统计数据
    await Promise.all([
      authStore.checkConnection(),
      loadStats()
    ])
    ElMessage.success('状态刷新成功')
  } catch (error) {
    console.error('刷新状态失败:', error)
    ElMessage.error('状态刷新失败')
  } finally {
    statusLoading.value = false
  }
}

function goToCreateAnnouncement() {
  // 跳转到公告管理页面，并通过URL参数标识要创建公告
  router.push('/announcements?action=create')
}

function formatTrend(trendData) {
  if (!trendData) return null

  return {
    type: trendData.trend,
    value: trendData.change,
    text: '较上期'
  }
}

onMounted(() => {
  loadStats()
})
</script>

<style scoped>
.dashboard {
  padding: 0;
}

.page-header {
  margin-bottom: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin-bottom: 8px;
}

.page-description {
  color: var(--el-text-color-regular);
  margin: 0;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 24px;
}

.stats-card {
  background: var(--el-bg-color);
  border: 1px solid var(--el-border-color-lighter);
  border-radius: 8px;
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 16px;
  transition: all 0.3s ease;
}

.stats-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.stats-icon {
  font-size: 32px;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--el-fill-color-lighter);
  border-radius: 50%;
}

.stats-content {
  flex: 1;
}

.stats-number {
  font-size: 28px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin-bottom: 4px;
}

.stats-label {
  font-size: 14px;
  color: var(--el-text-color-regular);
  margin-bottom: 4px;
}

.stats-change {
  font-size: 12px;
  font-weight: 500;
}

.stats-change.positive {
  color: var(--el-color-success);
}

.stats-change.negative {
  color: var(--el-color-danger);
}

.quick-actions {
  margin-bottom: 24px;
}

.action-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 12px;
}

.system-status {
  margin-bottom: 24px;
}

.card {
  background: var(--el-bg-color);
  border: 1px solid var(--el-border-color-lighter);
  border-radius: 8px;
  overflow: hidden;
}

.card-header {
  padding: 16px 20px;
  background: var(--el-fill-color-lighter);
  border-bottom: 1px solid var(--el-border-color-lighter);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.card-title {
  font-size: 16px;
  font-weight: 500;
  color: var(--el-text-color-primary);
  margin: 0;
}

.card-body {
  padding: 20px;
}

.overview-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.overview-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.overview-item:hover {
  background: #e9ecef;
  transform: translateY(-2px);
}

.overview-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  flex-shrink: 0;
}

.overview-content {
  flex: 1;
}

.overview-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 4px;
}

.overview-value {
  font-size: 24px;
  font-weight: 700;
  color: #333;
  margin-bottom: 4px;
}

.overview-change {
  font-size: 12px;
  font-weight: 500;
}

.overview-change.positive {
  color: #10b981;
}

.overview-change.negative {
  color: #ef4444;
}

@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }

  .action-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .overview-grid {
    grid-template-columns: 1fr;
  }

  .overview-item {
    padding: 12px;
    gap: 12px;
  }

  .overview-icon {
    width: 40px;
    height: 40px;
  }

  .overview-value {
    font-size: 20px;
  }
}
</style>
