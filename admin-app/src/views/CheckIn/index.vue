<template>
  <div class="checkin-container">
    <div class="page-header">
      <div class="header-left">
        <h1 class="page-title">
          <CalendarCheckIcon :size="24" />
          签到管理
        </h1>
        <p class="page-description">管理用户签到记录和配置</p>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="refreshData">
          <template #icon>
            <RefreshCwIcon :size="16" />
          </template>
          刷新
        </el-button>
        <el-button type="success" @click="showConfigDialog">
          <template #icon>
            <SettingsIcon :size="16" />
          </template>
          签到配置
        </el-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-grid">
      <div class="stat-card">
        <div class="stat-icon total">
          <CalendarCheckIcon :size="24" />
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stats.total || 0 }}</div>
          <div class="stat-label">总签到次数</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon today">
          <CalendarIcon :size="24" />
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stats.todayCount || 0 }}</div>
          <div class="stat-label">今日签到</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon users">
          <UsersIcon :size="24" />
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stats.activeUsers || 0 }}</div>
          <div class="stat-label">活跃用户</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon consecutive">
          <TrendingUpIcon :size="24" />
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stats.avgConsecutive || 0 }}</div>
          <div class="stat-label">平均连续天数</div>
        </div>
      </div>
    </div>

    <!-- 功能标签页 -->
    <el-tabs v-model="activeTab" class="checkin-tabs">
      <el-tab-pane label="签到记录" name="records">
        <!-- 筛选和搜索 -->
        <div class="filter-section">
          <el-form :model="filters" inline class="filter-form">
            <el-form-item label="用户">
              <el-input
                v-model="filters.userId"
                placeholder="输入用户ID"
                style="width: 200px"
              >
                <template #prefix>
                  <UserIcon :size="16" />
                </template>
              </el-input>
            </el-form-item>
            <el-form-item label="日期范围">
              <el-date-picker
                v-model="filters.dateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                style="width: 240px"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="loadCheckInRecords">
                <template #icon>
                  <SearchIcon :size="16" />
                </template>
                搜索
              </el-button>
              <el-button @click="resetFilters">
                <template #icon>
                  <RotateCcwIcon :size="16" />
                </template>
                重置
              </el-button>
              <el-button type="success" @click="exportRecords">
                <template #icon>
                  <DownloadIcon :size="16" />
                </template>
                导出
              </el-button>
            </el-form-item>
          </el-form>
        </div>

        <!-- 签到记录表格 -->
        <div class="table-section">
          <el-table
            v-loading="loading"
            :data="checkInRecords"
            stripe
            style="width: 100%"
          >
            <el-table-column prop="userId" label="用户ID" width="200" />
            <el-table-column prop="nickname" label="昵称" min-width="150" />
            <el-table-column prop="date" label="签到日期" width="120">
              <template #default="{ row }">
                {{ formatDate(row.date, 'date') }}
              </template>
            </el-table-column>
            <el-table-column prop="checkInAt" label="签到时间" width="180">
              <template #default="{ row }">
                {{ formatDate(row.checkInAt) }}
              </template>
            </el-table-column>
            <el-table-column prop="consecutiveDays" label="连续天数" width="100">
              <template #default="{ row }">
                <el-tag :type="getConsecutiveColor(row.consecutiveDays)" size="small">
                  {{ row.consecutiveDays || 0 }} 天
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="reward" label="获得积分" width="100">
              <template #default="{ row }">
                <span class="points-text">+{{ row.reward || 0 }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="bonusPoints" label="奖励积分" width="100">
              <template #default="{ row }">
                <span v-if="row.bonusPoints > 0" class="bonus-text">+{{ row.bonusPoints }}</span>
                <span v-else>-</span>
              </template>
            </el-table-column>
            <el-table-column prop="device" label="设备信息" min-width="150">
              <template #default="{ row }">
                <div class="device-info">
                  <div>{{ row.device?.platform || '-' }}</div>
                  <div class="device-detail">{{ row.device?.model || '-' }}</div>
                </div>
              </template>
            </el-table-column>
          </el-table>

          <!-- 分页 -->
          <div class="pagination-wrapper">
            <el-pagination
              v-model:current-page="pagination.page"
              v-model:page-size="pagination.pageSize"
              :total="pagination.total"
              :page-sizes="[10, 20, 50, 100]"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            />
          </div>
        </div>
      </el-tab-pane>

      <el-tab-pane label="签到统计" name="statistics">
        <div class="statistics-section">
          <!-- 日期选择 -->
          <div class="stats-filter">
            <el-form inline>
              <el-form-item label="统计周期">
                <el-select v-model="statsPeriod" @change="loadStatistics">
                  <el-option label="最近7天" value="7d" />
                  <el-option label="最近30天" value="30d" />
                  <el-option label="最近90天" value="90d" />
                  <el-option label="自定义" value="custom" />
                </el-select>
              </el-form-item>
              <el-form-item v-if="statsPeriod === 'custom'" label="日期范围">
                <el-date-picker
                  v-model="customDateRange"
                  type="daterange"
                  @change="loadStatistics"
                />
              </el-form-item>
            </el-form>
          </div>

          <!-- 统计图表区域 -->
          <div class="charts-grid">
            <div class="chart-card">
              <h4>每日签到趋势</h4>
              <div class="chart-placeholder">
                <!-- TODO: 集成图表库显示签到趋势 -->
                <p>签到趋势图表</p>
              </div>
            </div>
            <div class="chart-card">
              <h4>连续签到分布</h4>
              <div class="chart-placeholder">
                <!-- TODO: 集成图表库显示连续签到分布 -->
                <p>连续签到分布图</p>
              </div>
            </div>
          </div>

          <!-- 排行榜 -->
          <div class="leaderboard-section">
            <h4>签到排行榜</h4>
            <el-table
              v-loading="leaderboardLoading"
              :data="checkInLeaderboard"
              stripe
              style="width: 100%"
            >
              <el-table-column label="排名" width="80">
                <template #default="{ $index }">
                  <div class="rank-badge" :class="getRankClass($index)">
                    {{ $index + 1 }}
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="userId" label="用户ID" width="200" />
              <el-table-column prop="nickname" label="昵称" min-width="150" />
              <el-table-column prop="totalCheckIns" label="总签到天数" width="120" />
              <el-table-column prop="consecutiveDays" label="连续天数" width="120">
                <template #default="{ row }">
                  <el-tag :type="getConsecutiveColor(row.consecutiveDays)" size="small">
                    {{ row.consecutiveDays }} 天
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="totalPoints" label="累计积分" width="120">
                <template #default="{ row }">
                  <span class="points-text">{{ row.totalPoints }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="lastCheckIn" label="最后签到" width="180">
                <template #default="{ row }">
                  {{ formatDate(row.lastCheckIn) }}
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>

    <!-- 签到配置对话框 -->
    <el-dialog
      v-model="configDialogVisible"
      title="签到配置"
      width="600px"
      @close="loadCheckInConfig"
    >
      <div v-loading="configLoading" class="config-content">
        <el-form
          ref="configFormRef"
          :model="checkInConfig"
          label-width="150px"
        >
          <el-form-item label="基础积分奖励">
            <el-input-number
              v-model="checkInConfig.basePoints"
              :min="0"
              :max="1000"
            />
            <span class="form-help">每次签到获得的基础积分</span>
          </el-form-item>
          <el-form-item label="连续签到奖励">
            <el-input-number
              v-model="checkInConfig.consecutiveBonus"
              :min="0"
              :max="1000"
            />
            <span class="form-help">连续签到额外奖励积分</span>
          </el-form-item>
          <el-form-item label="连续签到阈值">
            <el-input-number
              v-model="checkInConfig.consecutiveThreshold"
              :min="1"
              :max="365"
            />
            <span class="form-help">连续多少天开始获得奖励</span>
          </el-form-item>
          <el-form-item label="每日签到限制">
            <el-switch
              v-model="checkInConfig.dailyLimit"
              active-text="启用"
              inactive-text="禁用"
            />
            <span class="form-help">是否限制每日只能签到一次</span>
          </el-form-item>
          <el-form-item label="签到时间限制">
            <el-time-picker
              v-model="checkInConfig.timeRange"
              is-range
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
            />
            <span class="form-help">允许签到的时间范围</span>
          </el-form-item>
        </el-form>
      </div>
      
      <template #footer>
        <el-button @click="configDialogVisible = false">取消</el-button>
        <el-button
          type="primary"
          :loading="configSaving"
          @click="saveCheckInConfig"
        >
          保存配置
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import {
  CalendarCheck as CalendarCheckIcon,
  Calendar as CalendarIcon,
  Users as UsersIcon,
  TrendingUp as TrendingUpIcon,
  RefreshCw as RefreshCwIcon,
  Settings as SettingsIcon,
  User as UserIcon,
  Search as SearchIcon,
  RotateCcw as RotateCcwIcon,
  Download as DownloadIcon
} from 'lucide-vue-next'
import { callCloudFunction } from '@/api/wechat-api.js'

// 响应式数据
const loading = ref(false)
const leaderboardLoading = ref(false)
const configLoading = ref(false)
const configSaving = ref(false)
const activeTab = ref('records')
const checkInRecords = ref([])
const checkInLeaderboard = ref([])
const stats = ref({})
const configDialogVisible = ref(false)
const configFormRef = ref()
const statsPeriod = ref('30d')
const customDateRange = ref([])

const filters = reactive({
  userId: '',
  dateRange: []
})

const pagination = reactive({
  page: 1,
  pageSize: 20,
  total: 0
})

const checkInConfig = reactive({
  basePoints: 10,
  consecutiveBonus: 5,
  consecutiveThreshold: 3,
  dailyLimit: true,
  timeRange: []
})

// 方法
async function loadCheckInRecords() {
  try {
    loading.value = true
    
    const params = {
      page: pagination.page,
      pageSize: pagination.pageSize
    }
    
    if (filters.userId.trim()) {
      params.userId = filters.userId.trim()
    }
    
    if (filters.dateRange && filters.dateRange.length === 2) {
      params.startDate = filters.dateRange[0].toISOString()
      params.endDate = filters.dateRange[1].toISOString()
    }
    
    const result = await callCloudFunction('getUserCheckInHistoryAdmin', params)

    if (result.success) {
      checkInRecords.value = result.data || []
      pagination.total = result.pagination?.total || 0
    } else {
      ElMessage.error(result.message || '获取签到记录失败')
    }
  } catch (error) {
    console.error('获取签到记录失败:', error)
    ElMessage.error('获取签到记录失败')
  } finally {
    loading.value = false
  }
}

async function loadLeaderboard() {
  try {
    leaderboardLoading.value = true
    
    const result = await callCloudFunction('getCheckInLeaderboard', {
      limit: 50,
      period: statsPeriod.value
    })

    if (result.success) {
      checkInLeaderboard.value = result.data || []
    } else {
      ElMessage.error(result.message || '获取排行榜失败')
    }
  } catch (error) {
    console.error('获取排行榜失败:', error)
    ElMessage.error('获取排行榜失败')
  } finally {
    leaderboardLoading.value = false
  }
}

async function loadStats() {
  try {
    const result = await callCloudFunction('getCheckInStatsAdmin', { 
      period: statsPeriod.value 
    })
    if (result.success) {
      stats.value = result.data || {}
    }
  } catch (error) {
    console.error('获取签到统计失败:', error)
  }
}

async function loadStatistics() {
  await Promise.all([loadStats(), loadLeaderboard()])
}

async function loadCheckInConfig() {
  try {
    configLoading.value = true
    
    const result = await callCloudFunction('getCheckInConfig')
    if (result.success && result.data) {
      Object.assign(checkInConfig, result.data)
    }
  } catch (error) {
    console.error('获取签到配置失败:', error)
  } finally {
    configLoading.value = false
  }
}

function resetFilters() {
  filters.userId = ''
  filters.dateRange = []
  pagination.page = 1
  loadCheckInRecords()
}

function handleSizeChange(size) {
  pagination.pageSize = size
  pagination.page = 1
  loadCheckInRecords()
}

function handleCurrentChange(page) {
  pagination.page = page
  loadCheckInRecords()
}

async function refreshData() {
  await Promise.all([
    loadCheckInRecords(),
    loadStats(),
    loadLeaderboard()
  ])
  ElMessage.success('数据已刷新')
}

function showConfigDialog() {
  configDialogVisible.value = true
  loadCheckInConfig()
}

async function saveCheckInConfig() {
  try {
    configSaving.value = true

    const result = await callCloudFunction('updateCheckInConfig', checkInConfig)

    if (result.success) {
      ElMessage.success('签到配置保存成功')
      configDialogVisible.value = false
    } else {
      ElMessage.error(result.message || '保存配置失败')
    }
  } catch (error) {
    console.error('保存配置失败:', error)
    ElMessage.error('保存配置失败')
  } finally {
    configSaving.value = false
  }
}

async function exportRecords() {
  try {
    const params = {
      format: 'json',
      limit: 1000
    }
    
    if (filters.userId.trim()) {
      params.userId = filters.userId.trim()
    }
    
    if (filters.dateRange && filters.dateRange.length === 2) {
      params.startDate = filters.dateRange[0].toISOString()
      params.endDate = filters.dateRange[1].toISOString()
    }
    
    const result = await callCloudFunction('exportCheckInData', params)

    if (result.success) {
      ElMessage.success('数据导出成功')
    } else {
      ElMessage.error(result.message || '导出失败')
    }
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败')
  }
}

// 辅助函数
function getConsecutiveColor(days) {
  if (days >= 30) return 'danger'
  if (days >= 7) return 'warning'
  if (days >= 3) return 'success'
  return 'info'
}

function getRankClass(index) {
  if (index === 0) return 'rank-gold'
  if (index === 1) return 'rank-silver'
  if (index === 2) return 'rank-bronze'
  return 'rank-normal'
}

function formatDate(dateString, type = 'datetime') {
  if (!dateString) return '-'
  const date = new Date(dateString)
  if (type === 'date') {
    return date.toLocaleDateString('zh-CN')
  }
  return date.toLocaleString('zh-CN')
}

// 生命周期
onMounted(() => {
  loadCheckInRecords()
  loadStats()
  loadLeaderboard()
})
</script>

<style scoped>
.checkin-container {
  padding: 24px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 8px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.page-description {
  color: #6b7280;
  margin: 0;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.stat-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.stat-icon.total { background: #3b82f6; }
.stat-icon.today { background: #10b981; }
.stat-icon.users { background: #f59e0b; }
.stat-icon.consecutive { background: #8b5cf6; }

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
}

.stat-label {
  font-size: 14px;
  color: #6b7280;
}

.checkin-tabs {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.filter-section {
  margin-bottom: 20px;
  padding: 16px;
  background: #f9fafb;
  border-radius: 8px;
}

.table-section {
  margin-top: 16px;
}

.statistics-section {
  margin-top: 16px;
}

.stats-filter {
  margin-bottom: 20px;
  padding: 16px;
  background: #f9fafb;
  border-radius: 8px;
}

.charts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 20px;
  margin-bottom: 24px;
}

.chart-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.chart-card h4 {
  margin: 0 0 16px 0;
  color: #1f2937;
}

.chart-placeholder {
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f9fafb;
  border-radius: 8px;
  color: #6b7280;
}

.leaderboard-section {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.leaderboard-section h4 {
  margin: 0 0 16px 0;
  color: #1f2937;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.rank-badge {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  color: white;
}

.rank-gold { background: #fbbf24; }
.rank-silver { background: #9ca3af; }
.rank-bronze { background: #d97706; }
.rank-normal { background: #6b7280; }

.points-text {
  font-weight: 600;
  color: #10b981;
}

.bonus-text {
  font-weight: 600;
  color: #f59e0b;
}

.device-info {
  font-size: 14px;
}

.device-detail {
  color: #6b7280;
  font-size: 12px;
}

.config-content {
  min-height: 200px;
}

.form-help {
  font-size: 12px;
  color: #6b7280;
  margin-left: 8px;
}
</style>
