<template>
  <div class="friend-apps-container">
    <div class="page-header">
      <div class="header-left">
        <h1 class="page-title">
          <LinkIcon :size="24" />
          友情应用
        </h1>
        <p class="page-description">管理友情链接和推荐应用</p>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="refreshData">
          <template #icon>
            <RefreshCwIcon :size="16" />
          </template>
          刷新
        </el-button>
        <el-button type="success" @click="showCreateDialog">
          <template #icon>
            <PlusIcon :size="16" />
          </template>
          添加应用
        </el-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-grid">
      <div class="stat-card">
        <div class="stat-icon total">
          <LinkIcon :size="24" />
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stats.total || 0 }}</div>
          <div class="stat-label">总应用数</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon visible">
          <EyeIcon :size="24" />
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stats.visible || 0 }}</div>
          <div class="stat-label">显示中</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon clicks">
          <MousePointerClickIcon :size="24" />
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stats.totalClicks || 0 }}</div>
          <div class="stat-label">总点击量</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon rate">
          <TrendingUpIcon :size="24" />
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stats.clickRate || 0 }}%</div>
          <div class="stat-label">点击率</div>
        </div>
      </div>
    </div>

    <!-- 筛选和搜索 -->
    <div class="filter-section">
      <el-form :model="filters" inline class="filter-form">
        <el-form-item label="分类">
          <el-select v-model="filters.category" placeholder="选择分类" style="width: 120px">
            <el-option label="全部" value="all" />
            <el-option label="工具" value="tool" />
            <el-option label="娱乐" value="entertainment" />
            <el-option label="学习" value="education" />
            <el-option label="生活" value="lifestyle" />
            <el-option label="其他" value="other" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="filters.visible" placeholder="显示状态" style="width: 120px">
            <el-option label="全部" value="all" />
            <el-option label="显示" value="true" />
            <el-option label="隐藏" value="false" />
          </el-select>
        </el-form-item>
        <el-form-item label="搜索">
          <el-input
            v-model="filters.keyword"
            placeholder="搜索应用名称或描述"
            style="width: 250px"
            @keyup.enter="handleSearch"
          >
            <template #prefix>
              <SearchIcon :size="16" />
            </template>
          </el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <template #icon>
              <SearchIcon :size="16" />
            </template>
            搜索
          </el-button>
          <el-button @click="resetFilters">
            <template #icon>
              <RotateCcwIcon :size="16" />
            </template>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 应用列表 -->
    <div class="table-section">
      <el-table
        v-loading="loading"
        :data="friendAppsList"
        stripe
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="icon" label="图标" width="80">
          <template #default="{ row }">
            <el-image
              v-if="row.icon"
              :src="row.icon"
              :alt="row.name"
              style="width: 40px; height: 40px; border-radius: 8px;"
              fit="cover"
            />
            <div v-else class="no-icon">
              <AppWindowIcon :size="20" />
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="name" label="应用名称" min-width="150" />
        <el-table-column prop="description" label="描述" min-width="200" />
        <el-table-column prop="category" label="分类" width="100">
          <template #default="{ row }">
            <el-tag size="small">{{ getCategoryText(row.category) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="url" label="链接" min-width="200">
          <template #default="{ row }">
            <el-link @click="recordClick(row)" target="_blank" type="primary">
              {{ row.url }}
            </el-link>
          </template>
        </el-table-column>
        <el-table-column prop="clickCount" label="点击量" width="100" />
        <el-table-column prop="sortOrder" label="排序" width="80" />
        <el-table-column prop="isVisible" label="状态" width="80">
          <template #default="{ row }">
            <el-switch
              v-model="row.isVisible"
              @change="toggleVisibility(row)"
            />
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatDate(row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button
              type="primary"
              size="small"
              @click="editApp(row)"
            >
              <template #icon>
                <EditIcon :size="14" />
              </template>
              编辑
            </el-button>
            <el-button
              type="danger"
              size="small"
              @click="deleteApp(row)"
            >
              <template #icon>
                <TrashIcon :size="14" />
              </template>
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.pageSize"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 批量操作 -->
    <div v-if="selectedApps.length > 0" class="batch-actions">
      <div class="batch-info">
        已选择 {{ selectedApps.length }} 个应用
      </div>
      <div class="batch-buttons">
        <el-button type="success" @click="batchToggleVisibility(true)">
          批量显示
        </el-button>
        <el-button type="warning" @click="batchToggleVisibility(false)">
          批量隐藏
        </el-button>
        <el-button type="danger" @click="batchDelete">
          批量删除
        </el-button>
      </div>
    </div>

    <!-- 应用编辑对话框 -->
    <el-dialog
      v-model="appDialogVisible"
      :title="isEditing ? '编辑应用' : '添加应用'"
      width="600px"
      @close="resetAppForm"
    >
      <el-form
        ref="appFormRef"
        :model="appForm"
        :rules="appRules"
        label-width="100px"
      >
        <el-form-item label="应用名称" prop="name">
          <el-input v-model="appForm.name" placeholder="请输入应用名称" />
        </el-form-item>
        <el-form-item label="应用描述" prop="description">
          <el-input
            v-model="appForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入应用描述"
          />
        </el-form-item>
        <el-form-item label="应用链接" prop="url">
          <el-input v-model="appForm.url" placeholder="请输入应用链接" />
        </el-form-item>
        <el-form-item label="应用图标" prop="icon">
          <el-input v-model="appForm.icon" placeholder="请输入图标链接" />
        </el-form-item>
        <el-form-item label="分类" prop="category">
          <el-select v-model="appForm.category" style="width: 100%">
            <el-option label="工具" value="tool" />
            <el-option label="娱乐" value="entertainment" />
            <el-option label="学习" value="education" />
            <el-option label="生活" value="lifestyle" />
            <el-option label="其他" value="other" />
          </el-select>
        </el-form-item>
        <el-form-item label="排序权重" prop="sortOrder">
          <el-input-number
            v-model="appForm.sortOrder"
            :min="0"
            :max="999"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="显示状态">
          <el-switch
            v-model="appForm.visible"
            active-text="显示"
            inactive-text="隐藏"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="appDialogVisible = false">取消</el-button>
        <el-button
          type="primary"
          :loading="saving"
          @click="saveApp"
        >
          保存
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Link as LinkIcon,
  Eye as EyeIcon,
  MousePointerClick as MousePointerClickIcon,
  TrendingUp as TrendingUpIcon,
  RefreshCw as RefreshCwIcon,
  Plus as PlusIcon,
  Search as SearchIcon,
  RotateCcw as RotateCcwIcon,
  AppWindow as AppWindowIcon,
  Edit as EditIcon,
  Trash as TrashIcon
} from 'lucide-vue-next'
import { callCloudFunction } from '@/api/wechat-api.js'

// 响应式数据
const loading = ref(false)
const saving = ref(false)
const friendAppsList = ref([])
const selectedApps = ref([])
const stats = ref({})
const appDialogVisible = ref(false)
const isEditing = ref(false)
const appFormRef = ref()

const filters = reactive({
  category: 'all',
  visible: 'all',
  keyword: ''
})

const pagination = reactive({
  page: 1,
  pageSize: 20,
  total: 0
})

const appForm = reactive({
  name: '',
  description: '',
  url: '',
  icon: '',
  category: 'tool',
  sortOrder: 0,
  visible: true
})

const appRules = {
  name: [
    { required: true, message: '请输入应用名称', trigger: 'blur' }
  ],
  description: [
    { required: true, message: '请输入应用描述', trigger: 'blur' }
  ],
  url: [
    { required: true, message: '请输入应用链接', trigger: 'blur' },
    { type: 'url', message: '请输入有效的URL', trigger: 'blur' }
  ],
  category: [
    { required: true, message: '请选择分类', trigger: 'change' }
  ]
}

// 方法
async function loadFriendAppsList() {
  try {
    loading.value = true
    
    const params = {
      page: pagination.page,
      pageSize: pagination.pageSize
    }
    
    if (filters.category !== 'all') {
      params.category = filters.category
    }
    if (filters.visible !== 'all') {
      params.visible = filters.visible === 'true'
    }
    if (filters.keyword.trim()) {
      params.keyword = filters.keyword.trim()
    }
    
    const result = await callCloudFunction('getFriendAppListAdmin', params)

    if (result.success) {
      friendAppsList.value = result.data || []
      pagination.total = result.pagination?.total || 0
    } else {
      ElMessage.error(result.message || '获取友情应用列表失败')
    }
  } catch (error) {
    console.error('获取友情应用列表失败:', error)
    ElMessage.error('获取友情应用列表失败')
  } finally {
    loading.value = false
  }
}

async function loadStats() {
  try {
    const result = await callCloudFunction('getFriendAppStatsAdmin', { period: '30d' })
    if (result.success) {
      stats.value = result.data || {}
    }
  } catch (error) {
    console.error('获取友情应用统计失败:', error)
  }
}

function handleSearch() {
  pagination.page = 1
  loadFriendAppsList()
}

function resetFilters() {
  filters.category = 'all'
  filters.visible = 'all'
  filters.keyword = ''
  pagination.page = 1
  loadFriendAppsList()
}

function handleSizeChange(size) {
  pagination.pageSize = size
  pagination.page = 1
  loadFriendAppsList()
}

function handleCurrentChange(page) {
  pagination.page = page
  loadFriendAppsList()
}

function handleSelectionChange(selection) {
  selectedApps.value = selection
}

async function refreshData() {
  await Promise.all([loadFriendAppsList(), loadStats()])
  ElMessage.success('数据已刷新')
}

function showCreateDialog() {
  isEditing.value = false
  resetAppForm()
  appDialogVisible.value = true
}

function editApp(app) {
  isEditing.value = true
  Object.assign(appForm, app)
  appDialogVisible.value = true
}

function resetAppForm() {
  Object.assign(appForm, {
    name: '',
    description: '',
    url: '',
    icon: '',
    category: 'tool',
    sortOrder: 0,
    visible: true
  })
  appFormRef.value?.clearValidate()
}

async function saveApp() {
  try {
    const valid = await appFormRef.value.validate()
    if (!valid) return

    saving.value = true

    const apiType = isEditing.value ? 'updateFriendApp' : 'createFriendApp'
    const data = { ...appForm }

    if (isEditing.value) {
      data.id = appForm._id
    }

    const result = await callCloudFunction(apiType, data)

    if (result.success) {
      ElMessage.success(isEditing.value ? '应用更新成功' : '应用创建成功')
      appDialogVisible.value = false
      loadFriendAppsList()
    } else {
      ElMessage.error(result.message || '保存应用失败')
    }
  } catch (error) {
    console.error('保存应用失败:', error)
    ElMessage.error('保存应用失败')
  } finally {
    saving.value = false
  }
}

async function toggleVisibility(app) {
  try {
    const result = await callCloudFunction('toggleFriendAppStatus', {
      id: app._id,
      isVisible: app.isVisible
    })

    if (result.success) {
      ElMessage.success('状态更新成功')
      loadStats()
    } else {
      // 恢复原状态
      app.isVisible = !app.isVisible
      ElMessage.error(result.message || '状态更新失败')
    }
  } catch (error) {
    // 恢复原状态
    app.isVisible = !app.isVisible
    console.error('状态更新失败:', error)
    ElMessage.error('状态更新失败')
  }
}

async function deleteApp(app) {
  try {
    await ElMessageBox.confirm(
      `确定要删除应用 "${app.name}" 吗？`,
      '删除应用',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const result = await callCloudFunction('deleteFriendApp', {
      id: app._id
    })

    if (result.success) {
      ElMessage.success('应用删除成功')
      loadFriendAppsList()
    } else {
      ElMessage.error(result.message || '删除应用失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除应用失败:', error)
      ElMessage.error('删除应用失败')
    }
  }
}

async function recordClick(app) {
  try {
    // 记录点击
    await callCloudFunction('recordFriendAppClick', {
      id: app._id
    })

    // 更新本地点击计数
    app.clickCount = (app.clickCount || 0) + 1

    // 打开链接
    window.open(app.url, '_blank')
  } catch (error) {
    console.error('记录点击失败:', error)
    // 即使记录失败也要打开链接
    window.open(app.url, '_blank')
  }
}

async function batchToggleVisibility(visible) {
  try {
    const action = visible ? '显示' : '隐藏'
    
    await ElMessageBox.confirm(
      `确定要${action} ${selectedApps.value.length} 个应用吗？`,
      `批量${action}`,
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // TODO: 实现批量切换显示状态API
    ElMessage.success(`批量${action}成功`)
    selectedApps.value = []
    loadFriendAppsList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量操作失败:', error)
      ElMessage.error('批量操作失败')
    }
  }
}

async function batchDelete() {
  try {
    await ElMessageBox.confirm(
      `确定要删除 ${selectedApps.value.length} 个应用吗？此操作不可恢复。`,
      '批量删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // TODO: 实现批量删除API
    ElMessage.success('批量删除成功')
    selectedApps.value = []
    loadFriendAppsList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除失败:', error)
      ElMessage.error('批量删除失败')
    }
  }
}

// 辅助函数
function getCategoryText(category) {
  const texts = {
    tool: '工具',
    entertainment: '娱乐',
    education: '学习',
    lifestyle: '生活',
    other: '其他'
  }
  return texts[category] || category
}

function formatDate(dateString) {
  if (!dateString) return '-'
  return new Date(dateString).toLocaleString('zh-CN')
}

// 生命周期
onMounted(() => {
  loadFriendAppsList()
  loadStats()
})
</script>

<style scoped>
.friend-apps-container {
  padding: 24px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 8px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.page-description {
  color: #6b7280;
  margin: 0;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.stat-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.stat-icon.total { background: #3b82f6; }
.stat-icon.visible { background: #10b981; }
.stat-icon.clicks { background: #f59e0b; }
.stat-icon.rate { background: #8b5cf6; }

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
}

.stat-label {
  font-size: 14px;
  color: #6b7280;
}

.filter-section {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.table-section {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.batch-actions {
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  background: white;
  border-radius: 8px;
  padding: 16px 24px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  display: flex;
  align-items: center;
  gap: 16px;
  z-index: 1000;
}

.batch-info {
  font-weight: 500;
  color: #374151;
}

.batch-buttons {
  display: flex;
  gap: 8px;
}

.no-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background: #f3f4f6;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #9ca3af;
}
</style>
