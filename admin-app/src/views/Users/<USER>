<template>
  <div class="users-container">
    <div class="page-header">
      <div class="header-left">
        <h1 class="page-title">
          <UsersIcon :size="24" />
          用户管理
        </h1>
        <p class="page-description">管理系统用户信息和状态</p>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="refreshData">
          <template #icon>
            <RefreshCwIcon :size="16" />
          </template>
          刷新
        </el-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-grid">
      <div class="stat-card">
        <div class="stat-icon total">
          <UsersIcon :size="24" />
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stats.total || 0 }}</div>
          <div class="stat-label">总用户数</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon active">
          <UserCheckIcon :size="24" />
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stats.active || 0 }}</div>
          <div class="stat-label">活跃用户</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon vip">
          <CrownIcon :size="24" />
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stats.vip || 0 }}</div>
          <div class="stat-label">VIP用户</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon new">
          <UserPlusIcon :size="24" />
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stats.todayNew || 0 }}</div>
          <div class="stat-label">今日新增</div>
        </div>
      </div>
    </div>

    <!-- 筛选和搜索 -->
    <div class="filter-section card-responsive">
      <el-form :model="filters" class="filter-form form-responsive">
        <div class="grid grid-cols-4 gap-4">


          <el-form-item label="VIP状态">
            <el-select v-model="filters.vipStatus" placeholder="VIP状态" class="mobile-w-full">
              <el-option label="全部" value="all" />
              <el-option label="VIP" value="true" />
              <el-option label="普通" value="false" />
            </el-select>
          </el-form-item>

          <el-form-item label="排序方式">
            <el-select v-model="filters.sortBy" placeholder="排序字段" class="mobile-w-full">
              <el-option label="注册时间" value="createTime" />
              <el-option label="最后登录" value="lastLoginTime" />
              <el-option label="积分数量" value="points" />
              <el-option label="用户编号" value="no" />
            </el-select>
          </el-form-item>

          <el-form-item label="排序方向">
            <el-select v-model="filters.sortOrder" class="mobile-w-full">
              <el-option label="降序" value="desc" />
              <el-option label="升序" value="asc" />
            </el-select>
          </el-form-item>
        </div>

        <div class="search-row flex gap-4 items-end">
          <el-form-item label="搜索" class="flex-1">
            <el-input
              v-model="filters.keyword"
              placeholder="搜索用户名、昵称、邮箱或OpenID"
              @keyup.enter="handleSearch"
              class="mobile-w-full"
            >
              <template #prefix>
                <SearchIcon :size="16" />
              </template>
            </el-input>
          </el-form-item>

          <div class="search-actions flex gap-2">
            <el-button type="primary" @click="handleSearch" class="btn-responsive">
              <template #icon>
                <SearchIcon :size="16" />
              </template>
              搜索
            </el-button>
            <el-button @click="resetFilters" class="btn-responsive">
              <template #icon>
                <RotateCcwIcon :size="16" />
              </template>
              重置
            </el-button>
            <el-button @click="loadUserList" class="btn-responsive">
              <template #icon>
                <RefreshCwIcon :size="16" />
              </template>
              刷新
            </el-button>
            <el-button type="success" @click="exportUserData" class="btn-responsive">
              <template #icon>
                <DownloadIcon :size="16" />
              </template>
              导出
            </el-button>
          </div>
        </div>
      </el-form>
    </div>

    <!-- 标签页 -->
    <div class="tabs-section">
      <el-tabs v-model="activeTab" @tab-change="handleTabChange">
        <!-- 用户管理标签页 -->
        <el-tab-pane label="用户管理" name="users">
          <div class="table-section">
            <el-table
              v-loading="loading"
              :data="userList"
              stripe
              style="width: 100%"
              @selection-change="handleSelectionChange"
            >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="avatar" label="头像" width="80">
          <template #default="{ row }">
            <el-avatar :size="40" :src="row.avatar" :alt="row.nickname">
              <UserIcon :size="20" />
            </el-avatar>
          </template>
        </el-table-column>
        <el-table-column prop="nickname" label="昵称" min-width="150" />
        <el-table-column prop="no" label="用户编号" width="100" />
        <el-table-column prop="openid" label="OpenID" min-width="200">
          <template #default="{ row }">
            <span class="openid-text">{{ row.openid ? row.openid.substring(0, 20) + '...' : '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag
              :type="getStatusType(row.status)"
              size="small"
            >
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="vip" label="VIP状态" width="100">
          <template #default="{ row }">
            <el-tag v-if="row.vip?.status" type="warning" size="small">
              <CrownIcon :size="12" />
              VIP
            </el-tag>
            <span v-else class="text-gray-400">普通</span>
          </template>
        </el-table-column>
        <el-table-column prop="vip.expireAt" label="VIP到期时间" width="180">
          <template #default="{ row }">
            <span v-if="row.vip?.status && row.vip?.expireAt" :class="getVipExpiryClass(row.vip.expireAt)">
              {{ formatDate(row.vip.expireAt) }}
            </span>
            <span v-else class="text-gray-400">-</span>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="注册时间" width="220">
          <template #default="{ row }">
            {{ formatDateWithRelative(row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column prop="lastLoginTime" label="最后登录" width="220">
          <template #default="{ row }">
            {{ formatDateWithRelative(row.lastLoginTime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="280" fixed="right">
          <template #default="{ row }">
            <el-button
              type="primary"
              size="small"
              @click="viewUserDetail(row)"
            >
              <template #icon>
                <EyeIcon :size="14" />
              </template>
              查看
            </el-button>
            <el-button
              type="success"
              size="small"
              @click="editUser(row)"
            >
              <template #icon>
                <UserIcon :size="14" />
              </template>
              编辑
            </el-button>
            <el-button
              :type="row.status === 'active' ? 'warning' : 'success'"
              size="small"
              @click="toggleUserStatus(row)"
            >
              {{ row.status === 'active' ? '禁用' : '启用' }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.pageSize"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>

      <!-- 批量操作 -->
      <div v-if="selectedUsers.length > 0" class="batch-actions">
        <div class="batch-info">
          已选择 {{ selectedUsers.length }} 个用户
        </div>
        <div class="batch-buttons">
          <el-button type="success" @click="batchUpdateStatus('active')">
            批量启用
          </el-button>
          <el-button type="warning" @click="batchUpdateStatus('inactive')">
            批量禁用
          </el-button>
          <el-button type="danger" @click="batchUpdateStatus('banned')">
            批量封禁
          </el-button>
        </div>
      </div>
    </div>
  </el-tab-pane>

  <!-- VIP管理标签页 -->
  <el-tab-pane label="VIP管理" name="vip">
    <div class="vip-management">
      <!-- VIP统计卡片 -->
      <div class="stats-grid">
        <div class="stat-card">
          <div class="stat-icon">
            <CrownIcon :size="24" />
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ vipStats.activeVipCount || 0 }}</div>
            <div class="stat-label">当前VIP用户</div>
          </div>
        </div>
        <div class="stat-card">
          <div class="stat-icon">
            <UsersIcon :size="24" />
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ vipStats.totalVipCount || 0 }}</div>
            <div class="stat-label">总VIP用户</div>
          </div>
        </div>
        <div class="stat-card">
          <div class="stat-icon">
            <TrendingUpIcon :size="24" />
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ vipStats.newVipCount || 0 }}</div>
            <div class="stat-label">本月新增</div>
          </div>
        </div>
        <div class="stat-card">
          <div class="stat-icon">
            <TrendingDownIcon :size="24" />
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ vipStats.expiredVipCount || 0 }}</div>
            <div class="stat-label">本月过期</div>
          </div>
        </div>
      </div>

      <!-- VIP用户筛选 -->
      <div class="filter-section card-responsive">
        <el-form :model="vipFilters" class="filter-form form-responsive">
          <div class="grid grid-cols-4 gap-4">
            <el-form-item label="VIP状态">
              <el-select v-model="vipFilters.status" placeholder="VIP状态" class="mobile-w-full">
                <el-option label="全部" value="all" />
                <el-option label="有效VIP" value="active" />
                <el-option label="已过期" value="expired" />
              </el-select>
            </el-form-item>

            <el-form-item label="时间范围">
              <el-date-picker
                v-model="vipFilters.dateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                class="mobile-w-full"
              />
            </el-form-item>

            <el-form-item label="排序方式">
              <el-select v-model="vipFilters.sortBy" class="mobile-w-full">
                <el-option label="VIP到期时间" value="vipExpireAt" />
                <el-option label="VIP获得时间" value="vipGrantTime" />
                <el-option label="用户名" value="nickname" />
              </el-select>
            </el-form-item>

            <el-form-item label="排序方向">
              <el-select v-model="vipFilters.sortOrder" class="mobile-w-full">
                <el-option label="降序" value="desc" />
                <el-option label="升序" value="asc" />
              </el-select>
            </el-form-item>
          </div>

          <div class="search-row flex gap-4 items-end">
            <el-form-item label="搜索" class="flex-1">
              <el-input
                v-model="vipFilters.keyword"
                placeholder="搜索用户名或OpenID"
                @keyup.enter="handleVipSearch"
                class="mobile-w-full"
              >
                <template #prefix>
                  <SearchIcon :size="16" />
                </template>
              </el-input>
            </el-form-item>

            <div class="search-actions flex gap-2">
              <el-button type="primary" @click="handleVipSearch" class="btn-responsive">
                <template #icon>
                  <SearchIcon :size="16" />
                </template>
                搜索
              </el-button>
              <el-button @click="resetVipFilters" class="btn-responsive">
                <template #icon>
                  <RotateCcwIcon :size="16" />
                </template>
                重置
              </el-button>
              <el-button type="success" @click="showGrantVipDialog" class="btn-responsive">
                <template #icon>
                  <PlusIcon :size="16" />
                </template>
                赠送VIP
              </el-button>
            </div>
          </div>
        </el-form>
      </div>

      <!-- VIP用户列表 -->
      <div class="table-section">
        <el-table
          v-loading="vipLoading"
          :data="vipUserList"
          stripe
          style="width: 100%"
        >
          <el-table-column prop="avatar" label="头像" width="80">
            <template #default="{ row }">
              <el-avatar :size="40" :src="row.avatar" :alt="row.nickname">
                <UserIcon :size="20" />
              </el-avatar>
            </template>
          </el-table-column>
          <el-table-column prop="nickname" label="昵称" min-width="150" />
          <el-table-column prop="vip.status" label="VIP状态" width="100">
            <template #default="{ row }">
              <el-tag v-if="row.vip?.status" type="warning" size="small">
                <CrownIcon :size="12" />
                VIP
              </el-tag>
              <el-tag v-else type="info" size="small">已过期</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="vip.expireAt" label="到期时间" width="180">
            <template #default="{ row }">
              <span v-if="row.vip?.expireAt" :class="getVipExpiryClass(row.vip.expireAt)">
                {{ formatDate(row.vip.expireAt) }}
              </span>
              <span v-else class="text-gray-400">-</span>
            </template>
          </el-table-column>
          <el-table-column prop="vip.grantTime" label="获得时间" width="180">
            <template #default="{ row }">
              <span v-if="row.vip?.grantTime">
                {{ formatDate(row.vip.grantTime) }}
              </span>
              <span v-else class="text-gray-400">-</span>
            </template>
          </el-table-column>
          <el-table-column label="剩余天数" width="100">
            <template #default="{ row }">
              <span v-if="row.vip?.status && row.vip?.expireAt" :class="getVipExpiryClass(row.vip.expireAt)">
                {{ getVipRemainingDays(row.vip.expireAt) }}天
              </span>
              <span v-else class="text-gray-400">-</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200" fixed="right">
            <template #default="{ row }">
              <el-button
                type="primary"
                size="small"
                @click="viewVipRecords(row)"
              >
                VIP记录
              </el-button>
              <el-button
                v-if="row.vip?.status"
                type="warning"
                size="small"
                @click="renewVip(row)"
              >
                续期
              </el-button>
              <el-button
                v-if="row.vip?.status"
                type="danger"
                size="small"
                @click="revokeVip(row)"
              >
                取消VIP
              </el-button>
              <el-button
                v-else
                type="success"
                size="small"
                @click="grantVip(row)"
              >
                赠送VIP
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- VIP用户分页 -->
        <div class="pagination-wrapper">
          <el-pagination
            v-model:current-page="vipPagination.page"
            v-model:page-size="vipPagination.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="vipPagination.total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleVipSizeChange"
            @current-change="handleVipCurrentChange"
          />
        </div>
      </div>
    </div>
  </el-tab-pane>
</el-tabs>
</div>



    <!-- 用户详情对话框 -->
    <el-dialog
      v-model="userDetailDialogVisible"
      title="用户详情"
      width="900px"
      :close-on-click-modal="false"
    >
      <div v-if="currentUser" class="user-detail">
        <div class="detail-section">
          <h4>基本信息</h4>
          <div class="detail-grid">
            <div class="detail-item">
              <label>头像:</label>
              <el-avatar :size="50" :src="currentUser.avatar" :alt="currentUser.nickname">
                <UserIcon :size="24" />
              </el-avatar>
            </div>
            <div class="detail-item">
              <label>昵称:</label>
              <span>{{ currentUser.nickname || '-' }}</span>
            </div>
            <div class="detail-item">
              <label>用户编号:</label>
              <span>{{ currentUser.no || '-' }}</span>
            </div>
            <div class="detail-item">
              <label>OpenID:</label>
              <span class="openid-text">{{ currentUser.openid || '-' }}</span>
            </div>
            <div class="detail-item">
              <label>管理员:</label>
              <el-tag :type="currentUser.isAdmin ? 'danger' : 'info'" size="small">
                {{ currentUser.isAdmin ? '是' : '否' }}
              </el-tag>
            </div>
            <div class="detail-item">
              <label>状态:</label>
              <el-tag :type="getStatusType(currentUser.status)" size="small">
                {{ getStatusText(currentUser.status) }}
              </el-tag>
            </div>
            <div class="detail-item">
              <label>VIP状态:</label>
              <el-tag v-if="currentUser.vip?.status" type="warning" size="small">
                <CrownIcon :size="12" />
                VIP
              </el-tag>
              <span v-else>普通用户</span>
            </div>
          </div>
        </div>

        <div class="detail-section">
          <h4>统计信息</h4>
          <div class="detail-grid">
            <div class="detail-item">
              <label>总积分:</label>
              <span class="points-value">{{ currentUser.points || 0 }}</span>
            </div>
            <div class="detail-item">
              <label>API调用次数:</label>
              <span>{{ currentUser.apiCallCount || 0 }}</span>
            </div>
            <div class="detail-item">
              <label>签到天数:</label>
              <span>{{ currentUser.checkInStats?.totalDays || 0 }} 天</span>
            </div>
            <div class="detail-item">
              <label>连续签到:</label>
              <span>{{ currentUser.checkInStats?.consecutiveDays || 0 }} 天</span>
            </div>
            <div class="detail-item">
              <label>测试用户:</label>
              <el-tag :type="currentUser.isTestUser ? 'warning' : 'info'" size="small">
                {{ currentUser.isTestUser ? '是' : '否' }}
              </el-tag>
            </div>
          </div>
        </div>

        <div class="detail-section">
          <h4>时间信息</h4>
          <div class="detail-grid">
            <div class="detail-item">
              <label>注册时间:</label>
              <span>{{ formatDateWithRelative(currentUser.createTime) }}</span>
            </div>
            <div class="detail-item">
              <label>最后登录:</label>
              <span>{{ formatDateWithRelative(currentUser.lastLoginTime) }}</span>
            </div>
            <div class="detail-item">
              <label>最后活跃:</label>
              <span>{{ formatDateWithRelative(currentUser.lastActiveTime) }}</span>
            </div>
            <div class="detail-item">
              <label>更新时间:</label>
              <span>{{ formatDate(currentUser.updateTime) }}</span>
            </div>
          </div>
        </div>

        <div v-if="currentUser.vip?.status" class="detail-section">
          <h4>VIP信息</h4>
          <div class="detail-grid">
            <div class="detail-item">
              <label>VIP状态:</label>
              <el-tag type="warning" size="small">有效</el-tag>
            </div>
            <div class="detail-item">
              <label>到期时间:</label>
              <span>{{ formatDate(currentUser.vip.expiredAt) }}</span>
            </div>
            <div class="detail-item">
              <label>剩余天数:</label>
              <span>{{ getVipRemainingDays(currentUser.vip.expireAt) }} 天</span>
            </div>
          </div>
        </div>

        <div v-if="currentUser.invitation" class="detail-section">
          <h4>邀请信息</h4>
          <div class="detail-grid">
            <div class="detail-item">
              <label>邀请码:</label>
              <span>{{ currentUser.invitation.code || '-' }}</span>
            </div>
            <div class="detail-item">
              <label>邀请人数:</label>
              <span>{{ currentUser.invitation.invitedCount || 0 }}</span>
            </div>
          </div>
        </div>

        <!-- 历史记录标签页 -->
        <div class="detail-section">
          <h4>历史记录</h4>
          <el-tabs v-model="activeHistoryTab" @tab-change="handleHistoryTabChange">
            <!-- 签到记录 -->
            <el-tab-pane label="签到记录" name="checkin">
              <div class="history-content">
                <div class="history-filters">
                  <el-date-picker
                    v-model="checkinDateRange"
                    type="monthrange"
                    range-separator="至"
                    start-placeholder="开始月份"
                    end-placeholder="结束月份"
                    @change="loadCheckinHistory"
                    size="small"
                  />
                  <el-button size="small" @click="loadCheckinHistory">刷新</el-button>
                </div>

                <!-- 签到统计 -->
                <div class="checkin-stats">
                  <div class="stat-card">
                    <div class="stat-title">本月签到</div>
                    <div class="stat-number">{{ checkinStats.thisMonth || 0 }}天</div>
                  </div>
                  <div class="stat-card">
                    <div class="stat-title">连续签到</div>
                    <div class="stat-number">{{ checkinStats.consecutive || 0 }}天</div>
                  </div>
                  <div class="stat-card">
                    <div class="stat-title">总签到</div>
                    <div class="stat-number">{{ checkinStats.total || 0 }}天</div>
                  </div>
                </div>

                <!-- 签到日历 -->
                <div class="checkin-calendar">
                  <el-calendar v-model="checkinCalendarDate" size="small">
                    <template #date-cell="{ data }">
                      <div class="calendar-day" :class="getCheckinDayClass(data.day)">
                        <span class="day-number">{{ data.day.split('-').pop() }}</span>
                        <div v-if="isCheckinDay(data.day)" class="checkin-mark">
                          <CheckIcon :size="12" />
                        </div>
                      </div>
                    </template>
                  </el-calendar>
                </div>
              </div>
            </el-tab-pane>

            <!-- 积分记录 -->
            <el-tab-pane label="积分记录" name="points">
              <div class="history-content">
                <div class="history-filters">
                  <el-select v-model="pointsFilter.type" placeholder="积分类型" size="small" style="width: 120px">
                    <el-option label="全部" value="all" />
                    <el-option label="获得" value="earn" />
                    <el-option label="消费" value="spend" />
                  </el-select>
                  <el-date-picker
                    v-model="pointsDateRange"
                    type="daterange"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    @change="loadPointsHistory"
                    size="small"
                  />
                  <el-button size="small" @click="loadPointsHistory">刷新</el-button>
                </div>

                <el-table :data="pointsHistory" v-loading="pointsLoading" stripe size="small" max-height="300">
                  <el-table-column prop="amount" label="积分变化" width="100">
                    <template #default="{ row }">
                      <span :class="row.amount > 0 ? 'text-green-600' : 'text-red-600'">
                        {{ row.amount > 0 ? '+' : '' }}{{ row.amount }}
                      </span>
                    </template>
                  </el-table-column>
                  <el-table-column prop="type" label="类型" width="80">
                    <template #default="{ row }">
                      <el-tag :type="getPointsTypeTag(row.type)" size="small">
                        {{ getPointsTypeText(row.type) }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column prop="description" label="描述" min-width="150" />
                  <el-table-column prop="timestamp" label="时间" width="140">
                    <template #default="{ row }">
                      {{ formatDate(row.timestamp) }}
                    </template>
                  </el-table-column>
                </el-table>

                <div class="pagination-wrapper">
                  <el-pagination
                    v-model:current-page="pointsPagination.page"
                    v-model:page-size="pointsPagination.pageSize"
                    :page-sizes="[5, 10, 20]"
                    :total="pointsPagination.total"
                    layout="total, sizes, prev, pager, next"
                    @size-change="loadPointsHistory"
                    @current-change="loadPointsHistory"
                    size="small"
                  />
                </div>
              </div>
            </el-tab-pane>

            <!-- VIP记录 -->
            <el-tab-pane label="VIP记录" name="vip">
              <div class="history-content">
                <div class="history-filters">
                  <el-select v-model="vipFilter.type" placeholder="操作类型" size="small" style="width: 120px">
                    <el-option label="全部" value="all" />
                    <el-option label="赠送" value="grant" />
                    <el-option label="续期" value="renew" />
                    <el-option label="取消" value="revoke" />
                  </el-select>
                  <el-button size="small" @click="loadVipHistory">刷新</el-button>
                </div>

                <el-table :data="vipHistory" v-loading="vipHistoryLoading" stripe size="small" max-height="300">
                  <el-table-column prop="type" label="操作类型" width="80">
                    <template #default="{ row }">
                      <el-tag v-if="row.type === 'grant'" type="success" size="small">赠送</el-tag>
                      <el-tag v-else-if="row.type === 'renew'" type="primary" size="small">续期</el-tag>
                      <el-tag v-else-if="row.type === 'revoke'" type="danger" size="small">取消</el-tag>
                      <el-tag v-else type="info" size="small">{{ row.type }}</el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column prop="duration" label="时长" width="80">
                    <template #default="{ row }">
                      <span v-if="row.duration">{{ row.duration }}天</span>
                      <span v-else class="text-gray-400">-</span>
                    </template>
                  </el-table-column>
                  <el-table-column prop="reason" label="原因" min-width="120" />
                  <el-table-column prop="createTime" label="操作时间" width="140">
                    <template #default="{ row }">
                      {{ formatDate(row.createTime) }}
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>

      <template #footer>
        <el-button @click="userDetailDialogVisible = false">关闭</el-button>
        <el-button type="primary" @click="editUserFromDetail">编辑用户</el-button>
      </template>
    </el-dialog>

    <!-- 用户编辑对话框 -->
    <el-dialog
      v-model="userEditDialogVisible"
      title="编辑用户"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form
        v-if="editingUser"
        :model="editingUser"
        :rules="editUserRules"
        ref="editUserFormRef"
        label-width="120px"
      >
        <div class="user-edit-header">
          <el-avatar :size="60" :src="editingUser.avatar" :alt="editingUser.nickname">
            <UserIcon :size="30" />
          </el-avatar>
          <div class="user-edit-info">
            <h3>{{ editingUser.nickname }}</h3>
            <p class="text-gray-500">{{ editingUser.openid }}</p>
          </div>
        </div>

        <el-form-item label="用户昵称" prop="nickname">
          <el-input v-model="editingUser.nickname" placeholder="请输入用户昵称" />
        </el-form-item>

        <el-form-item label="用户状态" prop="status">
          <el-select v-model="editingUser.status" placeholder="选择用户状态" style="width: 100%">
            <el-option label="正常" value="active" />
            <el-option label="禁用" value="inactive" />
            <el-option label="封禁" value="banned" />
          </el-select>
        </el-form-item>

        <el-form-item label="VIP状态">
          <div class="vip-status-section">
            <el-switch
              v-model="editingUser.vip.status"
              active-text="VIP用户"
              inactive-text="普通用户"
            />
            <div v-if="editingUser.vip.status" class="vip-details">
              <el-form-item label="VIP到期时间" style="margin-top: 16px;">
                <el-date-picker
                  v-model="editingUser.vip.expireAt"
                  type="datetime"
                  placeholder="选择到期时间"
                  style="width: 100%"
                />
              </el-form-item>
            </div>
          </div>
        </el-form-item>

        <el-form-item label="积分余额" prop="pointsBalance">
          <el-input-number
            v-model="editingUser.pointsBalance"
            :min="0"
            :max="999999"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="备注">
          <el-input
            v-model="editingUser.remark"
            type="textarea"
            :rows="3"
            placeholder="管理员备注"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="userEditDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveUserEdit" :loading="editLoading">保存</el-button>
      </template>
    </el-dialog>

    <!-- VIP记录对话框 -->
    <el-dialog
      v-model="vipRecordDialogVisible"
      title="VIP记录"
      width="800px"
      :close-on-click-modal="false"
    >
      <div v-if="currentVipUser" class="vip-record-content">
        <div class="user-info-header">
          <el-avatar :size="50" :src="currentVipUser.avatar" :alt="currentVipUser.nickname">
            <UserIcon :size="24" />
          </el-avatar>
          <div class="user-info-text">
            <h3>{{ currentVipUser.nickname }}</h3>
            <p class="text-gray-500">{{ currentVipUser.openid }}</p>
          </div>
        </div>

        <el-table :data="vipRecords" stripe style="width: 100%">
          <el-table-column prop="type" label="操作类型" width="100">
            <template #default="{ row }">
              <el-tag v-if="row.type === 'grant'" type="success" size="small">赠送</el-tag>
              <el-tag v-else-if="row.type === 'renew'" type="primary" size="small">续期</el-tag>
              <el-tag v-else-if="row.type === 'revoke'" type="danger" size="small">取消</el-tag>
              <el-tag v-else type="info" size="small">{{ row.type }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="duration" label="时长(天)" width="100">
            <template #default="{ row }">
              <span v-if="row.duration">{{ row.duration }}天</span>
              <span v-else class="text-gray-400">-</span>
            </template>
          </el-table-column>
          <el-table-column prop="reason" label="原因" min-width="150" />
          <el-table-column prop="expireAt" label="到期时间" width="180">
            <template #default="{ row }">
              <span v-if="row.expireAt">{{ formatDate(row.expireAt) }}</span>
              <span v-else class="text-gray-400">-</span>
            </template>
          </el-table-column>
          <el-table-column prop="createTime" label="操作时间" width="180">
            <template #default="{ row }">
              {{ formatDate(row.createTime) }}
            </template>
          </el-table-column>
        </el-table>
      </div>

      <template #footer>
        <el-button @click="vipRecordDialogVisible = false">关闭</el-button>
      </template>
    </el-dialog>

    <!-- 赠送VIP对话框 -->
    <el-dialog
      v-model="grantVipDialogVisible"
      title="赠送VIP"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form :model="grantVipForm" label-width="100px">
        <el-form-item label="用户ID" required>
          <el-input
            v-model="grantVipForm.userId"
            placeholder="请输入用户OpenID"
            :disabled="!!grantVipForm.userId"
          />
        </el-form-item>
        <el-form-item label="时长(天)" required>
          <el-input-number
            v-model="grantVipForm.duration"
            :min="1"
            :max="3650"
            placeholder="VIP时长"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="原因">
          <el-input
            v-model="grantVipForm.reason"
            type="textarea"
            :rows="3"
            placeholder="赠送原因"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="grantVipDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmGrantVip">确认赠送</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Users as UsersIcon,
  UserCheck as UserCheckIcon,
  Crown as CrownIcon,
  UserPlus as UserPlusIcon,
  Search as SearchIcon,
  RotateCcw as RotateCcwIcon,
  RefreshCw as RefreshCwIcon,
  User as UserIcon,
  Eye as EyeIcon,
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  Plus as PlusIcon,
  Download as DownloadIcon,
  Check as CheckIcon
} from 'lucide-vue-next'
import { callCloudFunction } from '@/api/wechat-api.js'
import dayjs from 'dayjs'
import relativeTime from 'dayjs/plugin/relativeTime'
import 'dayjs/locale/zh-cn'

// 配置dayjs
dayjs.extend(relativeTime)
dayjs.locale('zh-cn')

// 响应式数据
const loading = ref(false)
const userList = ref([])
const selectedUsers = ref([])
const stats = ref({})
const userDetailDialogVisible = ref(false)
const currentUser = ref(null)

// 标签页相关
const activeTab = ref('users')

// 用户管理相关
const filters = reactive({
  vipStatus: 'all',
  keyword: '',
  sortBy: 'createTime',
  sortOrder: 'desc'
})

const pagination = reactive({
  page: 1,
  pageSize: 20,
  total: 0
})

// VIP管理相关
const vipLoading = ref(false)
const vipUserList = ref([])
const vipStats = ref({})
const vipRecordDialogVisible = ref(false)
const grantVipDialogVisible = ref(false)
const currentVipUser = ref(null)
const vipRecords = ref([])

const vipFilters = reactive({
  status: 'all',
  keyword: '',
  sortBy: 'vipExpireAt',
  sortOrder: 'desc',
  dateRange: null
})

const vipPagination = reactive({
  page: 1,
  pageSize: 20,
  total: 0
})

const grantVipForm = reactive({
  userId: '',
  duration: 30,
  reason: '管理员赠送'
})

// 用户编辑相关
const userEditDialogVisible = ref(false)
const editingUser = ref(null)
const editLoading = ref(false)
const editUserFormRef = ref(null)

const editUserRules = {
  nickname: [
    { required: true, message: '请输入用户昵称', trigger: 'blur' },
    { min: 1, max: 20, message: '昵称长度在 1 到 20 个字符', trigger: 'blur' }
  ],
  status: [
    { required: true, message: '请选择用户状态', trigger: 'change' }
  ],
  pointsBalance: [
    { type: 'number', min: 0, message: '积分余额不能为负数', trigger: 'blur' }
  ]
}

// 历史记录相关
const activeHistoryTab = ref('checkin')
const checkinDateRange = ref(null)
const checkinCalendarDate = ref(new Date())
const checkinHistory = ref([])
const checkinStats = ref({})

const pointsHistory = ref([])
const pointsLoading = ref(false)
const pointsDateRange = ref(null)
const pointsFilter = reactive({
  type: 'all'
})
const pointsPagination = reactive({
  page: 1,
  pageSize: 10,
  total: 0
})

const vipHistory = ref([])
const vipHistoryLoading = ref(false)
const vipFilter = reactive({
  type: 'all'
})

// 计算属性
const hasFilters = computed(() => {
  return filters.status !== 'all' || 
         filters.vipStatus !== 'all' || 
         filters.keyword.trim() !== ''
})

// 方法
async function loadUserList() {
  try {
    loading.value = true
    
    const result = await callCloudFunction('getUserList', {
      ...filters,
      page: pagination.page,
      pageSize: pagination.pageSize
    })

    if (result.success) {
      userList.value = result.data || []
      pagination.total = result.pagination?.total || 0
    } else {
      ElMessage.error(result.message || '获取用户列表失败')
    }
  } catch (error) {
    console.error('获取用户列表失败:', error)
    ElMessage.error('获取用户列表失败')
  } finally {
    loading.value = false
  }
}

async function loadStats() {
  try {
    const result = await callCloudFunction('getUserStats', { period: '30d' })
    if (result.success) {
      stats.value = result.data || {}
    }
  } catch (error) {
    console.error('获取用户统计失败:', error)
  }
}

function handleSearch() {
  pagination.page = 1
  loadUserList()
}

function resetFilters() {
  filters.vipStatus = 'all'
  filters.keyword = ''
  filters.sortBy = 'createTime'
  filters.sortOrder = 'desc'
  pagination.page = 1
  loadUserList()
}



function handleSizeChange(size) {
  pagination.pageSize = size
  pagination.page = 1
  loadUserList()
}

function handleCurrentChange(page) {
  pagination.page = page
  loadUserList()
}

function handleSelectionChange(selection) {
  selectedUsers.value = selection
}

async function refreshData() {
  await Promise.all([loadUserList(), loadStats()])
  ElMessage.success('数据已刷新')
}

function viewUserDetail(user) {
  currentUser.value = user
  userDetailDialogVisible.value = true

  // 重置历史记录数据
  activeHistoryTab.value = 'checkin'
  checkinHistory.value = []
  checkinStats.value = {}
  pointsHistory.value = []
  pointsPagination.page = 1
  pointsPagination.total = 0
  vipHistory.value = []

  // 加载默认的签到历史
  loadCheckinHistory()
}

async function toggleUserStatus(user) {
  try {
    const action = user.status === 'active' ? '禁用' : '启用'
    await ElMessageBox.confirm(
      `确定要${action}用户 "${user.nickname}" 吗？`,
      `${action}用户`,
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const newStatus = user.status === 'active' ? 'inactive' : 'active'
    const result = await callCloudFunction('updateUserStatus', {
      userId: user._id,
      status: newStatus,
      reason: `管理员${action}操作`
    })

    if (result.success) {
      ElMessage.success(`${action}成功`)
      loadUserList()
    } else {
      ElMessage.error(result.message || `${action}失败`)
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('更新用户状态失败:', error)
      ElMessage.error('操作失败')
    }
  }
}

async function batchUpdateStatus(status) {
  try {
    const statusText = {
      active: '启用',
      inactive: '禁用',
      banned: '封禁'
    }[status]

    await ElMessageBox.confirm(
      `确定要${statusText} ${selectedUsers.value.length} 个用户吗？`,
      `批量${statusText}`,
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const userIds = selectedUsers.value.map(user => user._id)
    const result = await callCloudFunction('batchUpdateUserStatus', {
      userIds,
      status,
      reason: `管理员批量${statusText}操作`
    })

    if (result.success) {
      ElMessage.success(`批量${statusText}成功`)
      selectedUsers.value = []
      loadUserList()
    } else {
      ElMessage.error(result.message || `批量${statusText}失败`)
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量操作失败:', error)
      ElMessage.error('批量操作失败')
    }
  }
}

function getStatusType(status) {
  const types = {
    active: 'success',
    inactive: 'info',
    banned: 'danger'
  }
  return types[status] || 'info'
}

function getStatusText(status) {
  const texts = {
    active: '活跃',
    inactive: '非活跃',
    banned: '已禁用'
  }
  return texts[status] || status
}

function formatDate(dateString) {
  if (!dateString) return '-'
  return new Date(dateString).toLocaleString('zh-CN')
}

function formatDateWithRelative(dateString) {
  if (!dateString) return '-'

  const date = dayjs(dateString)
  const now = dayjs()
  const formatted = date.format('YYYY-MM-DD HH:mm:ss')
  const relative = date.fromNow()

  return `${formatted} (${relative})`
}



function getVipExpiryClass(expireAt) {
  if (!expireAt) return 'text-gray-400'

  const now = new Date()
  const expire = new Date(expireAt)
  const diffDays = Math.ceil((expire - now) / (1000 * 60 * 60 * 24))

  if (diffDays < 0) return 'vip-expired'
  if (diffDays <= 3) return 'vip-expiring-soon'
  if (diffDays <= 7) return 'vip-expiring'
  return 'vip-normal'
}

function getVipRemainingDays(expireAt) {
  if (!expireAt) return 0

  const now = new Date()
  const expire = new Date(expireAt)
  const diffDays = Math.ceil((expire - now) / (1000 * 60 * 60 * 24))

  return Math.max(0, diffDays)
}

function editUserFromDetail() {
  if (currentUser.value) {
    editUser(currentUser.value)
  }
  userDetailDialogVisible.value = false
}

function editUser(user) {
  // 深拷贝用户数据，避免直接修改原数据
  editingUser.value = {
    ...user,
    vip: {
      status: user.vip?.status || false,
      expireAt: user.vip?.expireAt ? new Date(user.vip.expireAt) : null
    },
    pointsBalance: user.pointsBalance || 0,
    remark: user.remark || ''
  }
  userEditDialogVisible.value = true
}

async function saveUserEdit() {
  try {
    // 表单验证
    if (!editUserFormRef.value) return
    const valid = await editUserFormRef.value.validate()
    if (!valid) return

    editLoading.value = true

    // 准备更新数据
    const updateData = {
      userId: editingUser.value.openid,
      nickname: editingUser.value.nickname,
      status: editingUser.value.status,
      pointsBalance: editingUser.value.pointsBalance,
      remark: editingUser.value.remark
    }

    // 如果VIP状态有变化，添加VIP相关数据
    if (editingUser.value.vip.status) {
      updateData.vip = {
        status: true,
        expireAt: editingUser.value.vip.expireAt
      }
    } else {
      updateData.vip = {
        status: false
      }
    }

    const result = await callCloudFunction('updateUserAdmin', updateData)

    if (result.success) {
      ElMessage.success('用户信息更新成功')
      userEditDialogVisible.value = false

      // 刷新用户列表
      if (activeTab.value === 'users') {
        loadUserList()
      } else {
        loadVipUserList()
      }
      loadStats()
      loadVipStats()
    }
  } catch (error) {
    console.error('更新用户信息失败:', error)
    ElMessage.error('更新用户信息失败')
  } finally {
    editLoading.value = false
  }
}

// 导出用户数据
async function exportUserData() {
  try {
    ElMessage.info('正在导出用户数据，请稍候...')

    const params = {
      ...filters,
      format: 'excel',
      filename: `用户数据_${new Date().toISOString().split('T')[0]}`
    }

    const result = await callCloudFunction('exportUsersAdmin', params)

    if (result.success) {
      // 创建下载链接
      const blob = new Blob([result.data], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      })
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `${params.filename}.xlsx`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)

      ElMessage.success('用户数据导出成功')
    }
  } catch (error) {
    console.error('导出用户数据失败:', error)
    ElMessage.error('导出用户数据失败')
  }
}

// 历史记录相关方法
function handleHistoryTabChange(tabName) {
  activeHistoryTab.value = tabName
  if (!currentUser.value) return

  switch (tabName) {
    case 'checkin':
      loadCheckinHistory()
      break
    case 'points':
      loadPointsHistory()
      break
    case 'vip':
      loadVipHistory()
      break
  }
}

async function loadCheckinHistory() {
  if (!currentUser.value) return

  try {
    const params = {
      userId: currentUser.value.openid
    }

    console.log('[loadCheckinHistory] 当前用户:', currentUser.value)
    console.log('[loadCheckinHistory] 传递参数:', params)

    // 如果有日期范围，添加到参数中
    if (checkinDateRange.value && checkinDateRange.value.length === 2) {
      params.startDate = checkinDateRange.value[0]
      params.endDate = checkinDateRange.value[1]
    }

    const result = await callCloudFunction('getUserCheckInHistoryWithStatsAdmin', params)

    if (result.success) {
      checkinHistory.value = result.data.records || []
      checkinStats.value = result.data.stats || {}
    }
  } catch (error) {
    console.error('加载签到历史失败:', error)
    ElMessage.error('加载签到历史失败')
  }
}

async function loadPointsHistory() {
  if (!currentUser.value) return

  try {
    pointsLoading.value = true

    const params = {
      userId: currentUser.value.openid,
      type: pointsFilter.type,
      page: pointsPagination.page,
      pageSize: pointsPagination.pageSize
    }

    console.log('[loadPointsHistory] 当前用户:', currentUser.value)
    console.log('[loadPointsHistory] 传递参数:', params)

    // 如果有日期范围，添加到参数中
    if (pointsDateRange.value && pointsDateRange.value.length === 2) {
      params.startDate = pointsDateRange.value[0]
      params.endDate = pointsDateRange.value[1]
    }

    const result = await callCloudFunction('getUserPointsHistoryAdmin', params)

    console.log('[loadPointsHistory] API返回结果:', result)

    if (result.success) {
      pointsHistory.value = result.data || []
      pointsPagination.total = result.pagination?.total || 0
      console.log('[loadPointsHistory] 设置数据:', {
        pointsHistory: pointsHistory.value,
        total: pointsPagination.total
      })
    }
  } catch (error) {
    console.error('加载积分历史失败:', error)
    ElMessage.error('加载积分历史失败')
  } finally {
    pointsLoading.value = false
  }
}

async function loadVipHistory() {
  if (!currentUser.value) return

  try {
    vipHistoryLoading.value = true

    const params = {
      userId: currentUser.value.openid,
      type: vipFilter.type
    }

    const result = await callCloudFunction('getVipRecordsAdmin', params)

    console.log('[loadVipHistory] API返回结果:', result)

    if (result.success) {
      vipHistory.value = result.data || []
      console.log('[loadVipHistory] 设置数据:', vipHistory.value)
    }
  } catch (error) {
    console.error('加载VIP历史失败:', error)
    ElMessage.error('加载VIP历史失败')
  } finally {
    vipHistoryLoading.value = false
  }
}

// 签到日历相关方法
function isCheckinDay(date) {
  return checkinHistory.value.some(record => {
    const recordDate = new Date(record.date).toISOString().split('T')[0]
    return recordDate === date
  })
}

function getCheckinDayClass(date) {
  if (isCheckinDay(date)) {
    return 'checkin-day'
  }
  return ''
}

// 积分类型相关方法
function getPointsTypeTag(type) {
  const typeMap = {
    checkin: 'success',
    purchase: 'danger',
    admin_adjust: 'warning',
    reward: 'primary'
  }
  return typeMap[type] || 'info'
}

function getPointsTypeText(type) {
  const typeMap = {
    checkin: '签到',
    purchase: '购买',
    admin_adjust: '调整',
    reward: '奖励'
  }
  return typeMap[type] || type
}

// VIP管理相关方法
async function loadVipStats() {
  try {
    const result = await callCloudFunction('getVipStatsAdmin', {
      period: '30d'
    })

    if (result.success) {
      vipStats.value = result.data
    }
  } catch (error) {
    console.error('加载VIP统计失败:', error)
  }
}

async function loadVipUserList() {
  try {
    vipLoading.value = true

    const params = {
      ...vipFilters,
      page: vipPagination.page,
      pageSize: vipPagination.pageSize
    }

    // 处理日期范围
    if (vipFilters.dateRange && vipFilters.dateRange.length === 2) {
      params.startDate = vipFilters.dateRange[0]
      params.endDate = vipFilters.dateRange[1]
    }

    const result = await callCloudFunction('getVipUsersAdmin', params)

    if (result.success) {
      vipUserList.value = result.data.list
      vipPagination.total = result.data.total
    }
  } catch (error) {
    console.error('加载VIP用户列表失败:', error)
    ElMessage.error('加载VIP用户列表失败')
  } finally {
    vipLoading.value = false
  }
}

function handleTabChange(tabName) {
  activeTab.value = tabName
  if (tabName === 'vip') {
    loadVipStats()
    loadVipUserList()
  }
}

function handleVipSearch() {
  vipPagination.page = 1
  loadVipUserList()
}

function resetVipFilters() {
  Object.assign(vipFilters, {
    status: 'all',
    keyword: '',
    sortBy: 'vipExpireAt',
    sortOrder: 'desc',
    dateRange: null
  })
  handleVipSearch()
}

function handleVipSizeChange(size) {
  vipPagination.pageSize = size
  vipPagination.page = 1
  loadVipUserList()
}

function handleVipCurrentChange(page) {
  vipPagination.page = page
  loadVipUserList()
}





async function viewVipRecords(user) {
  try {
    currentVipUser.value = user

    const result = await callCloudFunction('getVipRecordsAdmin', {
      userId: user.openid,
      page: 1,
      pageSize: 50
    })

    if (result.success) {
      vipRecords.value = result.data.list
      vipRecordDialogVisible.value = true
    }
  } catch (error) {
    console.error('获取VIP记录失败:', error)
    ElMessage.error('获取VIP记录失败')
  }
}

function showGrantVipDialog() {
  Object.assign(grantVipForm, {
    userId: '',
    duration: 30,
    reason: '管理员赠送'
  })
  grantVipDialogVisible.value = true
}

function grantVip(user) {
  Object.assign(grantVipForm, {
    userId: user.openid,
    duration: 30,
    reason: '管理员赠送'
  })
  grantVipDialogVisible.value = true
}

function renewVip(user) {
  Object.assign(grantVipForm, {
    userId: user.openid,
    duration: 30,
    reason: 'VIP续期'
  })
  grantVipDialogVisible.value = true
}

async function confirmGrantVip() {
  try {
    if (!grantVipForm.userId || !grantVipForm.duration) {
      ElMessage.error('请填写完整信息')
      return
    }

    const result = await callCloudFunction('grantVipAdmin', {
      userId: grantVipForm.userId,
      duration: parseInt(grantVipForm.duration),
      reason: grantVipForm.reason
    })

    if (result.success) {
      ElMessage.success('VIP赠送成功')
      grantVipDialogVisible.value = false
      loadVipUserList()
      loadVipStats()
    }
  } catch (error) {
    console.error('赠送VIP失败:', error)
    ElMessage.error('赠送VIP失败')
  }
}

async function revokeVip(user) {
  try {
    await ElMessageBox.confirm(
      `确定要取消 ${user.nickname} 的VIP吗？`,
      '确认取消VIP',
      {
        type: 'warning'
      }
    )

    const result = await callCloudFunction('revokeVipAdmin', {
      userId: user.openid,
      reason: '管理员取消'
    })

    if (result.success) {
      ElMessage.success('VIP取消成功')
      loadVipUserList()
      loadVipStats()
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('取消VIP失败:', error)
      ElMessage.error('取消VIP失败')
    }
  }
}

// 生命周期
onMounted(() => {
  loadUserList()
  loadStats()
})
</script>

<style scoped>
.users-container {
  padding: 24px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 8px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.page-description {
  color: #6b7280;
  margin: 0;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.stat-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.stat-icon.total { background: #3b82f6; }
.stat-icon.active { background: #10b981; }
.stat-icon.vip { background: #f59e0b; }
.stat-icon.new { background: #8b5cf6; }

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
}

.stat-label {
  font-size: 14px;
  color: #6b7280;
}

.filter-section {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.table-section {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.batch-actions {
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  background: white;
  border-radius: 8px;
  padding: 16px 24px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  display: flex;
  align-items: center;
  gap: 16px;
  z-index: 1000;
}

.batch-info {
  font-weight: 500;
  color: #374151;
}

.batch-buttons {
  display: flex;
  gap: 8px;
}

.text-gray-400 {
  color: #9ca3af;
}

.user-detail {
  max-height: 500px;
  overflow-y: auto;
}

.detail-section {
  margin-bottom: 24px;
}

.detail-section h4 {
  margin: 0 0 12px 0;
  color: #1f2937;
  font-size: 16px;
  font-weight: 600;
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 8px;
}

.detail-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.detail-item label {
  font-weight: 500;
  color: #374151;
  min-width: 80px;
}

.points-value {
  font-weight: 600;
  color: #f59e0b;
}

.openid-text {
  font-family: monospace;
  font-size: 12px;
  color: #6b7280;
  word-break: break-all;
}

.vip-normal {
  color: #10b981;
}

.vip-expiring {
  color: #f59e0b;
  font-weight: 600;
}

.vip-expiring-soon {
  color: #ef4444;
  font-weight: 600;
  animation: pulse 2s infinite;
}

.vip-expired {
  color: #ef4444;
  font-weight: 600;
  text-decoration: line-through;
}

/* VIP管理样式 */
.vip-management {
  padding: 0;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 24px;
}

.stat-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #6b7280;
}

.vip-record-content {
  max-height: 500px;
  overflow-y: auto;
}

.user-info-header {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 20px;
  padding: 16px;
  background: #f9fafb;
  border-radius: 8px;
}

.user-info-text h3 {
  margin: 0 0 4px 0;
  color: #1f2937;
  font-size: 18px;
  font-weight: 600;
}

.user-info-text p {
  margin: 0;
  font-size: 14px;
}

.tabs-section {
  margin-top: 20px;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
  }

  .stat-card {
    padding: 16px;
  }

  .stat-value {
    font-size: 20px;
  }

  .user-info-header {
    flex-direction: column;
    text-align: center;
  }
}

@media (max-width: 480px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }
}

/* 用户编辑对话框样式 */
.user-edit-header {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 24px;
  padding: 16px;
  background: #f9fafb;
  border-radius: 8px;
}

.user-edit-info h3 {
  margin: 0 0 4px 0;
  color: #1f2937;
  font-size: 18px;
  font-weight: 600;
}

.user-edit-info p {
  margin: 0;
  font-size: 14px;
}

.vip-status-section {
  width: 100%;
}

.vip-details {
  margin-top: 16px;
  padding: 16px;
  background: #fef3c7;
  border-radius: 6px;
  border-left: 4px solid #f59e0b;
}

/* 历史记录样式 */
.history-content {
  margin-top: 16px;
}

.history-filters {
  display: flex;
  gap: 12px;
  margin-bottom: 16px;
  align-items: center;
}

.checkin-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
  margin-bottom: 20px;
}

.stat-card {
  background: #f8fafc;
  border-radius: 8px;
  padding: 16px;
  text-align: center;
  border: 1px solid #e2e8f0;
}

.stat-title {
  font-size: 14px;
  color: #64748b;
  margin-bottom: 8px;
}

.stat-number {
  font-size: 24px;
  font-weight: 700;
  color: #1e293b;
}

.checkin-calendar {
  margin-top: 16px;
}

.calendar-day {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
}

.calendar-day.checkin-day {
  background: #dcfce7;
  color: #166534;
}

.day-number {
  font-size: 14px;
}

.checkin-mark {
  position: absolute;
  top: 2px;
  right: 2px;
  color: #16a34a;
}

.pagination-wrapper {
  margin-top: 16px;
  display: flex;
  justify-content: center;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .checkin-stats {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .history-filters {
    flex-direction: column;
    align-items: stretch;
  }

  .stat-card {
    padding: 12px;
  }

  .stat-number {
    font-size: 20px;
  }
}

/* 动画效果 */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}
</style>
