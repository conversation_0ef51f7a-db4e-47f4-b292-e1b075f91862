<template>
  <div class="vip-container">
    <div class="page-header">
      <div class="header-left">
        <h1 class="page-title">
          <CrownIcon :size="24" />
          VIP管理
        </h1>
        <p class="page-description">管理VIP用户和记录</p>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="refreshData">
          <template #icon>
            <RefreshCwIcon :size="16" />
          </template>
          刷新
        </el-button>
        <el-button type="success" @click="showGrantVipDialog">
          <template #icon>
            <PlusIcon :size="16" />
          </template>
          赠送VIP
        </el-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-grid">
      <div class="stat-card">
        <div class="stat-icon total">
          <CrownIcon :size="24" />
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stats.totalVipUsers || 0 }}</div>
          <div class="stat-label">VIP用户总数</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon active">
          <CheckCircleIcon :size="24" />
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stats.activeVipUsers || 0 }}</div>
          <div class="stat-label">当前有效VIP</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon revenue">
          <DollarSignIcon :size="24" />
        </div>
        <div class="stat-content">
          <div class="stat-value">¥{{ stats.totalRevenue || 0 }}</div>
          <div class="stat-label">总收入</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon expiring">
          <ClockIcon :size="24" />
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stats.expiringSoon || 0 }}</div>
          <div class="stat-label">即将过期</div>
        </div>
      </div>
    </div>

    <!-- 功能标签页 -->
    <el-tabs v-model="activeTab" class="vip-tabs">
      <el-tab-pane label="VIP用户" name="users">
        <!-- 筛选和搜索 -->
        <div class="filter-section">
          <el-form :model="filters" inline class="filter-form">
            <el-form-item label="状态">
              <el-select v-model="filters.status" placeholder="选择状态" style="width: 120px">
                <el-option label="全部" value="all" />
                <el-option label="有效" value="active" />
                <el-option label="已过期" value="expired" />
                <el-option label="即将过期" value="expiring" />
              </el-select>
            </el-form-item>
            <el-form-item label="用户">
              <el-input
                v-model="filters.userId"
                placeholder="输入用户ID"
                style="width: 200px"
              >
                <template #prefix>
                  <UserIcon :size="16" />
                </template>
              </el-input>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="loadVipUsers">
                <template #icon>
                  <SearchIcon :size="16" />
                </template>
                搜索
              </el-button>
              <el-button @click="resetFilters">
                <template #icon>
                  <RotateCcwIcon :size="16" />
                </template>
                重置
              </el-button>
            </el-form-item>
          </el-form>
        </div>

        <!-- VIP用户表格 -->
        <div class="table-section">
          <el-table
            v-loading="loading"
            :data="vipUsers"
            stripe
            style="width: 100%"
          >
            <el-table-column prop="userId" label="用户ID" width="200" />
            <el-table-column prop="nickname" label="昵称" min-width="150" />
            <el-table-column prop="vipStatus" label="VIP状态" width="100">
              <template #default="{ row }">
                <el-tag :type="getVipStatusColor(row.vip?.status)" size="small">
                  {{ getVipStatusText(row.vip?.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="vipExpiredAt" label="到期时间" width="180">
              <template #default="{ row }">
                <span :class="getExpiryClass(row.vip?.expiredAt)">
                  {{ formatDate(row.vip?.expiredAt) }}
                </span>
              </template>
            </el-table-column>
            <el-table-column prop="remainingDays" label="剩余天数" width="100">
              <template #default="{ row }">
                <span :class="getRemainingDaysClass(calculateRemainingDays(row.vip?.expiredAt))">
                  {{ calculateRemainingDays(row.vip?.expiredAt) }} 天
                </span>
              </template>
            </el-table-column>
            <el-table-column prop="createTime" label="注册时间" width="180">
              <template #default="{ row }">
                {{ formatDate(row.createTime) }}
              </template>
            </el-table-column>
            <el-table-column label="操作" width="200" fixed="right">
              <template #default="{ row }">
                <el-button
                  type="primary"
                  size="small"
                  @click="viewVipDetail(row)"
                >
                  <template #icon>
                    <EyeIcon :size="14" />
                  </template>
                  查看
                </el-button>
                <el-button
                  type="warning"
                  size="small"
                  @click="extendVip(row)"
                >
                  <template #icon>
                    <ClockIcon :size="14" />
                  </template>
                  延期
                </el-button>
                <el-button
                  v-if="row.vip?.status"
                  type="danger"
                  size="small"
                  @click="cancelVip(row)"
                >
                  <template #icon>
                    <XIcon :size="14" />
                  </template>
                  取消
                </el-button>
              </template>
            </el-table-column>
          </el-table>

          <!-- 分页 -->
          <div class="pagination-wrapper">
            <el-pagination
              v-model:current-page="pagination.page"
              v-model:page-size="pagination.pageSize"
              :total="pagination.total"
              :page-sizes="[10, 20, 50, 100]"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            />
          </div>
        </div>
      </el-tab-pane>

      <el-tab-pane label="VIP记录" name="records">
        <!-- VIP记录筛选 -->
        <div class="filter-section">
          <el-form :model="recordFilters" inline class="filter-form">
            <el-form-item label="操作类型">
              <el-select v-model="recordFilters.action" placeholder="选择操作" style="width: 120px">
                <el-option label="全部" value="all" />
                <el-option label="激活" value="activate" />
                <el-option label="延期" value="extend" />
                <el-option label="过期" value="expire" />
                <el-option label="取消" value="cancel" />
              </el-select>
            </el-form-item>
            <el-form-item label="来源">
              <el-select v-model="recordFilters.source" placeholder="选择来源" style="width: 120px">
                <el-option label="全部" value="all" />
                <el-option label="购买" value="purchase" />
                <el-option label="赠送" value="gift" />
                <el-option label="手动" value="manual" />
              </el-select>
            </el-form-item>
            <el-form-item label="用户">
              <el-input
                v-model="recordFilters.userId"
                placeholder="输入用户ID"
                style="width: 200px"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="loadVipRecords">
                搜索
              </el-button>
              <el-button @click="resetRecordFilters">
                重置
              </el-button>
            </el-form-item>
          </el-form>
        </div>

        <!-- VIP记录表格 -->
        <div class="table-section">
          <el-table
            v-loading="recordsLoading"
            :data="vipRecords"
            stripe
            style="width: 100%"
          >
            <el-table-column prop="userId" label="用户ID" width="200" />
            <el-table-column prop="userNumber" label="用户编号" width="100" />
            <el-table-column prop="action" label="操作类型" width="100">
              <template #default="{ row }">
                <el-tag :type="getActionColor(row.action)" size="small">
                  {{ getActionText(row.action) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="duration" label="时长" width="100">
              <template #default="{ row }">
                {{ row.duration || 0 }} 天
              </template>
            </el-table-column>
            <el-table-column prop="source" label="来源" width="100">
              <template #default="{ row }">
                <el-tag size="small">{{ getSourceText(row.source) }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="description" label="描述" min-width="200" />
            <el-table-column prop="createTime" label="操作时间" width="180">
              <template #default="{ row }">
                {{ formatDate(row.createTime) }}
              </template>
            </el-table-column>
          </el-table>

          <!-- 分页 -->
          <div class="pagination-wrapper">
            <el-pagination
              v-model:current-page="recordPagination.page"
              v-model:page-size="recordPagination.pageSize"
              :total="recordPagination.total"
              :page-sizes="[10, 20, 50, 100]"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="handleRecordSizeChange"
              @current-change="handleRecordCurrentChange"
            />
          </div>
        </div>
      </el-tab-pane>

      <el-tab-pane label="统计分析" name="statistics">
        <div class="statistics-section">
          <!-- 统计筛选 -->
          <div class="stats-filter">
            <el-form inline>
              <el-form-item label="统计周期">
                <el-select v-model="statsPeriod" @change="loadStatistics">
                  <el-option label="最近7天" value="7d" />
                  <el-option label="最近30天" value="30d" />
                  <el-option label="最近90天" value="90d" />
                </el-select>
              </el-form-item>
            </el-form>
          </div>

          <!-- 统计图表 -->
          <div class="charts-grid">
            <div class="chart-card">
              <h4>VIP用户趋势</h4>
              <div class="chart-placeholder">
                <!-- TODO: 集成图表库显示VIP用户趋势 -->
                <p>VIP用户增长趋势图表</p>
              </div>
            </div>
            <div class="chart-card">
              <h4>收入统计</h4>
              <div class="chart-placeholder">
                <!-- TODO: 集成图表库显示收入统计 -->
                <p>VIP收入统计图表</p>
              </div>
            </div>
          </div>

          <!-- 详细统计 -->
          <div class="detailed-stats">
            <h4>详细统计</h4>
            <div class="stats-table">
              <el-table :data="detailedStats" stripe>
                <el-table-column prop="metric" label="指标" width="200" />
                <el-table-column prop="value" label="数值" width="150" />
                <el-table-column prop="description" label="说明" />
              </el-table>
            </div>
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>

    <!-- 赠送VIP对话框 -->
    <el-dialog
      v-model="grantVipDialogVisible"
      title="赠送VIP"
      width="500px"
      @close="resetGrantVipForm"
    >
      <el-form
        ref="grantVipFormRef"
        :model="grantVipForm"
        :rules="grantVipRules"
        label-width="100px"
      >
        <el-form-item label="用户ID" prop="userId">
          <el-input v-model="grantVipForm.userId" placeholder="请输入用户ID" />
        </el-form-item>
        <el-form-item label="时长" prop="duration">
          <el-input-number
            v-model="grantVipForm.duration"
            :min="1"
            :max="3650"
            style="width: 100%"
          />
          <span class="form-help">天数</span>
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input
            v-model="grantVipForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入赠送原因"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="grantVipDialogVisible = false">取消</el-button>
        <el-button
          type="primary"
          :loading="granting"
          @click="submitGrantVip"
        >
          确定赠送
        </el-button>
      </template>
    </el-dialog>

    <!-- VIP详情对话框 -->
    <el-dialog
      v-model="vipDetailDialogVisible"
      title="VIP详情"
      width="600px"
    >
      <div v-if="currentVipUser" class="vip-detail">
        <div class="detail-section">
          <h4>用户信息</h4>
          <div class="detail-grid">
            <div class="detail-item">
              <label>用户ID:</label>
              <span>{{ currentVipUser.userId }}</span>
            </div>
            <div class="detail-item">
              <label>昵称:</label>
              <span>{{ currentVipUser.nickname || '-' }}</span>
            </div>
            <div class="detail-item">
              <label>VIP状态:</label>
              <el-tag :type="getVipStatusColor(currentVipUser.vip?.status)" size="small">
                {{ getVipStatusText(currentVipUser.vip?.status) }}
              </el-tag>
            </div>
          </div>
        </div>

        <div class="detail-section">
          <h4>VIP信息</h4>
          <div class="detail-grid">
            <div class="detail-item">
              <label>到期时间:</label>
              <span>{{ formatDate(currentVipUser.vip?.expiredAt) }}</span>
            </div>
            <div class="detail-item">
              <label>剩余天数:</label>
              <span>{{ calculateRemainingDays(currentVipUser.vip?.expiredAt) }} 天</span>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Crown as CrownIcon,
  CheckCircle as CheckCircleIcon,
  DollarSign as DollarSignIcon,
  Clock as ClockIcon,
  RefreshCw as RefreshCwIcon,
  Plus as PlusIcon,
  User as UserIcon,
  Search as SearchIcon,
  RotateCcw as RotateCcwIcon,
  Eye as EyeIcon,
  X as XIcon
} from 'lucide-vue-next'
import { callCloudFunction } from '@/api/wechat-api.js'

// 响应式数据
const loading = ref(false)
const recordsLoading = ref(false)
const granting = ref(false)
const activeTab = ref('users')
const vipUsers = ref([])
const vipRecords = ref([])
const stats = ref({})
const detailedStats = ref([])
const grantVipDialogVisible = ref(false)
const vipDetailDialogVisible = ref(false)
const currentVipUser = ref(null)
const grantVipFormRef = ref()
const statsPeriod = ref('30d')

const filters = reactive({
  status: 'all',
  userId: ''
})

const recordFilters = reactive({
  action: 'all',
  source: 'all',
  userId: ''
})

const pagination = reactive({
  page: 1,
  pageSize: 20,
  total: 0
})

const recordPagination = reactive({
  page: 1,
  pageSize: 20,
  total: 0
})

const grantVipForm = reactive({
  userId: '',
  duration: 30,
  description: ''
})

const grantVipRules = {
  userId: [
    { required: true, message: '请输入用户ID', trigger: 'blur' }
  ],
  duration: [
    { required: true, message: '请输入时长', trigger: 'blur' },
    { type: 'number', min: 1, max: 3650, message: '时长范围1-3650天', trigger: 'blur' }
  ],
  description: [
    { required: true, message: '请输入赠送原因', trigger: 'blur' }
  ]
}

// 方法
async function loadVipUsers() {
  try {
    loading.value = true
    
    const params = {
      page: pagination.page,
      pageSize: pagination.pageSize
    }
    
    if (filters.status !== 'all') {
      params.status = filters.status
    }
    if (filters.userId.trim()) {
      params.userId = filters.userId.trim()
    }
    
    const result = await callCloudFunction('getVipUsersAdmin', params)

    if (result.success) {
      vipUsers.value = result.data || []
      pagination.total = result.pagination?.total || 0
    } else {
      ElMessage.error(result.message || '获取VIP用户列表失败')
    }
  } catch (error) {
    console.error('获取VIP用户列表失败:', error)
    ElMessage.error('获取VIP用户列表失败')
  } finally {
    loading.value = false
  }
}

async function loadVipRecords() {
  try {
    recordsLoading.value = true
    
    const params = {
      page: recordPagination.page,
      pageSize: recordPagination.pageSize
    }
    
    if (recordFilters.action !== 'all') {
      params.action = recordFilters.action
    }
    if (recordFilters.source !== 'all') {
      params.source = recordFilters.source
    }
    if (recordFilters.userId.trim()) {
      params.userId = recordFilters.userId.trim()
    }
    
    const result = await callCloudFunction('getVipRecordsAdmin', params)

    if (result.success) {
      vipRecords.value = result.data || []
      recordPagination.total = result.pagination?.total || 0
    } else {
      ElMessage.error(result.message || '获取VIP记录失败')
    }
  } catch (error) {
    console.error('获取VIP记录失败:', error)
    ElMessage.error('获取VIP记录失败')
  } finally {
    recordsLoading.value = false
  }
}

async function loadStats() {
  try {
    const result = await callCloudFunction('getVipStatsAdmin', { period: statsPeriod.value })
    if (result.success) {
      stats.value = result.data || {}
      
      // 构建详细统计数据
      detailedStats.value = [
        { metric: 'VIP用户总数', value: stats.value.totalVipUsers || 0, description: '历史累计VIP用户数量' },
        { metric: '当前有效VIP', value: stats.value.activeVipUsers || 0, description: '当前状态为有效的VIP用户' },
        { metric: '总收入', value: `¥${stats.value.totalRevenue || 0}`, description: 'VIP业务总收入' },
        { metric: '平均客单价', value: `¥${stats.value.averagePrice || 0}`, description: '每个VIP用户的平均消费' },
        { metric: '即将过期', value: stats.value.expiringSoon || 0, description: '7天内即将过期的VIP用户' }
      ]
    }
  } catch (error) {
    console.error('获取VIP统计失败:', error)
  }
}

async function loadStatistics() {
  await loadStats()
}

function resetFilters() {
  filters.status = 'all'
  filters.userId = ''
  pagination.page = 1
  loadVipUsers()
}

function resetRecordFilters() {
  recordFilters.action = 'all'
  recordFilters.source = 'all'
  recordFilters.userId = ''
  recordPagination.page = 1
  loadVipRecords()
}

function handleSizeChange(size) {
  pagination.pageSize = size
  pagination.page = 1
  loadVipUsers()
}

function handleCurrentChange(page) {
  pagination.page = page
  loadVipUsers()
}

function handleRecordSizeChange(size) {
  recordPagination.pageSize = size
  recordPagination.page = 1
  loadVipRecords()
}

function handleRecordCurrentChange(page) {
  recordPagination.page = page
  loadVipRecords()
}

async function refreshData() {
  await Promise.all([
    loadVipUsers(),
    loadVipRecords(),
    loadStats()
  ])
  ElMessage.success('数据已刷新')
}

function showGrantVipDialog() {
  grantVipDialogVisible.value = true
}

function resetGrantVipForm() {
  Object.assign(grantVipForm, {
    userId: '',
    duration: 30,
    description: ''
  })
  grantVipFormRef.value?.clearValidate()
}

async function submitGrantVip() {
  try {
    const valid = await grantVipFormRef.value.validate()
    if (!valid) return

    granting.value = true

    const result = await callCloudFunction('grantVipAdmin', {
      userId: grantVipForm.userId,
      duration: grantVipForm.duration,
      description: grantVipForm.description,
      source: 'gift'
    })

    if (result.success) {
      ElMessage.success('VIP赠送成功')
      grantVipDialogVisible.value = false
      refreshData()
    } else {
      ElMessage.error(result.message || 'VIP赠送失败')
    }
  } catch (error) {
    console.error('VIP赠送失败:', error)
    ElMessage.error('VIP赠送失败')
  } finally {
    granting.value = false
  }
}

function viewVipDetail(user) {
  currentVipUser.value = user
  vipDetailDialogVisible.value = true
}

async function extendVip(user) {
  try {
    const { value: duration } = await ElMessageBox.prompt(
      '请输入延期天数',
      '延期VIP',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputPattern: /^\d+$/,
        inputErrorMessage: '请输入有效的天数'
      }
    )

    const result = await callCloudFunction('extendVipAdmin', {
      userId: user.userId,
      duration: parseInt(duration),
      description: `管理员延期 ${duration} 天`
    })

    if (result.success) {
      ElMessage.success('VIP延期成功')
      refreshData()
    } else {
      ElMessage.error(result.message || 'VIP延期失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('VIP延期失败:', error)
      ElMessage.error('VIP延期失败')
    }
  }
}

async function cancelVip(user) {
  try {
    await ElMessageBox.confirm(
      `确定要取消用户 "${user.nickname || user.userId}" 的VIP吗？`,
      '取消VIP',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const result = await callCloudFunction('cancelVipAdmin', {
      userId: user.userId,
      description: '管理员取消VIP'
    })

    if (result.success) {
      ElMessage.success('VIP取消成功')
      refreshData()
    } else {
      ElMessage.error(result.message || 'VIP取消失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('VIP取消失败:', error)
      ElMessage.error('VIP取消失败')
    }
  }
}

// 辅助函数
function getVipStatusColor(status) {
  return status ? 'warning' : 'info'
}

function getVipStatusText(status) {
  return status ? 'VIP' : '普通用户'
}

function getExpiryClass(expiredAt) {
  if (!expiredAt) return ''
  const remaining = calculateRemainingDays(expiredAt)
  if (remaining <= 0) return 'expired'
  if (remaining <= 7) return 'expiring-soon'
  return ''
}

function getRemainingDaysClass(days) {
  if (days <= 0) return 'expired'
  if (days <= 7) return 'expiring-soon'
  return ''
}

function getActionColor(action) {
  const colors = {
    activate: 'success',
    extend: 'warning',
    expire: 'info',
    cancel: 'danger'
  }
  return colors[action] || 'info'
}

function getActionText(action) {
  const texts = {
    activate: '激活',
    extend: '延期',
    expire: '过期',
    cancel: '取消'
  }
  return texts[action] || action
}

function getSourceText(source) {
  const texts = {
    purchase: '购买',
    gift: '赠送',
    manual: '手动'
  }
  return texts[source] || source
}

function calculateRemainingDays(expiredAt) {
  if (!expiredAt) return 0
  const now = new Date()
  const end = new Date(expiredAt)
  const diff = end.getTime() - now.getTime()
  return Math.max(0, Math.ceil(diff / (1000 * 60 * 60 * 24)))
}

function formatDate(dateString) {
  if (!dateString) return '-'
  return new Date(dateString).toLocaleString('zh-CN')
}

// 生命周期
onMounted(() => {
  loadVipUsers()
  loadVipRecords()
  loadStats()
})
</script>

<style scoped>
.vip-container {
  padding: 24px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 8px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.page-description {
  color: #6b7280;
  margin: 0;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.stat-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.stat-icon.total { background: #f59e0b; }
.stat-icon.active { background: #10b981; }
.stat-icon.revenue { background: #3b82f6; }
.stat-icon.expiring { background: #ef4444; }

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
}

.stat-label {
  font-size: 14px;
  color: #6b7280;
}

.vip-tabs {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.filter-section {
  margin-bottom: 20px;
  padding: 16px;
  background: #f9fafb;
  border-radius: 8px;
}

.table-section {
  margin-top: 16px;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.statistics-section {
  margin-top: 16px;
}

.stats-filter {
  margin-bottom: 20px;
  padding: 16px;
  background: #f9fafb;
  border-radius: 8px;
}

.charts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 20px;
  margin-bottom: 24px;
}

.chart-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.chart-card h4 {
  margin: 0 0 16px 0;
  color: #1f2937;
}

.chart-placeholder {
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f9fafb;
  border-radius: 8px;
  color: #6b7280;
}

.detailed-stats {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.detailed-stats h4 {
  margin: 0 0 16px 0;
  color: #1f2937;
}

.expired {
  color: #ef4444;
  font-weight: 600;
}

.expiring-soon {
  color: #f59e0b;
  font-weight: 600;
}

.vip-detail {
  max-height: 500px;
  overflow-y: auto;
}

.detail-section {
  margin-bottom: 24px;
}

.detail-section h4 {
  margin: 0 0 12px 0;
  color: #1f2937;
  font-size: 16px;
  font-weight: 600;
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 8px;
}

.detail-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.detail-item label {
  font-weight: 500;
  color: #374151;
  min-width: 80px;
}

.form-help {
  font-size: 12px;
  color: #6b7280;
  margin-left: 8px;
}
</style>
