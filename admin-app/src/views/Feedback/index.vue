<template>
  <div class="feedback-container">
    <div class="page-header">
      <div class="header-left">
        <h1 class="page-title">
          <MessageSquareIcon :size="24" />
          反馈管理
        </h1>
        <p class="page-description">管理用户反馈和建议</p>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="refreshData">
          <template #icon>
            <RefreshCwIcon :size="16" />
          </template>
          刷新
        </el-button>

      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-grid">
      <div class="stat-card">
        <div class="stat-icon total">
          <MessageSquareIcon :size="24" />
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stats.total || 0 }}</div>
          <div class="stat-label">总反馈数</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon pending">
          <ClockIcon :size="24" />
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stats.pending || 0 }}</div>
          <div class="stat-label">待处理</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon replied">
          <CheckCircleIcon :size="24" />
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stats.replied || 0 }}</div>
          <div class="stat-label">已回复</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon rate">
          <TrendingUpIcon :size="24" />
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stats.replyRate || 0 }}%</div>
          <div class="stat-label">回复率</div>
        </div>
      </div>
    </div>

    <!-- 筛选和搜索 -->
    <div class="filter-section">
      <el-form :model="filters" inline class="filter-form">
        <el-form-item label="状态">
          <el-select v-model="filters.status" placeholder="选择状态" style="width: 120px">
            <el-option label="全部" value="all" />
            <el-option label="待处理" value="pending" />
            <el-option label="处理中" value="processing" />
            <el-option label="已回复" value="replied" />
            <el-option label="已解决" value="resolved" />
            <el-option label="已关闭" value="closed" />
          </el-select>
        </el-form-item>
        <el-form-item label="类型">
          <el-select v-model="filters.type" placeholder="反馈类型" style="width: 120px">
            <el-option label="全部" value="all" />
            <el-option label="Bug报告" value="bug" />
            <el-option label="功能建议" value="feature" />
            <el-option label="投诉" value="complaint" />
            <el-option label="其他" value="other" />
          </el-select>
        </el-form-item>
        <el-form-item label="搜索">
          <el-input
            v-model="filters.keyword"
            placeholder="搜索反馈内容或邮箱"
            style="width: 250px"
            @keyup.enter="handleSearch"
          >
            <template #prefix>
              <SearchIcon :size="16" />
            </template>
          </el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <template #icon>
              <SearchIcon :size="16" />
            </template>
            搜索
          </el-button>
          <el-button @click="resetFilters">
            <template #icon>
              <RotateCcwIcon :size="16" />
            </template>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 反馈列表 -->
    <div class="table-section">
      <el-table
        v-loading="loading"
        :data="feedbackList"
        stripe
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="type" label="类型" width="100">
          <template #default="{ row }">
            <el-tag :type="getTypeColor(row.type)" size="small">
              {{ getTypeText(row.type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="content" label="反馈内容" min-width="300">
          <template #default="{ row }">
            <div class="feedback-content">
              <p class="content-text">{{ row.content }}</p>
              <div v-if="row.email" class="contact-info">
                <MailIcon :size="12" />
                {{ row.email }}
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusColor(row.status)" size="small">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="priority" label="优先级" width="80">
          <template #default="{ row }">
            <el-tag
              :type="getPriorityColor(row.priority)"
              size="small"
            >
              {{ getPriorityText(row.priority) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="提交时间" width="180">
          <template #default="{ row }">
            {{ formatDate(row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column prop="updateTime" label="更新时间" width="180">
          <template #default="{ row }">
            {{ formatDate(row.updateTime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button
              type="primary"
              size="small"
              @click="viewFeedbackDetail(row)"
            >
              <template #icon>
                <EyeIcon :size="14" />
              </template>
              查看
            </el-button>
            <el-button
              v-if="row.status === 'pending'"
              type="success"
              size="small"
              @click="replyFeedback(row)"
            >
              <template #icon>
                <ReplyIcon :size="14" />
              </template>
              回复
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.pageSize"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 批量操作 -->
    <div v-if="selectedFeedback.length > 0" class="batch-actions">
      <div class="batch-info">
        已选择 {{ selectedFeedback.length }} 条反馈
      </div>
      <div class="batch-buttons">
        <el-button type="warning" @click="batchUpdateStatus('processing')">
          标记为处理中
        </el-button>
        <el-button type="success" @click="batchUpdateStatus('resolved')">
          标记为已解决
        </el-button>
        <el-button type="info" @click="batchUpdateStatus('closed')">
          关闭反馈
        </el-button>
      </div>
    </div>

    <!-- 回复对话框 -->
    <el-dialog
      v-model="replyDialogVisible"
      title="回复反馈"
      width="600px"
      @close="resetReplyForm"
    >
      <div v-if="currentFeedback" class="reply-dialog">
        <div class="original-feedback">
          <h4>原始反馈</h4>
          <p>{{ currentFeedback.content }}</p>
          <div v-if="currentFeedback.email" class="contact-info">
            <MailIcon :size="14" />
            {{ currentFeedback.email }}
          </div>
        </div>
        
        <el-form :model="replyForm" label-width="80px">
          <el-form-item label="回复内容" required>
            <el-input
              v-model="replyForm.reply"
              type="textarea"
              :rows="6"
              placeholder="请输入回复内容..."
            />
          </el-form-item>
          <el-form-item label="状态">
            <el-select v-model="replyForm.status" style="width: 200px">
              <el-option label="已回复" value="replied" />
              <el-option label="已解决" value="resolved" />
              <el-option label="关闭" value="closed" />
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      
      <template #footer>
        <el-button @click="replyDialogVisible = false">取消</el-button>
        <el-button
          type="primary"
          :loading="replying"
          @click="submitReply"
        >
          发送回复
        </el-button>
      </template>
    </el-dialog>

    <!-- 反馈详情对话框 -->
    <el-dialog
      v-model="feedbackDetailDialogVisible"
      title="反馈详情"
      width="700px"
    >
      <div v-if="currentFeedback" class="feedback-detail">
        <div class="detail-section">
          <h4>基本信息</h4>
          <div class="detail-grid">
            <div class="detail-item">
              <label>反馈ID:</label>
              <span>{{ currentFeedback._id }}</span>
            </div>
            <div class="detail-item">
              <label>用户ID:</label>
              <span>{{ currentFeedback.userId }}</span>
            </div>
            <div class="detail-item">
              <label>类型:</label>
              <el-tag :type="getTypeColor(currentFeedback.type)" size="small">
                {{ getTypeText(currentFeedback.type) }}
              </el-tag>
            </div>
            <div class="detail-item">
              <label>状态:</label>
              <el-tag :type="getStatusColor(currentFeedback.status)" size="small">
                {{ getStatusText(currentFeedback.status) }}
              </el-tag>
            </div>
            <div class="detail-item">
              <label>提交时间:</label>
              <span>{{ formatDate(currentFeedback.createTime) }}</span>
            </div>
            <div class="detail-item" v-if="currentFeedback.email">
              <label>联系邮箱:</label>
              <span>{{ currentFeedback.email }}</span>
            </div>
          </div>
        </div>

        <div class="detail-section">
          <h4>反馈内容</h4>
          <div class="feedback-content">
            <h5>{{ currentFeedback.title }}</h5>
            <div class="content-text">{{ currentFeedback.content }}</div>
          </div>
        </div>

        <div v-if="currentFeedback.adminReply" class="detail-section">
          <h4>管理员回复</h4>
          <div class="reply-content">
            <div class="reply-text">{{ currentFeedback.adminReply }}</div>
            <div class="reply-time">
              回复时间: {{ formatDate(currentFeedback.replyTime) }}
            </div>
          </div>
        </div>
      </div>

      <template #footer>
        <el-button @click="feedbackDetailDialogVisible = false">关闭</el-button>
        <el-button
          type="primary"
          @click="replyToFeedback(currentFeedback)"
        >
          回复反馈
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  MessageSquare as MessageSquareIcon,
  Clock as ClockIcon,
  CheckCircle as CheckCircleIcon,
  TrendingUp as TrendingUpIcon,
  Search as SearchIcon,
  RotateCcw as RotateCcwIcon,
  RefreshCw as RefreshCwIcon,
  Eye as EyeIcon,
  Reply as ReplyIcon,
  Mail as MailIcon
} from 'lucide-vue-next'
import { callCloudFunction } from '@/api/wechat-api.js'

// 响应式数据
const loading = ref(false)
const replying = ref(false)
const feedbackList = ref([])
const selectedFeedback = ref([])
const stats = ref({})
const replyDialogVisible = ref(false)
const feedbackDetailDialogVisible = ref(false)
const currentFeedback = ref(null)

const filters = reactive({
  status: 'all',
  type: 'all',
  keyword: ''
})

const pagination = reactive({
  page: 1,
  pageSize: 20,
  total: 0
})

const replyForm = reactive({
  reply: '',
  status: 'replied'
})

// 方法
async function loadFeedbackList() {
  try {
    loading.value = true

    // 构建查询参数
    const params = {
      page: pagination.page,
      pageSize: pagination.pageSize
    }

    // 添加筛选条件
    if (filters.status !== 'all') {
      params.status = filters.status
    }
    if (filters.type !== 'all') {
      params.type = filters.type
    }
    if (filters.keyword.trim()) {
      params.keyword = filters.keyword.trim()
    }

    const result = await callCloudFunction('getFeedbackListAdmin', params)

    if (result.success) {
      feedbackList.value = result.data || []
      pagination.total = result.pagination?.total || 0
    } else {
      ElMessage.error(result.message || '获取反馈列表失败')
    }
  } catch (error) {
    console.error('获取反馈列表失败:', error)
    ElMessage.error('获取反馈列表失败')
  } finally {
    loading.value = false
  }
}

async function loadStats() {
  try {
    const result = await callCloudFunction('getFeedbackStatsAdmin', { period: '30d' })
    if (result.success) {
      stats.value = result.data || {}
    }
  } catch (error) {
    console.error('获取反馈统计失败:', error)
  }
}

function handleSearch() {
  pagination.page = 1
  loadFeedbackList()
}

function resetFilters() {
  filters.status = 'all'
  filters.type = 'all'
  filters.keyword = ''
  pagination.page = 1
  loadFeedbackList()
}

function handleSizeChange(size) {
  pagination.pageSize = size
  pagination.page = 1
  loadFeedbackList()
}

function handleCurrentChange(page) {
  pagination.page = page
  loadFeedbackList()
}

function handleSelectionChange(selection) {
  selectedFeedback.value = selection
}

async function refreshData() {
  await Promise.all([loadFeedbackList(), loadStats()])
  ElMessage.success('数据已刷新')
}

function viewFeedbackDetail(feedback) {
  currentFeedback.value = feedback
  feedbackDetailDialogVisible.value = true
}

function replyFeedback(feedback) {
  currentFeedback.value = feedback
  replyForm.reply = ''
  replyForm.status = 'replied'
  replyDialogVisible.value = true
}

function resetReplyForm() {
  currentFeedback.value = null
  replyForm.reply = ''
  replyForm.status = 'replied'
}

async function submitReply() {
  if (!replyForm.reply.trim()) {
    ElMessage.warning('请输入回复内容')
    return
  }

  try {
    replying.value = true

    const result = await callCloudFunction('replyFeedback', {
      id: currentFeedback.value._id,
      reply: replyForm.reply.trim(),
      status: replyForm.status,
      adminId: 'admin', // TODO: 使用实际管理员ID
      adminName: '管理员'
    })

    if (result.success) {
      ElMessage.success('回复发送成功')
      replyDialogVisible.value = false
      loadFeedbackList()
      loadStats()
    } else {
      ElMessage.error(result.message || '回复发送失败')
    }
  } catch (error) {
    console.error('回复发送失败:', error)
    ElMessage.error('回复发送失败')
  } finally {
    replying.value = false
  }
}

async function batchUpdateStatus(status) {
  try {
    const statusText = getStatusText(status)
    
    await ElMessageBox.confirm(
      `确定要将 ${selectedFeedback.value.length} 条反馈标记为"${statusText}"吗？`,
      '批量操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const ids = selectedFeedback.value.map(item => item._id)
    const result = await callCloudFunction('batchOperateFeedback', {
      ids,
      action: 'updateStatus',
      status
    })

    if (result.success) {
      ElMessage.success('批量操作成功')
      selectedFeedback.value = []
      loadFeedbackList()
    } else {
      ElMessage.error(result.message || '批量操作失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量操作失败:', error)
      ElMessage.error('批量操作失败')
    }
  }
}

// 辅助函数
function getTypeColor(type) {
  const colors = {
    bug: 'danger',
    feature: 'primary',
    complaint: 'warning',
    other: 'info'
  }
  return colors[type] || 'info'
}

function getTypeText(type) {
  const texts = {
    bug: 'Bug',
    feature: '功能',
    complaint: '投诉',
    other: '其他'
  }
  return texts[type] || type
}

function getStatusColor(status) {
  const colors = {
    pending: 'warning',
    processing: 'primary',
    replied: 'success',
    resolved: 'success',
    closed: 'info'
  }
  return colors[status] || 'info'
}

function getStatusText(status) {
  const texts = {
    pending: '待处理',
    processing: '处理中',
    replied: '已回复',
    resolved: '已解决',
    closed: '已关闭'
  }
  return texts[status] || status
}

function getPriorityColor(priority) {
  if (priority >= 4) return 'danger'
  if (priority >= 3) return 'warning'
  return 'info'
}

function getPriorityText(priority) {
  if (priority >= 4) return '高'
  if (priority >= 3) return '中'
  return '低'
}

function formatDate(dateString) {
  if (!dateString) return '-'
  return new Date(dateString).toLocaleString('zh-CN')
}

// 生命周期
onMounted(() => {
  loadFeedbackList()
  loadStats()
})
</script>

<style scoped>
.feedback-container {
  padding: 24px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 8px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.page-description {
  color: #6b7280;
  margin: 0;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.stat-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.stat-icon.total { background: #3b82f6; }
.stat-icon.pending { background: #f59e0b; }
.stat-icon.replied { background: #10b981; }
.stat-icon.rate { background: #8b5cf6; }

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
}

.stat-label {
  font-size: 14px;
  color: #6b7280;
}

.filter-section {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.table-section {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.feedback-content {
  max-width: 300px;
}

.content-text {
  margin: 0 0 8px 0;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.contact-info {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #6b7280;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.batch-actions {
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  background: white;
  border-radius: 8px;
  padding: 16px 24px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  display: flex;
  align-items: center;
  gap: 16px;
  z-index: 1000;
}

.batch-info {
  font-weight: 500;
  color: #374151;
}

.batch-buttons {
  display: flex;
  gap: 8px;
}

.reply-dialog .original-feedback {
  background: #f9fafb;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 20px;
}

.reply-dialog .original-feedback h4 {
  margin: 0 0 8px 0;
  color: #374151;
}

.reply-dialog .original-feedback p {
  margin: 0 0 8px 0;
  line-height: 1.5;
}

.feedback-detail {
  max-height: 500px;
  overflow-y: auto;
}

.detail-section {
  margin-bottom: 24px;
}

.detail-section h4 {
  margin: 0 0 12px 0;
  color: #1f2937;
  font-size: 16px;
  font-weight: 600;
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 8px;
}

.detail-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.detail-item label {
  font-weight: 500;
  color: #374151;
  min-width: 80px;
}

.feedback-content h5 {
  margin: 0 0 8px 0;
  color: #1f2937;
  font-size: 16px;
  font-weight: 600;
}

.content-text {
  background: #f9fafb;
  padding: 12px;
  border-radius: 6px;
  line-height: 1.6;
  white-space: pre-wrap;
}

.reply-content {
  background: #f0f9ff;
  padding: 12px;
  border-radius: 6px;
  border-left: 4px solid #3b82f6;
}

.reply-text {
  line-height: 1.6;
  margin-bottom: 8px;
  white-space: pre-wrap;
}

.reply-time {
  font-size: 12px;
  color: #6b7280;
}
</style>
