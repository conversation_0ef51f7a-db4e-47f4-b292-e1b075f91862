<template>
  <div class="export-container">
    <div class="page-header">
      <div class="header-left">
        <h1 class="page-title">
          <DownloadIcon :size="24" />
          数据导出
        </h1>
        <p class="page-description">导出各类数据用于分析和备份</p>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="refreshExportHistory">
          <template #icon>
            <RefreshCwIcon :size="16" />
          </template>
          刷新
        </el-button>
      </div>
    </div>

    <!-- 导出选项卡片 -->
    <div class="export-cards">
      <div class="export-card">
        <div class="card-header">
          <div class="card-icon users">
            <UsersIcon :size="24" />
          </div>
          <div class="card-title">
            <h3>用户数据</h3>
            <p>导出用户信息和统计数据</p>
          </div>
        </div>
        <div class="card-content">
          <el-form :model="userExportForm" label-width="80px" size="small">
            <el-form-item label="格式">
              <el-select v-model="userExportForm.format" style="width: 120px">
                <el-option label="JSON" value="json" />
                <el-option label="CSV" value="csv" />
              </el-select>
            </el-form-item>
            <el-form-item label="状态">
              <el-select v-model="userExportForm.status" style="width: 120px">
                <el-option label="全部" value="all" />
                <el-option label="活跃" value="active" />
                <el-option label="非活跃" value="inactive" />
              </el-select>
            </el-form-item>
            <el-form-item label="数量">
              <el-input-number
                v-model="userExportForm.limit"
                :min="1"
                :max="10000"
                style="width: 120px"
              />
            </el-form-item>
          </el-form>
        </div>
        <div class="card-actions">
          <el-button
            type="primary"
            :loading="userExporting"
            @click="exportUserData"
          >
            <template #icon>
              <DownloadIcon :size="16" />
            </template>
            导出用户数据
          </el-button>
        </div>
      </div>

      <div class="export-card">
        <div class="card-header">
          <div class="card-icon feedback">
            <MessageSquareIcon :size="24" />
          </div>
          <div class="card-title">
            <h3>反馈数据</h3>
            <p>导出用户反馈和处理记录</p>
          </div>
        </div>
        <div class="card-content">
          <el-form :model="feedbackExportForm" label-width="80px" size="small">
            <el-form-item label="格式">
              <el-select v-model="feedbackExportForm.format" style="width: 120px">
                <el-option label="JSON" value="json" />
                <el-option label="CSV" value="csv" />
              </el-select>
            </el-form-item>
            <el-form-item label="状态">
              <el-select v-model="feedbackExportForm.status" style="width: 120px">
                <el-option label="全部" value="all" />
                <el-option label="待处理" value="pending" />
                <el-option label="已回复" value="replied" />
              </el-select>
            </el-form-item>
            <el-form-item label="数量">
              <el-input-number
                v-model="feedbackExportForm.limit"
                :min="1"
                :max="10000"
                style="width: 120px"
              />
            </el-form-item>
          </el-form>
        </div>
        <div class="card-actions">
          <el-button
            type="primary"
            :loading="feedbackExporting"
            @click="exportFeedbackData"
          >
            <template #icon>
              <DownloadIcon :size="16" />
            </template>
            导出反馈数据
          </el-button>
        </div>
      </div>

      <div class="export-card">
        <div class="card-header">
          <div class="card-icon points">
            <CoinsIcon :size="24" />
          </div>
          <div class="card-title">
            <h3>积分数据</h3>
            <p>导出积分记录和统计信息</p>
          </div>
        </div>
        <div class="card-content">
          <el-form :model="pointsExportForm" label-width="80px" size="small">
            <el-form-item label="格式">
              <el-select v-model="pointsExportForm.format" style="width: 120px">
                <el-option label="JSON" value="json" />
                <el-option label="CSV" value="csv" />
              </el-select>
            </el-form-item>
            <el-form-item label="类型">
              <el-select v-model="pointsExportForm.type" style="width: 120px">
                <el-option label="全部" value="all" />
                <el-option label="获得" value="earn" />
                <el-option label="消费" value="spend" />
              </el-select>
            </el-form-item>
            <el-form-item label="数量">
              <el-input-number
                v-model="pointsExportForm.limit"
                :min="1"
                :max="10000"
                style="width: 120px"
              />
            </el-form-item>
          </el-form>
        </div>
        <div class="card-actions">
          <el-button
            type="primary"
            :loading="pointsExporting"
            @click="exportPointsData"
          >
            <template #icon>
              <DownloadIcon :size="16" />
            </template>
            导出积分数据
          </el-button>
        </div>
      </div>

      <div class="export-card">
        <div class="card-header">
          <div class="card-icon checkin">
            <CalendarCheckIcon :size="24" />
          </div>
          <div class="card-title">
            <h3>签到数据</h3>
            <p>导出用户签到记录</p>
          </div>
        </div>
        <div class="card-content">
          <el-form :model="checkinExportForm" label-width="80px" size="small">
            <el-form-item label="格式">
              <el-select v-model="checkinExportForm.format" style="width: 120px">
                <el-option label="JSON" value="json" />
                <el-option label="CSV" value="csv" />
              </el-select>
            </el-form-item>
            <el-form-item label="用户">
              <el-input
                v-model="checkinExportForm.userId"
                placeholder="全部用户"
                style="width: 120px"
              />
            </el-form-item>
            <el-form-item label="数量">
              <el-input-number
                v-model="checkinExportForm.limit"
                :min="1"
                :max="10000"
                style="width: 120px"
              />
            </el-form-item>
          </el-form>
        </div>
        <div class="card-actions">
          <el-button
            type="primary"
            :loading="checkinExporting"
            @click="exportCheckinData"
          >
            <template #icon>
              <DownloadIcon :size="16" />
            </template>
            导出签到数据
          </el-button>
        </div>
      </div>

      <div class="export-card">
        <div class="card-header">
          <div class="card-icon all">
            <DatabaseIcon :size="24" />
          </div>
          <div class="card-title">
            <h3>全量数据</h3>
            <p>导出所有用户数据</p>
          </div>
        </div>
        <div class="card-content">
          <el-form :model="allDataExportForm" label-width="80px" size="small">
            <el-form-item label="格式">
              <el-select v-model="allDataExportForm.format" style="width: 120px">
                <el-option label="JSON" value="json" />
                <el-option label="CSV" value="csv" />
              </el-select>
            </el-form-item>
            <el-form-item label="类型">
              <el-select v-model="allDataExportForm.dataType" style="width: 120px">
                <el-option label="全部" value="all" />
                <el-option label="用户资料" value="profile" />
                <el-option label="设置" value="settings" />
              </el-select>
            </el-form-item>
            <el-form-item label="数量">
              <el-input-number
                v-model="allDataExportForm.limit"
                :min="1"
                :max="10000"
                style="width: 120px"
              />
            </el-form-item>
          </el-form>
        </div>
        <div class="card-actions">
          <el-button
            type="primary"
            :loading="allDataExporting"
            @click="exportAllData"
          >
            <template #icon>
              <DownloadIcon :size="16" />
            </template>
            导出全量数据
          </el-button>
        </div>
      </div>
    </div>

    <!-- 导出历史 -->
    <div class="history-section">
      <div class="section-header">
        <h3>导出历史</h3>
        <el-button type="danger" @click="clearHistory">
          <template #icon>
            <TrashIcon :size="16" />
          </template>
          清空历史
        </el-button>
      </div>
      
      <el-table
        v-loading="historyLoading"
        :data="exportHistory"
        stripe
        style="width: 100%"
      >
        <el-table-column prop="type" label="数据类型" width="120">
          <template #default="{ row }">
            <el-tag size="small">{{ getTypeText(row.type) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="format" label="格式" width="80">
          <template #default="{ row }">
            <el-tag type="info" size="small">{{ row.format.toUpperCase() }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="recordCount" label="记录数" width="100" />
        <el-table-column prop="fileSize" label="文件大小" width="100">
          <template #default="{ row }">
            {{ formatFileSize(row.fileSize) }}
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusColor(row.status)" size="small">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="导出时间" width="180">
          <template #default="{ row }">
            {{ formatDate(row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="{ row }">
            <el-button
              v-if="row.status === 'completed'"
              type="primary"
              size="small"
              @click="downloadFile(row)"
            >
              <template #icon>
                <DownloadIcon :size="14" />
              </template>
              下载
            </el-button>
            <el-button
              type="danger"
              size="small"
              @click="deleteExport(row)"
            >
              <template #icon>
                <TrashIcon :size="14" />
              </template>
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Download as DownloadIcon,
  RefreshCw as RefreshCwIcon,
  Users as UsersIcon,
  MessageSquare as MessageSquareIcon,
  Coins as CoinsIcon,
  CalendarCheck as CalendarCheckIcon,
  Database as DatabaseIcon,
  Trash as TrashIcon
} from 'lucide-vue-next'
import { callCloudFunction } from '@/api/wechat-api.js'

// 响应式数据
const userExporting = ref(false)
const feedbackExporting = ref(false)
const pointsExporting = ref(false)
const checkinExporting = ref(false)
const allDataExporting = ref(false)
const historyLoading = ref(false)
const exportHistory = ref([])

// 导出表单
const userExportForm = reactive({
  format: 'json',
  status: 'all',
  limit: 1000
})

const feedbackExportForm = reactive({
  format: 'json',
  status: 'all',
  limit: 1000
})

const pointsExportForm = reactive({
  format: 'json',
  type: 'all',
  limit: 1000
})

const checkinExportForm = reactive({
  format: 'json',
  userId: '',
  limit: 1000
})

const allDataExportForm = reactive({
  format: 'json',
  dataType: 'all',
  limit: 1000
})

// 方法
async function exportUserData() {
  try {
    userExporting.value = true
    
    const result = await callCloudFunction('exportUserData', {
      ...userExportForm,
      userId: 'all'
    })

    if (result.success) {
      ElMessage.success('用户数据导出成功')
      addToHistory('user', userExportForm.format, result.data)
      refreshExportHistory()
    } else {
      ElMessage.error(result.message || '导出失败')
    }
  } catch (error) {
    console.error('导出用户数据失败:', error)
    ElMessage.error('导出失败')
  } finally {
    userExporting.value = false
  }
}

async function exportFeedbackData() {
  try {
    feedbackExporting.value = true
    
    const result = await callCloudFunction('exportFeedbackData', feedbackExportForm)

    if (result.success) {
      ElMessage.success('反馈数据导出成功')
      addToHistory('feedback', feedbackExportForm.format, result.data)
      refreshExportHistory()
    } else {
      ElMessage.error(result.message || '导出失败')
    }
  } catch (error) {
    console.error('导出反馈数据失败:', error)
    ElMessage.error('导出失败')
  } finally {
    feedbackExporting.value = false
  }
}

async function exportPointsData() {
  try {
    pointsExporting.value = true
    
    const result = await callCloudFunction('exportPointsData', {
      ...pointsExportForm,
      userId: 'all'
    })

    if (result.success) {
      ElMessage.success('积分数据导出成功')
      addToHistory('points', pointsExportForm.format, result.data)
      refreshExportHistory()
    } else {
      ElMessage.error(result.message || '导出失败')
    }
  } catch (error) {
    console.error('导出积分数据失败:', error)
    ElMessage.error('导出失败')
  } finally {
    pointsExporting.value = false
  }
}

async function exportCheckinData() {
  try {
    checkinExporting.value = true
    
    const result = await callCloudFunction('exportCheckInData', {
      ...checkinExportForm,
      userId: checkinExportForm.userId || 'all'
    })

    if (result.success) {
      ElMessage.success('签到数据导出成功')
      addToHistory('checkin', checkinExportForm.format, result.data)
      refreshExportHistory()
    } else {
      ElMessage.error(result.message || '导出失败')
    }
  } catch (error) {
    console.error('导出签到数据失败:', error)
    ElMessage.error('导出失败')
  } finally {
    checkinExporting.value = false
  }
}

async function exportAllData() {
  try {
    allDataExporting.value = true
    
    const result = await callCloudFunction('exportAllDataAdmin', allDataExportForm)

    if (result.success) {
      ElMessage.success('全量数据导出成功')
      addToHistory('all', allDataExportForm.format, result.data)
      refreshExportHistory()
    } else {
      ElMessage.error(result.message || '导出失败')
    }
  } catch (error) {
    console.error('导出全量数据失败:', error)
    ElMessage.error('导出失败')
  } finally {
    allDataExporting.value = false
  }
}

function addToHistory(type, format, data) {
  const historyItem = {
    id: Date.now(),
    type,
    format,
    recordCount: Array.isArray(data) ? data.length : 0,
    fileSize: JSON.stringify(data).length,
    status: 'completed',
    createTime: new Date().toISOString(),
    data
  }
  
  // 添加到本地历史记录
  const history = JSON.parse(localStorage.getItem('export_history') || '[]')
  history.unshift(historyItem)
  
  // 只保留最近50条记录
  if (history.length > 50) {
    history.splice(50)
  }
  
  localStorage.setItem('export_history', JSON.stringify(history))
}

function refreshExportHistory() {
  try {
    historyLoading.value = true
    const history = JSON.parse(localStorage.getItem('export_history') || '[]')
    exportHistory.value = history
  } catch (error) {
    console.error('加载导出历史失败:', error)
  } finally {
    historyLoading.value = false
  }
}

async function clearHistory() {
  try {
    await ElMessageBox.confirm(
      '确定要清空所有导出历史吗？此操作不可恢复。',
      '清空历史',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    localStorage.removeItem('export_history')
    exportHistory.value = []
    ElMessage.success('导出历史已清空')
  } catch (error) {
    if (error !== 'cancel') {
      console.error('清空历史失败:', error)
    }
  }
}

function downloadFile(item) {
  try {
    const dataStr = JSON.stringify(item.data, null, 2)
    const blob = new Blob([dataStr], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    
    const link = document.createElement('a')
    link.href = url
    link.download = `${item.type}_export_${new Date(item.createTime).toISOString().slice(0, 10)}.${item.format}`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    
    URL.revokeObjectURL(url)
    ElMessage.success('文件下载成功')
  } catch (error) {
    console.error('下载文件失败:', error)
    ElMessage.error('下载失败')
  }
}

function deleteExport(item) {
  try {
    const history = JSON.parse(localStorage.getItem('export_history') || '[]')
    const index = history.findIndex(h => h.id === item.id)
    if (index > -1) {
      history.splice(index, 1)
      localStorage.setItem('export_history', JSON.stringify(history))
      refreshExportHistory()
      ElMessage.success('删除成功')
    }
  } catch (error) {
    console.error('删除导出记录失败:', error)
    ElMessage.error('删除失败')
  }
}

// 辅助函数
function getTypeText(type) {
  const texts = {
    user: '用户数据',
    feedback: '反馈数据',
    points: '积分数据',
    checkin: '签到数据',
    all: '全量数据'
  }
  return texts[type] || type
}

function getStatusColor(status) {
  const colors = {
    completed: 'success',
    processing: 'warning',
    failed: 'danger'
  }
  return colors[status] || 'info'
}

function getStatusText(status) {
  const texts = {
    completed: '已完成',
    processing: '处理中',
    failed: '失败'
  }
  return texts[status] || status
}

function formatFileSize(bytes) {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

function formatDate(dateString) {
  if (!dateString) return '-'
  return new Date(dateString).toLocaleString('zh-CN')
}

// 生命周期
onMounted(() => {
  refreshExportHistory()
})
</script>

<style scoped>
.export-container {
  padding: 24px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 8px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.page-description {
  color: #6b7280;
  margin: 0;
}

.export-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 20px;
  margin-bottom: 32px;
}

.export-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 20px;
}

.card-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.card-icon.users { background: #3b82f6; }
.card-icon.feedback { background: #10b981; }
.card-icon.points { background: #f59e0b; }
.card-icon.checkin { background: #8b5cf6; }
.card-icon.all { background: #ef4444; }

.card-title h3 {
  margin: 0 0 4px 0;
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}

.card-title p {
  margin: 0;
  font-size: 14px;
  color: #6b7280;
}

.card-content {
  margin-bottom: 20px;
}

.card-actions {
  display: flex;
  justify-content: flex-end;
}

.history-section {
  background: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.section-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}
</style>
