<template>
  <div class="cache-container">
    <div class="page-header">
      <div class="header-left">
        <h1 class="page-title">
          <DatabaseIcon :size="24" />
          缓存管理
        </h1>
        <p class="page-description">系统缓存监控和管理</p>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="refreshData">
          <template #icon>
            <RefreshCwIcon :size="16" />
          </template>
          刷新
        </el-button>
        <el-button type="warning" @click="cleanupExpiredCache">
          <template #icon>
            <TrashIcon :size="16" />
          </template>
          清理过期缓存
        </el-button>
        <el-button type="danger" @click="clearAllCache">
          <template #icon>
            <XIcon :size="16" />
          </template>
          清空所有缓存
        </el-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-grid">
      <div class="stat-card">
        <div class="stat-icon total">
          <DatabaseIcon :size="24" />
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stats.total || 0 }}</div>
          <div class="stat-label">总缓存数</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon active">
          <CheckCircleIcon :size="24" />
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stats.active || 0 }}</div>
          <div class="stat-label">有效缓存</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon expired">
          <ClockIcon :size="24" />
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stats.expired || 0 }}</div>
          <div class="stat-label">过期缓存</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon hit-rate">
          <TrendingUpIcon :size="24" />
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stats.hitRate || 0 }}%</div>
          <div class="stat-label">命中率</div>
        </div>
      </div>
    </div>

    <!-- 功能标签页 -->
    <el-tabs v-model="activeTab" class="cache-tabs">
      <el-tab-pane label="缓存列表" name="list">
        <!-- 筛选和搜索 -->
        <div class="filter-section">
          <el-form :model="filters" inline class="filter-form">
            <el-form-item label="状态">
              <el-select v-model="filters.status" placeholder="选择状态" style="width: 120px">
                <el-option label="全部" value="all" />
                <el-option label="有效" value="active" />
                <el-option label="过期" value="expired" />
              </el-select>
            </el-form-item>
            <el-form-item label="缓存键">
              <el-input
                v-model="filters.keyword"
                placeholder="搜索缓存键"
                style="width: 250px"
              >
                <template #prefix>
                  <SearchIcon :size="16" />
                </template>
              </el-input>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="loadCacheList">
                <template #icon>
                  <SearchIcon :size="16" />
                </template>
                搜索
              </el-button>
              <el-button @click="resetFilters">
                <template #icon>
                  <RotateCcwIcon :size="16" />
                </template>
                重置
              </el-button>
            </el-form-item>
          </el-form>
        </div>

        <!-- 缓存列表表格 -->
        <div class="table-section">
          <el-table
            v-loading="loading"
            :data="cacheList"
            stripe
            style="width: 100%"
            @selection-change="handleSelectionChange"
          >
            <el-table-column type="selection" width="55" />
            <el-table-column prop="key" label="缓存键" min-width="200">
              <template #default="{ row }">
                <span class="cache-key">{{ row.key }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="status" label="状态" width="100">
              <template #default="{ row }">
                <el-tag :type="getCacheStatusColor(row.status)" size="small">
                  {{ getCacheStatusText(row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="size" label="大小" width="100">
              <template #default="{ row }">
                {{ formatSize(row.size) }}
              </template>
            </el-table-column>
            <el-table-column prop="createTime" label="创建时间" width="180">
              <template #default="{ row }">
                {{ formatDate(row.createTime) }}
              </template>
            </el-table-column>
            <el-table-column prop="expireTime" label="过期时间" width="180">
              <template #default="{ row }">
                <span :class="getExpiryClass(row.expireTime)">
                  {{ formatDate(row.expireTime) }}
                </span>
              </template>
            </el-table-column>
            <el-table-column prop="remainingTime" label="剩余时间" width="120">
              <template #default="{ row }">
                <span :class="getRemainingTimeClass(row.expireTime)">
                  {{ formatRemainingTime(row.expireTime) }}
                </span>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="200" fixed="right">
              <template #default="{ row }">
                <el-button
                  type="primary"
                  size="small"
                  @click="viewCacheDetail(row)"
                >
                  <template #icon>
                    <EyeIcon :size="14" />
                  </template>
                  查看
                </el-button>
                <el-button
                  type="warning"
                  size="small"
                  @click="refreshCache(row)"
                >
                  <template #icon>
                    <RefreshCwIcon :size="14" />
                  </template>
                  刷新
                </el-button>
                <el-button
                  type="danger"
                  size="small"
                  @click="deleteCache(row)"
                >
                  <template #icon>
                    <TrashIcon :size="14" />
                  </template>
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>

          <!-- 分页 -->
          <div class="pagination-wrapper">
            <el-pagination
              v-model:current-page="pagination.page"
              v-model:page-size="pagination.pageSize"
              :total="pagination.total"
              :page-sizes="[10, 20, 50, 100]"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            />
          </div>
        </div>

        <!-- 批量操作 -->
        <div v-if="selectedCaches.length > 0" class="batch-actions">
          <div class="batch-info">
            已选择 {{ selectedCaches.length }} 个缓存
          </div>
          <div class="batch-buttons">
            <el-button type="warning" @click="batchRefreshCache">
              批量刷新
            </el-button>
            <el-button type="danger" @click="batchDeleteCache">
              批量删除
            </el-button>
          </div>
        </div>
      </el-tab-pane>

      <el-tab-pane label="缓存统计" name="statistics">
        <div class="statistics-section">
          <!-- 统计筛选 -->
          <div class="stats-filter">
            <el-form inline>
              <el-form-item label="统计周期">
                <el-select v-model="statsPeriod" @change="loadStatistics">
                  <el-option label="最近1小时" value="1h" />
                  <el-option label="最近24小时" value="24h" />
                  <el-option label="最近7天" value="7d" />
                </el-select>
              </el-form-item>
            </el-form>
          </div>

          <!-- 统计图表 -->
          <div class="charts-grid">
            <div class="chart-card">
              <h4>缓存命中率趋势</h4>
              <div class="chart-placeholder">
                <!-- TODO: 集成图表库显示缓存命中率趋势 -->
                <p>缓存命中率趋势图表</p>
              </div>
            </div>
            <div class="chart-card">
              <h4>缓存使用量统计</h4>
              <div class="chart-placeholder">
                <!-- TODO: 集成图表库显示缓存使用量 -->
                <p>缓存使用量统计图表</p>
              </div>
            </div>
          </div>

          <!-- 详细统计 -->
          <div class="detailed-stats">
            <h4>缓存性能指标</h4>
            <div class="stats-table">
              <el-table :data="performanceStats" stripe>
                <el-table-column prop="metric" label="指标" width="200" />
                <el-table-column prop="value" label="数值" width="150" />
                <el-table-column prop="description" label="说明" />
              </el-table>
            </div>
          </div>
        </div>
      </el-tab-pane>

      <el-tab-pane label="缓存配置" name="config">
        <div class="config-section">
          <el-form
            ref="configFormRef"
            :model="cacheConfig"
            label-width="150px"
            style="max-width: 600px"
          >
            <el-form-item label="默认过期时间">
              <el-input-number
                v-model="cacheConfig.defaultTTL"
                :min="60"
                :max="86400"
                style="width: 200px"
              />
              <span class="form-help">秒（60-86400）</span>
            </el-form-item>
            <el-form-item label="最大缓存数量">
              <el-input-number
                v-model="cacheConfig.maxCacheCount"
                :min="100"
                :max="10000"
                style="width: 200px"
              />
              <span class="form-help">个（100-10000）</span>
            </el-form-item>
            <el-form-item label="自动清理间隔">
              <el-input-number
                v-model="cacheConfig.cleanupInterval"
                :min="300"
                :max="3600"
                style="width: 200px"
              />
              <span class="form-help">秒（300-3600）</span>
            </el-form-item>
            <el-form-item label="启用缓存">
              <el-switch
                v-model="cacheConfig.enabled"
                active-text="启用"
                inactive-text="禁用"
              />
            </el-form-item>
            <el-form-item>
              <el-button
                type="primary"
                :loading="configSaving"
                @click="saveCacheConfig"
              >
                保存配置
              </el-button>
              <el-button @click="resetCacheConfig">
                重置配置
              </el-button>
            </el-form-item>
          </el-form>
        </div>
      </el-tab-pane>
    </el-tabs>

    <!-- 缓存详情对话框 -->
    <el-dialog
      v-model="cacheDetailDialogVisible"
      title="缓存详情"
      width="700px"
    >
      <div v-if="currentCache" class="cache-detail">
        <div class="detail-section">
          <h4>基本信息</h4>
          <div class="detail-grid">
            <div class="detail-item">
              <label>缓存键:</label>
              <span class="cache-key">{{ currentCache.key }}</span>
            </div>
            <div class="detail-item">
              <label>状态:</label>
              <el-tag :type="getCacheStatusColor(currentCache.status)" size="small">
                {{ getCacheStatusText(currentCache.status) }}
              </el-tag>
            </div>
            <div class="detail-item">
              <label>大小:</label>
              <span>{{ formatSize(currentCache.size) }}</span>
            </div>
            <div class="detail-item">
              <label>创建时间:</label>
              <span>{{ formatDate(currentCache.createTime) }}</span>
            </div>
            <div class="detail-item">
              <label>过期时间:</label>
              <span>{{ formatDate(currentCache.expireTime) }}</span>
            </div>
            <div class="detail-item">
              <label>剩余时间:</label>
              <span>{{ formatRemainingTime(currentCache.expireTime) }}</span>
            </div>
          </div>
        </div>

        <div class="detail-section">
          <h4>缓存内容</h4>
          <div class="cache-content">
            <el-input
              v-model="currentCache.content"
              type="textarea"
              :rows="10"
              readonly
              placeholder="缓存内容"
            />
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Database as DatabaseIcon,
  CheckCircle as CheckCircleIcon,
  Clock as ClockIcon,
  TrendingUp as TrendingUpIcon,
  RefreshCw as RefreshCwIcon,
  Trash as TrashIcon,
  X as XIcon,
  Search as SearchIcon,
  RotateCcw as RotateCcwIcon,
  Eye as EyeIcon
} from 'lucide-vue-next'
import { callCloudFunction } from '@/api/wechat-api.js'

// 响应式数据
const loading = ref(false)
const configSaving = ref(false)
const activeTab = ref('list')
const cacheList = ref([])
const selectedCaches = ref([])
const stats = ref({})
const performanceStats = ref([])
const cacheDetailDialogVisible = ref(false)
const currentCache = ref(null)
const configFormRef = ref()
const statsPeriod = ref('24h')

const filters = reactive({
  status: 'all',
  keyword: ''
})

const pagination = reactive({
  page: 1,
  pageSize: 20,
  total: 0
})

const cacheConfig = reactive({
  defaultTTL: 3600,
  maxCacheCount: 1000,
  cleanupInterval: 600,
  enabled: true
})

// 方法
async function loadCacheList() {
  try {
    loading.value = true
    
    const params = {
      page: pagination.page,
      pageSize: pagination.pageSize
    }
    
    if (filters.status !== 'all') {
      params.status = filters.status
    }
    if (filters.keyword.trim()) {
      params.keyword = filters.keyword.trim()
    }
    
    const result = await callCloudFunction('getCacheListAdmin', params)

    if (result.success) {
      cacheList.value = result.data || []
      pagination.total = result.pagination?.total || 0
    } else {
      ElMessage.error(result.message || '获取缓存列表失败')
    }
  } catch (error) {
    console.error('获取缓存列表失败:', error)
    ElMessage.error('获取缓存列表失败')
  } finally {
    loading.value = false
  }
}

async function loadStats() {
  try {
    const result = await callCloudFunction('getCacheStatsAdmin', { period: statsPeriod.value })
    if (result.success) {
      stats.value = result.data || {}
      
      // 构建性能统计数据
      performanceStats.value = [
        { metric: '总缓存数', value: stats.value.total || 0, description: '系统中所有缓存项的数量' },
        { metric: '有效缓存', value: stats.value.active || 0, description: '当前有效的缓存项数量' },
        { metric: '过期缓存', value: stats.value.expired || 0, description: '已过期但未清理的缓存项' },
        { metric: '命中率', value: `${stats.value.hitRate || 0}%`, description: '缓存命中的百分比' },
        { metric: '平均大小', value: formatSize(stats.value.averageSize || 0), description: '每个缓存项的平均大小' }
      ]
    }
  } catch (error) {
    console.error('获取缓存统计失败:', error)
  }
}

async function loadCacheConfig() {
  try {
    const result = await callCloudFunction('getCacheConfigAdmin')
    if (result.success && result.data) {
      Object.assign(cacheConfig, result.data)
    }
  } catch (error) {
    console.error('获取缓存配置失败:', error)
  }
}

async function loadStatistics() {
  await loadStats()
}

function resetFilters() {
  filters.status = 'all'
  filters.keyword = ''
  pagination.page = 1
  loadCacheList()
}

function handleSizeChange(size) {
  pagination.pageSize = size
  pagination.page = 1
  loadCacheList()
}

function handleCurrentChange(page) {
  pagination.page = page
  loadCacheList()
}

function handleSelectionChange(selection) {
  selectedCaches.value = selection
}

async function refreshData() {
  await Promise.all([
    loadCacheList(),
    loadStats()
  ])
  ElMessage.success('数据已刷新')
}

async function cleanupExpiredCache() {
  try {
    await ElMessageBox.confirm(
      '确定要清理所有过期缓存吗？',
      '清理过期缓存',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const result = await callCloudFunction('cleanupExpiredCacheAdmin')

    if (result.success) {
      ElMessage.success(`清理完成，共清理 ${result.data?.cleanedCount || 0} 个过期缓存`)
      refreshData()
    } else {
      ElMessage.error(result.message || '清理失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('清理过期缓存失败:', error)
      ElMessage.error('清理失败')
    }
  }
}

async function clearAllCache() {
  try {
    await ElMessageBox.confirm(
      '确定要清空所有缓存吗？此操作不可恢复！',
      '清空所有缓存',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'error'
      }
    )

    const result = await callCloudFunction('clearAllCacheAdmin')

    if (result.success) {
      ElMessage.success('所有缓存已清空')
      refreshData()
    } else {
      ElMessage.error(result.message || '清空失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('清空缓存失败:', error)
      ElMessage.error('清空失败')
    }
  }
}

function viewCacheDetail(cache) {
  currentCache.value = {
    ...cache,
    content: JSON.stringify(cache.value || {}, null, 2)
  }
  cacheDetailDialogVisible.value = true
}

async function refreshCache(cache) {
  try {
    const result = await callCloudFunction('refreshCacheAdmin', {
      key: cache.key
    })

    if (result.success) {
      ElMessage.success('缓存刷新成功')
      loadCacheList()
    } else {
      ElMessage.error(result.message || '缓存刷新失败')
    }
  } catch (error) {
    console.error('缓存刷新失败:', error)
    ElMessage.error('缓存刷新失败')
  }
}

async function deleteCache(cache) {
  try {
    await ElMessageBox.confirm(
      `确定要删除缓存 "${cache.key}" 吗？`,
      '删除缓存',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const result = await callCloudFunction('deleteCacheAdmin', {
      key: cache.key
    })

    if (result.success) {
      ElMessage.success('缓存删除成功')
      loadCacheList()
    } else {
      ElMessage.error(result.message || '缓存删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('缓存删除失败:', error)
      ElMessage.error('缓存删除失败')
    }
  }
}

async function batchRefreshCache() {
  try {
    const keys = selectedCaches.value.map(cache => cache.key)
    
    const result = await callCloudFunction('batchRefreshCacheAdmin', { keys })

    if (result.success) {
      ElMessage.success(`批量刷新成功，共刷新 ${keys.length} 个缓存`)
      selectedCaches.value = []
      loadCacheList()
    } else {
      ElMessage.error(result.message || '批量刷新失败')
    }
  } catch (error) {
    console.error('批量刷新失败:', error)
    ElMessage.error('批量刷新失败')
  }
}

async function batchDeleteCache() {
  try {
    await ElMessageBox.confirm(
      `确定要删除 ${selectedCaches.value.length} 个缓存吗？`,
      '批量删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const keys = selectedCaches.value.map(cache => cache.key)
    
    const result = await callCloudFunction('batchDeleteCacheAdmin', { keys })

    if (result.success) {
      ElMessage.success(`批量删除成功，共删除 ${keys.length} 个缓存`)
      selectedCaches.value = []
      loadCacheList()
    } else {
      ElMessage.error(result.message || '批量删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除失败:', error)
      ElMessage.error('批量删除失败')
    }
  }
}

async function saveCacheConfig() {
  try {
    configSaving.value = true

    const result = await callCloudFunction('updateCacheConfigAdmin', cacheConfig)

    if (result.success) {
      ElMessage.success('缓存配置保存成功')
    } else {
      ElMessage.error(result.message || '保存配置失败')
    }
  } catch (error) {
    console.error('保存配置失败:', error)
    ElMessage.error('保存配置失败')
  } finally {
    configSaving.value = false
  }
}

function resetCacheConfig() {
  loadCacheConfig()
}

// 辅助函数
function getCacheStatusColor(status) {
  return status === 'active' ? 'success' : 'warning'
}

function getCacheStatusText(status) {
  return status === 'active' ? '有效' : '过期'
}

function getExpiryClass(expireTime) {
  if (!expireTime) return ''
  const remaining = new Date(expireTime).getTime() - new Date().getTime()
  if (remaining <= 0) return 'expired'
  if (remaining <= 60 * 60 * 1000) return 'expiring-soon' // 1小时内过期
  return ''
}

function getRemainingTimeClass(expireTime) {
  if (!expireTime) return ''
  const remaining = new Date(expireTime).getTime() - new Date().getTime()
  if (remaining <= 0) return 'expired'
  if (remaining <= 60 * 60 * 1000) return 'expiring-soon'
  return ''
}

function formatSize(bytes) {
  if (!bytes) return '0 B'
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(1024))
  return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i]
}

function formatDate(dateString) {
  if (!dateString) return '-'
  return new Date(dateString).toLocaleString('zh-CN')
}

function formatRemainingTime(expireTime) {
  if (!expireTime) return '-'
  const remaining = new Date(expireTime).getTime() - new Date().getTime()
  if (remaining <= 0) return '已过期'
  
  const hours = Math.floor(remaining / (1000 * 60 * 60))
  const minutes = Math.floor((remaining % (1000 * 60 * 60)) / (1000 * 60))
  
  if (hours > 0) {
    return `${hours}小时${minutes}分钟`
  }
  return `${minutes}分钟`
}

// 生命周期
onMounted(() => {
  loadCacheList()
  loadStats()
  loadCacheConfig()
})
</script>

<style scoped>
.cache-container {
  padding: 24px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 8px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.page-description {
  color: #6b7280;
  margin: 0;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.stat-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.stat-icon.total { background: #3b82f6; }
.stat-icon.active { background: #10b981; }
.stat-icon.expired { background: #f59e0b; }
.stat-icon.hit-rate { background: #8b5cf6; }

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
}

.stat-label {
  font-size: 14px;
  color: #6b7280;
}

.cache-tabs {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.filter-section {
  margin-bottom: 20px;
  padding: 16px;
  background: #f9fafb;
  border-radius: 8px;
}

.table-section {
  margin-top: 16px;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.batch-actions {
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  background: white;
  border-radius: 8px;
  padding: 16px 24px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  display: flex;
  align-items: center;
  gap: 16px;
  z-index: 1000;
}

.batch-info {
  font-weight: 500;
  color: #374151;
}

.batch-buttons {
  display: flex;
  gap: 8px;
}

.statistics-section {
  margin-top: 16px;
}

.stats-filter {
  margin-bottom: 20px;
  padding: 16px;
  background: #f9fafb;
  border-radius: 8px;
}

.charts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 20px;
  margin-bottom: 24px;
}

.chart-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.chart-card h4 {
  margin: 0 0 16px 0;
  color: #1f2937;
}

.chart-placeholder {
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f9fafb;
  border-radius: 8px;
  color: #6b7280;
}

.detailed-stats {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.detailed-stats h4 {
  margin: 0 0 16px 0;
  color: #1f2937;
}

.config-section {
  margin-top: 16px;
}

.cache-key {
  font-family: monospace;
  font-size: 12px;
  color: #374151;
  word-break: break-all;
}

.expired {
  color: #ef4444;
  font-weight: 600;
}

.expiring-soon {
  color: #f59e0b;
  font-weight: 600;
}

.cache-detail {
  max-height: 500px;
  overflow-y: auto;
}

.detail-section {
  margin-bottom: 24px;
}

.detail-section h4 {
  margin: 0 0 12px 0;
  color: #1f2937;
  font-size: 16px;
  font-weight: 600;
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 8px;
}

.detail-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.detail-item label {
  font-weight: 500;
  color: #374151;
  min-width: 80px;
}

.cache-content {
  margin-top: 12px;
}

.form-help {
  font-size: 12px;
  color: #6b7280;
  margin-left: 8px;
}
</style>
