<template>
  <div class="settings-container">
    <div class="page-header">
      <div class="header-left">
        <h1 class="page-title">
          <SettingsIcon :size="24" />
          系统设置
        </h1>
        <p class="page-description">管理系统配置和参数</p>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="refreshData">
          <template #icon>
            <RefreshCwIcon :size="16" />
          </template>
          刷新
        </el-button>
        <el-button type="success" @click="showCreateDialog">
          <template #icon>
            <PlusIcon :size="16" />
          </template>
          新增配置
        </el-button>
      </div>
    </div>

    <!-- 配置统计 -->
    <div class="stats-grid">
      <div class="stat-card">
        <div class="stat-icon total">
          <SettingsIcon :size="24" />
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stats.total || 0 }}</div>
          <div class="stat-label">总配置数</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon enabled">
          <CheckCircleIcon :size="24" />
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stats.enabled || 0 }}</div>
          <div class="stat-label">已启用</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon disabled">
          <XCircleIcon :size="24" />
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stats.disabled || 0 }}</div>
          <div class="stat-label">已禁用</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon wechat">
          <CloudIcon :size="24" />
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ connectionStatus }}</div>
          <div class="stat-label">API状态</div>
        </div>
      </div>
    </div>

    <!-- 微信配置卡片 -->
    <div class="config-section">
      <div class="section-header">
        <h3>微信配置</h3>
        <el-button type="primary" @click="showWechatConfig">
          <template #icon>
            <EditIcon :size="16" />
          </template>
          修改配置
        </el-button>
      </div>
      <div class="wechat-config-card">
        <div class="config-item">
          <label>AppID:</label>
          <span>{{ maskedAppId }}</span>
        </div>
        <div class="config-item">
          <label>云环境ID:</label>
          <span>{{ wechatConfig.env || '未配置' }}</span>
        </div>
        <div class="config-item">
          <label>云函数名称:</label>
          <span>{{ wechatConfig.functionName || '未配置' }}</span>
        </div>
        <div class="config-item">
          <label>连接状态:</label>
          <el-tag :type="systemStore.apiConnected ? 'success' : 'danger'">
            {{ systemStore.apiConnected ? '已连接' : '未连接' }}
          </el-tag>
        </div>
      </div>
    </div>

    <!-- Access Token 状态监控 -->
    <TokenStatus />

    <!-- 筛选和搜索 -->
    <div class="filter-section">
      <el-form :model="filters" inline class="filter-form">
        <el-form-item label="分类">
          <el-select v-model="filters.category" placeholder="选择分类" style="width: 120px">
            <el-option label="全部" value="all" />
            <el-option label="通用" value="general" />
            <el-option label="系统" value="system" />
            <el-option label="用户" value="user" />
            <el-option label="安全" value="security" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="filters.enable" placeholder="启用状态" style="width: 120px">
            <el-option label="全部" value="all" />
            <el-option label="已启用" value="true" />
            <el-option label="已禁用" value="false" />
          </el-select>
        </el-form-item>
        <el-form-item label="搜索">
          <el-input
            v-model="filters.keyword"
            placeholder="搜索配置键或描述"
            style="width: 250px"
            @keyup.enter="handleSearch"
          >
            <template #prefix>
              <SearchIcon :size="16" />
            </template>
          </el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <template #icon>
              <SearchIcon :size="16" />
            </template>
            搜索
          </el-button>
          <el-button @click="resetFilters">
            <template #icon>
              <RotateCcwIcon :size="16" />
            </template>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 配置列表 -->
    <div class="table-section">
      <el-table
        v-loading="loading"
        :data="configList"
        stripe
        style="width: 100%"
      >
        <el-table-column prop="key" label="配置键" min-width="200" />
        <el-table-column prop="value" label="配置值" min-width="200">
          <template #default="{ row }">
            <div class="config-value">
              <span v-if="row.dataType === 'password'">******</span>
              <pre v-else-if="isJsonValue(row.value)" class="json-value">{{ formatJsonValue(row.value) }}</pre>
              <span v-else class="text-value">{{ row.value }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="description" label="描述" min-width="200" />
        <el-table-column prop="category" label="分类" width="100">
          <template #default="{ row }">
            <el-tag size="small">{{ getCategoryText(row.category) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="dataType" label="类型" width="100">
          <template #default="{ row }">
            <el-tag type="info" size="small">{{ row.dataType }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="enable" label="状态" width="80">
          <template #default="{ row }">
            <el-switch
              v-model="row.enable"
              @change="toggleConfigStatus(row)"
            />
          </template>
        </el-table-column>
        <el-table-column prop="updateTime" label="更新时间" width="180">
          <template #default="{ row }">
            {{ formatDate(row.updateTime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="{ row }">
            <el-button
              type="primary"
              size="small"
              @click="editConfig(row)"
            >
              <template #icon>
                <EditIcon :size="14" />
              </template>
              编辑
            </el-button>
            <el-button
              type="danger"
              size="small"
              @click="deleteConfig(row)"
            >
              <template #icon>
                <TrashIcon :size="14" />
              </template>
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.pageSize"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 配置编辑对话框 -->
    <el-dialog
      v-model="configDialogVisible"
      :title="isEditing ? '编辑配置' : '新增配置'"
      width="600px"
      @close="resetConfigForm"
    >
      <el-form
        ref="configFormRef"
        :model="configForm"
        :rules="configRules"
        label-width="100px"
      >
        <el-form-item label="配置键" prop="key">
          <el-input
            v-model="configForm.key"
            :disabled="isEditing"
            placeholder="请输入配置键"
          />
        </el-form-item>
        <el-form-item label="配置值" prop="value">
          <el-input
            v-model="configForm.value"
            :type="configForm.dataType === 'password' ? 'password' : 'text'"
            placeholder="请输入配置值"
          />
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input
            v-model="configForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入配置描述"
          />
        </el-form-item>
        <el-form-item label="分类" prop="category">
          <el-select v-model="configForm.category" style="width: 100%">
            <el-option label="通用" value="general" />
            <el-option label="系统" value="system" />
            <el-option label="用户" value="user" />
            <el-option label="安全" value="security" />
          </el-select>
        </el-form-item>
        <el-form-item label="数据类型" prop="dataType">
          <el-select v-model="configForm.dataType" style="width: 100%">
            <el-option label="字符串" value="string" />
            <el-option label="数字" value="number" />
            <el-option label="布尔值" value="boolean" />
            <el-option label="密码" value="password" />
            <el-option label="JSON" value="json" />
          </el-select>
        </el-form-item>
        <el-form-item label="启用状态">
          <el-switch v-model="configForm.enable" />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="configDialogVisible = false">取消</el-button>
        <el-button
          type="primary"
          :loading="saving"
          @click="saveConfig"
        >
          保存
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Settings as SettingsIcon,
  CheckCircle as CheckCircleIcon,
  XCircle as XCircleIcon,
  Cloud as CloudIcon,
  RefreshCw as RefreshCwIcon,
  Plus as PlusIcon,
  Edit as EditIcon,
  Search as SearchIcon,
  RotateCcw as RotateCcwIcon,
  Trash as TrashIcon
} from 'lucide-vue-next'
import { useSystemStore } from '@/stores/system.js'
import { callCloudFunction } from '@/api/wechat-api.js'
import { getWechatConfig } from '@/api/wechat-api.js'
import TokenStatus from '@/components/TokenStatus.vue'

const router = useRouter()
const systemStore = useSystemStore()

// 注册组件
defineOptions({
  components: {
    TokenStatus
  }
})

// 响应式数据
const loading = ref(false)
const saving = ref(false)
const configList = ref([])
const stats = ref({})
const configDialogVisible = ref(false)
const isEditing = ref(false)
const configFormRef = ref()

const filters = reactive({
  category: 'all',
  enable: 'all',
  keyword: ''
})

const pagination = reactive({
  page: 1,
  pageSize: 20,
  total: 0
})

const configForm = reactive({
  key: '',
  value: '',
  description: '',
  category: 'general',
  dataType: 'string',
  enable: true
})

const configRules = {
  key: [
    { required: true, message: '请输入配置键', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  value: [
    { required: true, message: '请输入配置值', trigger: 'blur' }
  ],
  description: [
    { required: true, message: '请输入配置描述', trigger: 'blur' }
  ],
  category: [
    { required: true, message: '请选择分类', trigger: 'change' }
  ],
  dataType: [
    { required: true, message: '请选择数据类型', trigger: 'change' }
  ]
}

// 计算属性
const wechatConfig = computed(() => getWechatConfig())

const maskedAppId = computed(() => {
  const appId = wechatConfig.value.appId
  if (!appId) return '未配置'
  return appId.slice(0, 4) + '****' + appId.slice(-4)
})

const connectionStatus = computed(() => {
  return systemStore.apiConnected ? '已连接' : '未连接'
})

// 方法
async function loadConfigList() {
  try {
    loading.value = true
    
    const result = await callCloudFunction('getAllConfigsAdmin', {
      ...filters,
      page: pagination.page,
      pageSize: pagination.pageSize,
      sortBy: 'key',
      sortOrder: 'asc'
    })

    if (result.success) {
      configList.value = result.data || []
      pagination.total = result.pagination?.total || 0
    } else {
      ElMessage.error(result.message || '获取配置列表失败')
    }
  } catch (error) {
    console.error('获取配置列表失败:', error)
    ElMessage.error('获取配置列表失败')
  } finally {
    loading.value = false
  }
}

async function loadStats() {
  try {
    const result = await callCloudFunction('getConfigStats')
    if (result.success) {
      stats.value = result.data || {}
    }
  } catch (error) {
    console.error('获取配置统计失败:', error)
  }
}

function handleSearch() {
  pagination.page = 1
  loadConfigList()
}

function resetFilters() {
  filters.category = 'all'
  filters.enable = 'all'
  filters.keyword = ''
  pagination.page = 1
  loadConfigList()
}

function handleSizeChange(size) {
  pagination.pageSize = size
  pagination.page = 1
  loadConfigList()
}

function handleCurrentChange(page) {
  pagination.page = page
  loadConfigList()
}

async function refreshData() {
  await Promise.all([loadConfigList(), loadStats()])
  ElMessage.success('数据已刷新')
}

function showWechatConfig() {
  router.push('/setup')
}

function showCreateDialog() {
  isEditing.value = false
  resetConfigForm()
  configDialogVisible.value = true
}

function editConfig(config) {
  isEditing.value = true
  Object.assign(configForm, config)
  configDialogVisible.value = true
}

function resetConfigForm() {
  Object.assign(configForm, {
    key: '',
    value: '',
    description: '',
    category: 'general',
    dataType: 'string',
    enable: true
  })
  configFormRef.value?.clearValidate()
}

async function saveConfig() {
  try {
    const valid = await configFormRef.value.validate()
    if (!valid) return

    saving.value = true

    const apiType = isEditing.value ? 'updateConfig' : 'createConfig'
    const result = await callCloudFunction(apiType, configForm)

    if (result.success) {
      ElMessage.success(isEditing.value ? '配置更新成功' : '配置创建成功')
      configDialogVisible.value = false
      loadConfigList()
    } else {
      ElMessage.error(result.message || '保存配置失败')
    }
  } catch (error) {
    console.error('保存配置失败:', error)
    ElMessage.error('保存配置失败')
  } finally {
    saving.value = false
  }
}

async function toggleConfigStatus(config) {
  try {
    const result = await callCloudFunction('updateConfig', {
      key: config.key,
      enable: config.enable
    })

    if (result.success) {
      ElMessage.success('状态更新成功')
    } else {
      // 恢复原状态
      config.enable = !config.enable
      ElMessage.error(result.message || '状态更新失败')
    }
  } catch (error) {
    // 恢复原状态
    config.enable = !config.enable
    console.error('状态更新失败:', error)
    ElMessage.error('状态更新失败')
  }
}

async function deleteConfig(config) {
  try {
    await ElMessageBox.confirm(
      `确定要删除配置 "${config.key}" 吗？此操作不可恢复。`,
      '删除配置',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const result = await callCloudFunction('deleteConfig', {
      key: config.key
    })

    if (result.success) {
      ElMessage.success('配置删除成功')
      loadConfigList()
    } else {
      ElMessage.error(result.message || '配置删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除配置失败:', error)
      ElMessage.error('删除配置失败')
    }
  }
}

function getCategoryText(category) {
  const texts = {
    general: '通用',
    system: '系统',
    user: '用户',
    security: '安全'
  }
  return texts[category] || category
}

function formatDate(dateString) {
  if (!dateString) return '-'
  return new Date(dateString).toLocaleString('zh-CN')
}

function isJsonValue(value) {
  if (typeof value === 'object' && value !== null) {
    return true
  }
  if (typeof value === 'string') {
    try {
      const parsed = JSON.parse(value)
      return typeof parsed === 'object' && parsed !== null
    } catch {
      return false
    }
  }
  return false
}

function formatJsonValue(value) {
  try {
    if (typeof value === 'object' && value !== null) {
      return JSON.stringify(value, null, 2)
    }
    if (typeof value === 'string') {
      const parsed = JSON.parse(value)
      return JSON.stringify(parsed, null, 2)
    }
    return String(value)
  } catch {
    return String(value)
  }
}

// 生命周期
onMounted(() => {
  loadConfigList()
  loadStats()
})
</script>

<style scoped>
.settings-container {
  padding: 24px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 8px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.page-description {
  color: #6b7280;
  margin: 0;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.stat-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.stat-icon.total { background: #3b82f6; }
.stat-icon.enabled { background: #10b981; }
.stat-icon.disabled { background: #ef4444; }
.stat-icon.wechat { background: #8b5cf6; }

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
}

.stat-label {
  font-size: 14px;
  color: #6b7280;
}

.config-section {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.section-header h3 {
  margin: 0;
  color: #1f2937;
}

.wechat-config-card {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
}

.config-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.config-item label {
  font-weight: 500;
  color: #374151;
  min-width: 100px;
}

.filter-section {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.table-section {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.config-value {
  max-width: 300px;
  word-break: break-all;
}

.json-value {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  padding: 8px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  line-height: 1.4;
  color: #495057;
  white-space: pre-wrap;
  max-height: 200px;
  overflow-y: auto;
  margin: 0;
}

.text-value {
  display: inline-block;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}
</style>
