<template>
  <div class="fishing-container">
    <div class="page-header">
      <div class="header-left">
        <h1 class="page-title">
          <FishIcon :size="24" />
          摸鱼状态管理
        </h1>
        <p class="page-description">管理用户摸鱼状态和统计</p>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="refreshData">
          <template #icon>
            <RefreshCwIcon :size="16" />
          </template>
          刷新
        </el-button>
        <el-button type="warning" @click="cleanupExpiredStatus">
          <template #icon>
            <TrashIcon :size="16" />
          </template>
          清理过期状态
        </el-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-grid">
      <div class="stat-card">
        <div class="stat-icon total">
          <FishIcon :size="24" />
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stats.totalFishing || 0 }}</div>
          <div class="stat-label">总摸鱼次数</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon active">
          <PlayIcon :size="24" />
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stats.activeFishing || 0 }}</div>
          <div class="stat-label">正在摸鱼</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon today">
          <CalendarIcon :size="24" />
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stats.todayFishing || 0 }}</div>
          <div class="stat-label">今日摸鱼</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon duration">
          <ClockIcon :size="24" />
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stats.avgDuration || 0 }}分</div>
          <div class="stat-label">平均时长</div>
        </div>
      </div>
    </div>

    <!-- 功能标签页 -->
    <el-tabs v-model="activeTab" class="fishing-tabs">
      <el-tab-pane label="摸鱼记录" name="records">
        <!-- 筛选和搜索 -->
        <div class="filter-section">
          <el-form :model="filters" inline class="filter-form">
            <el-form-item label="用户">
              <el-input
                v-model="filters.userId"
                placeholder="输入用户ID"
                style="width: 200px"
              >
                <template #prefix>
                  <UserIcon :size="16" />
                </template>
              </el-input>
            </el-form-item>
            <el-form-item label="状态">
              <el-select v-model="filters.status" placeholder="选择状态" style="width: 120px">
                <el-option label="全部" value="all" />
                <el-option label="进行中" value="active" />
                <el-option label="已结束" value="completed" />
                <el-option label="已过期" value="expired" />
              </el-select>
            </el-form-item>
            <el-form-item label="日期范围">
              <el-date-picker
                v-model="filters.dateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                style="width: 240px"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="loadFishingRecords">
                <template #icon>
                  <SearchIcon :size="16" />
                </template>
                搜索
              </el-button>
              <el-button @click="resetFilters">
                <template #icon>
                  <RotateCcwIcon :size="16" />
                </template>
                重置
              </el-button>
            </el-form-item>
          </el-form>
        </div>

        <!-- 摸鱼记录表格 -->
        <div class="table-section">
          <el-table
            v-loading="loading"
            :data="fishingRecords"
            stripe
            style="width: 100%"
          >
            <el-table-column prop="userId" label="用户ID" width="200" />
            <el-table-column prop="nickname" label="昵称" min-width="150" />
            <el-table-column prop="status" label="状态" width="100">
              <template #default="{ row }">
                <el-tag :type="getStatusColor(row.status)" size="small">
                  {{ getStatusText(row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="reason" label="摸鱼理由" min-width="200" />
            <el-table-column prop="startTime" label="开始时间" width="180">
              <template #default="{ row }">
                {{ formatDate(row.startTime) }}
              </template>
            </el-table-column>
            <el-table-column prop="endTime" label="结束时间" width="180">
              <template #default="{ row }">
                {{ formatDate(row.endTime) }}
              </template>
            </el-table-column>
            <el-table-column prop="duration" label="时长" width="100">
              <template #default="{ row }">
                <span v-if="row.duration">{{ formatDuration(row.duration) }}</span>
                <span v-else-if="row.status === 'active'" class="active-duration">
                  {{ calculateActiveDuration(row.startTime) }}
                </span>
                <span v-else>-</span>
              </template>
            </el-table-column>
            <el-table-column prop="location" label="位置" min-width="150">
              <template #default="{ row }">
                <div v-if="row.location" class="location-info">
                  <div>{{ row.location.name || '未知位置' }}</div>
                  <div class="location-detail">
                    {{ row.location.address || '-' }}
                  </div>
                </div>
                <span v-else>-</span>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="120" fixed="right">
              <template #default="{ row }">
                <el-button
                  v-if="row.status === 'active'"
                  type="warning"
                  size="small"
                  @click="endFishing(row)"
                >
                  结束摸鱼
                </el-button>
                <el-button
                  type="primary"
                  size="small"
                  @click="viewDetail(row)"
                >
                  查看详情
                </el-button>
              </template>
            </el-table-column>
          </el-table>

          <!-- 分页 -->
          <div class="pagination-wrapper">
            <el-pagination
              v-model:current-page="pagination.page"
              v-model:page-size="pagination.pageSize"
              :total="pagination.total"
              :page-sizes="[10, 20, 50, 100]"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            />
          </div>
        </div>
      </el-tab-pane>

      <el-tab-pane label="摸鱼统计" name="statistics">
        <div class="statistics-section">
          <!-- 统计筛选 -->
          <div class="stats-filter">
            <el-form inline>
              <el-form-item label="统计周期">
                <el-select v-model="statsPeriod" @change="loadStatistics">
                  <el-option label="最近7天" value="7d" />
                  <el-option label="最近30天" value="30d" />
                  <el-option label="最近90天" value="90d" />
                </el-select>
              </el-form-item>
            </el-form>
          </div>

          <!-- 统计图表 -->
          <div class="charts-grid">
            <div class="chart-card">
              <h4>摸鱼趋势</h4>
              <div class="chart-placeholder">
                <!-- TODO: 集成图表库显示摸鱼趋势 -->
                <p>摸鱼趋势图表</p>
              </div>
            </div>
            <div class="chart-card">
              <h4>摸鱼时长分布</h4>
              <div class="chart-placeholder">
                <!-- TODO: 集成图表库显示时长分布 -->
                <p>时长分布图</p>
              </div>
            </div>
          </div>

          <!-- 摸鱼排行榜 -->
          <div class="leaderboard-section">
            <h4>摸鱼排行榜</h4>
            <el-table
              v-loading="leaderboardLoading"
              :data="fishingLeaderboard"
              stripe
              style="width: 100%"
            >
              <el-table-column label="排名" width="80">
                <template #default="{ $index }">
                  <div class="rank-badge" :class="getRankClass($index)">
                    {{ $index + 1 }}
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="userId" label="用户ID" width="200" />
              <el-table-column prop="nickname" label="昵称" min-width="150" />
              <el-table-column prop="totalCount" label="摸鱼次数" width="120" />
              <el-table-column prop="totalDuration" label="总时长" width="120">
                <template #default="{ row }">
                  {{ formatDuration(row.totalDuration) }}
                </template>
              </el-table-column>
              <el-table-column prop="avgDuration" label="平均时长" width="120">
                <template #default="{ row }">
                  {{ formatDuration(row.avgDuration) }}
                </template>
              </el-table-column>
              <el-table-column prop="lastFishing" label="最后摸鱼" width="180">
                <template #default="{ row }">
                  {{ formatDate(row.lastFishing) }}
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>

    <!-- 详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="摸鱼详情"
      width="600px"
    >
      <div v-if="currentRecord" class="fishing-detail">
        <div class="detail-section">
          <h4>基本信息</h4>
          <div class="detail-grid">
            <div class="detail-item">
              <label>用户:</label>
              <span>{{ currentRecord.nickname || currentRecord.userId }}</span>
            </div>
            <div class="detail-item">
              <label>状态:</label>
              <el-tag :type="getStatusColor(currentRecord.status)" size="small">
                {{ getStatusText(currentRecord.status) }}
              </el-tag>
            </div>
            <div class="detail-item">
              <label>理由:</label>
              <span>{{ currentRecord.reason || '-' }}</span>
            </div>
            <div class="detail-item">
              <label>时长:</label>
              <span>{{ formatDuration(currentRecord.duration) }}</span>
            </div>
          </div>
        </div>

        <div v-if="currentRecord.location" class="detail-section">
          <h4>位置信息</h4>
          <div class="detail-grid">
            <div class="detail-item">
              <label>位置名称:</label>
              <span>{{ currentRecord.location.name || '-' }}</span>
            </div>
            <div class="detail-item">
              <label>详细地址:</label>
              <span>{{ currentRecord.location.address || '-' }}</span>
            </div>
            <div class="detail-item">
              <label>坐标:</label>
              <span>
                {{ currentRecord.location.latitude }}, {{ currentRecord.location.longitude }}
              </span>
            </div>
          </div>
        </div>

        <div class="detail-section">
          <h4>时间信息</h4>
          <div class="detail-grid">
            <div class="detail-item">
              <label>开始时间:</label>
              <span>{{ formatDate(currentRecord.startTime) }}</span>
            </div>
            <div class="detail-item">
              <label>结束时间:</label>
              <span>{{ formatDate(currentRecord.endTime) }}</span>
            </div>
            <div class="detail-item">
              <label>创建时间:</label>
              <span>{{ formatDate(currentRecord.createTime) }}</span>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Fish as FishIcon,
  Play as PlayIcon,
  Calendar as CalendarIcon,
  Clock as ClockIcon,
  RefreshCw as RefreshCwIcon,
  Trash as TrashIcon,
  User as UserIcon,
  Search as SearchIcon,
  RotateCcw as RotateCcwIcon
} from 'lucide-vue-next'
import { callCloudFunction } from '@/api/wechat-api.js'

// 响应式数据
const loading = ref(false)
const leaderboardLoading = ref(false)
const activeTab = ref('records')
const fishingRecords = ref([])
const fishingLeaderboard = ref([])
const stats = ref({})
const detailDialogVisible = ref(false)
const currentRecord = ref(null)
const statsPeriod = ref('30d')

const filters = reactive({
  userId: '',
  status: 'all',
  dateRange: []
})

const pagination = reactive({
  page: 1,
  pageSize: 20,
  total: 0
})

// 方法
async function loadFishingRecords() {
  try {
    loading.value = true
    
    const params = {
      page: pagination.page,
      pageSize: pagination.pageSize
    }
    
    if (filters.userId.trim()) {
      params.userId = filters.userId.trim()
    }
    if (filters.status !== 'all') {
      params.status = filters.status
    }
    if (filters.dateRange && filters.dateRange.length === 2) {
      params.startDate = filters.dateRange[0].toISOString()
      params.endDate = filters.dateRange[1].toISOString()
    }
    
    const result = await callCloudFunction('getFishingRecordsAdmin', params)

    if (result.success) {
      fishingRecords.value = result.data || []
      pagination.total = result.pagination?.total || 0
    } else {
      ElMessage.error(result.message || '获取摸鱼记录失败')
    }
  } catch (error) {
    console.error('获取摸鱼记录失败:', error)
    ElMessage.error('获取摸鱼记录失败')
  } finally {
    loading.value = false
  }
}

async function loadLeaderboard() {
  try {
    leaderboardLoading.value = true
    
    const result = await callCloudFunction('getFishingLeaderboard', {
      limit: 50,
      period: statsPeriod.value
    })

    if (result.success) {
      fishingLeaderboard.value = result.data || []
    } else {
      ElMessage.error(result.message || '获取排行榜失败')
    }
  } catch (error) {
    console.error('获取排行榜失败:', error)
    ElMessage.error('获取排行榜失败')
  } finally {
    leaderboardLoading.value = false
  }
}

async function loadStats() {
  try {
    const result = await callCloudFunction('getFishingStatsAdmin', { 
      period: statsPeriod.value 
    })
    if (result.success) {
      stats.value = result.data || {}
    }
  } catch (error) {
    console.error('获取摸鱼统计失败:', error)
  }
}

async function loadStatistics() {
  await Promise.all([loadStats(), loadLeaderboard()])
}

function resetFilters() {
  filters.userId = ''
  filters.status = 'all'
  filters.dateRange = []
  pagination.page = 1
  loadFishingRecords()
}

function handleSizeChange(size) {
  pagination.pageSize = size
  pagination.page = 1
  loadFishingRecords()
}

function handleCurrentChange(page) {
  pagination.page = page
  loadFishingRecords()
}

async function refreshData() {
  await Promise.all([
    loadFishingRecords(),
    loadStats(),
    loadLeaderboard()
  ])
  ElMessage.success('数据已刷新')
}

async function cleanupExpiredStatus() {
  try {
    await ElMessageBox.confirm(
      '确定要清理所有过期的摸鱼状态吗？',
      '清理过期状态',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const result = await callCloudFunction('cleanupExpiredFishingStatus')

    if (result.success) {
      ElMessage.success(`清理完成，共清理 ${result.data?.cleanedCount || 0} 条过期记录`)
      refreshData()
    } else {
      ElMessage.error(result.message || '清理失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('清理过期状态失败:', error)
      ElMessage.error('清理失败')
    }
  }
}

async function endFishing(record) {
  try {
    await ElMessageBox.confirm(
      `确定要结束用户 "${record.nickname || record.userId}" 的摸鱼状态吗？`,
      '结束摸鱼',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const result = await callCloudFunction('endFishingStatusAdmin', {
      userId: record.userId,
      recordId: record._id
    })

    if (result.success) {
      ElMessage.success('摸鱼状态已结束')
      loadFishingRecords()
    } else {
      ElMessage.error(result.message || '操作失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('结束摸鱼失败:', error)
      ElMessage.error('操作失败')
    }
  }
}

function viewDetail(record) {
  currentRecord.value = record
  detailDialogVisible.value = true
}

// 辅助函数
function getStatusColor(status) {
  const colors = {
    active: 'success',
    completed: 'info',
    expired: 'warning'
  }
  return colors[status] || 'info'
}

function getStatusText(status) {
  const texts = {
    active: '进行中',
    completed: '已结束',
    expired: '已过期'
  }
  return texts[status] || status
}

function getRankClass(index) {
  if (index === 0) return 'rank-gold'
  if (index === 1) return 'rank-silver'
  if (index === 2) return 'rank-bronze'
  return 'rank-normal'
}

function formatDate(dateString) {
  if (!dateString) return '-'
  return new Date(dateString).toLocaleString('zh-CN')
}

function formatDuration(minutes) {
  if (!minutes) return '-'
  const hours = Math.floor(minutes / 60)
  const mins = minutes % 60
  if (hours > 0) {
    return `${hours}小时${mins}分钟`
  }
  return `${mins}分钟`
}

function calculateActiveDuration(startTime) {
  if (!startTime) return '-'
  const now = new Date()
  const start = new Date(startTime)
  const diff = Math.floor((now - start) / (1000 * 60))
  return formatDuration(diff)
}

// 生命周期
onMounted(() => {
  loadFishingRecords()
  loadStats()
  loadLeaderboard()
})
</script>

<style scoped>
.fishing-container {
  padding: 24px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 8px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.page-description {
  color: #6b7280;
  margin: 0;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.stat-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.stat-icon.total { background: #3b82f6; }
.stat-icon.active { background: #10b981; }
.stat-icon.today { background: #f59e0b; }
.stat-icon.duration { background: #8b5cf6; }

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
}

.stat-label {
  font-size: 14px;
  color: #6b7280;
}

.fishing-tabs {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.filter-section {
  margin-bottom: 20px;
  padding: 16px;
  background: #f9fafb;
  border-radius: 8px;
}

.table-section {
  margin-top: 16px;
}

.statistics-section {
  margin-top: 16px;
}

.stats-filter {
  margin-bottom: 20px;
  padding: 16px;
  background: #f9fafb;
  border-radius: 8px;
}

.charts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 20px;
  margin-bottom: 24px;
}

.chart-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.chart-card h4 {
  margin: 0 0 16px 0;
  color: #1f2937;
}

.chart-placeholder {
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f9fafb;
  border-radius: 8px;
  color: #6b7280;
}

.leaderboard-section {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.leaderboard-section h4 {
  margin: 0 0 16px 0;
  color: #1f2937;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.rank-badge {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  color: white;
}

.rank-gold { background: #fbbf24; }
.rank-silver { background: #9ca3af; }
.rank-bronze { background: #d97706; }
.rank-normal { background: #6b7280; }

.active-duration {
  color: #10b981;
  font-weight: 600;
}

.location-info {
  font-size: 14px;
}

.location-detail {
  color: #6b7280;
  font-size: 12px;
}

.fishing-detail {
  max-height: 500px;
  overflow-y: auto;
}

.detail-section {
  margin-bottom: 24px;
}

.detail-section h4 {
  margin: 0 0 12px 0;
  color: #1f2937;
  font-size: 16px;
  font-weight: 600;
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 8px;
}

.detail-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.detail-item label {
  font-weight: 500;
  color: #374151;
  min-width: 80px;
}
</style>
