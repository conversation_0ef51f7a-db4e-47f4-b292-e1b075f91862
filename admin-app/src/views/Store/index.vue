<template>
  <div class="store-container">
    <div class="page-header">
      <div class="header-left">
        <h1 class="page-title">
          <ShoppingBagIcon :size="24" />
          商店管理
        </h1>
        <p class="page-description">管理商品和兑换码</p>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="refreshData">
          <template #icon>
            <RefreshCwIcon :size="16" />
          </template>
          刷新
        </el-button>
        <el-button type="success" @click="showCreateItemDialog">
          <template #icon>
            <PlusIcon :size="16" />
          </template>
          新增商品
        </el-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-grid">
      <div class="stat-card">
        <div class="stat-icon total">
          <ShoppingBagIcon :size="24" />
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stats.total || 0 }}</div>
          <div class="stat-label">总商品数</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon active">
          <CheckCircleIcon :size="24" />
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stats.active || 0 }}</div>
          <div class="stat-label">上架商品</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon sales">
          <TrendingUpIcon :size="24" />
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stats.totalSales || 0 }}</div>
          <div class="stat-label">总销量</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon revenue">
          <CoinsIcon :size="24" />
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stats.totalRevenue || 0 }}</div>
          <div class="stat-label">总收入</div>
        </div>
      </div>
    </div>

    <!-- 功能标签页 -->
    <el-tabs v-model="activeTab" class="store-tabs">
      <el-tab-pane label="商品管理" name="items">
        <!-- 筛选和搜索 -->
        <div class="filter-section">
          <el-form :model="itemFilters" inline class="filter-form">
            <el-form-item label="分类">
              <el-select v-model="itemFilters.category" placeholder="选择分类" style="width: 120px">
                <el-option label="全部" value="all" />
                <el-option label="虚拟商品" value="virtual" />
                <el-option label="实物商品" value="physical" />
                <el-option label="服务" value="service" />
              </el-select>
            </el-form-item>
            <el-form-item label="状态">
              <el-select v-model="itemFilters.status" placeholder="商品状态" style="width: 120px">
                <el-option label="全部" value="all" />
                <el-option label="上架" value="active" />
                <el-option label="下架" value="inactive" />
                <el-option label="缺货" value="out_of_stock" />
              </el-select>
            </el-form-item>
            <el-form-item label="排序">
              <el-select v-model="itemFilters.sortBy" placeholder="排序字段" style="width: 120px">
                <el-option label="排序顺序" value="sortOrder" />
                <el-option label="创建时间" value="createTime" />
                <el-option label="价格" value="price" />
                <el-option label="销量" value="soldCount" />
                <el-option label="库存" value="stock" />
              </el-select>
            </el-form-item>
            <el-form-item label="方向">
              <el-select v-model="itemFilters.sortOrder" style="width: 80px">
                <el-option label="升序" value="asc" />
                <el-option label="降序" value="desc" />
              </el-select>
            </el-form-item>
            <el-form-item label="搜索">
              <el-input
                v-model="itemFilters.keyword"
                placeholder="搜索商品名称"
                style="width: 200px"
                @keyup.enter="loadStoreItems"
              >
                <template #prefix>
                  <SearchIcon :size="16" />
                </template>
              </el-input>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="loadStoreItems">
                <template #icon>
                  <SearchIcon :size="16" />
                </template>
                搜索
              </el-button>
              <el-button @click="resetItemFilters">
                <template #icon>
                  <RotateCcwIcon :size="16" />
                </template>
                重置
              </el-button>
            </el-form-item>
          </el-form>
        </div>

        <!-- 商品列表 -->
        <div class="table-section">
          <el-table
            v-loading="itemsLoading"
            :data="storeItems"
            stripe
            style="width: 100%"
          >
            <el-table-column prop="icon" label="图标" width="80">
              <template #default="{ row }">
                <div class="item-icon">
                  <component v-if="row.icon" :is="row.icon" :size="24" />
                  <ShoppingBagIcon v-else :size="24" />
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="name" label="商品名称" min-width="200" />
            <el-table-column prop="category" label="分类" width="100">
              <template #default="{ row }">
                <el-tag size="small">{{ getCategoryText(row.category) }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="price" label="价格" width="100">
              <template #default="{ row }">
                <span class="price-text">{{ row.price }}积分</span>
              </template>
            </el-table-column>
            <el-table-column prop="stock" label="库存" width="80" />
            <el-table-column prop="soldCount" label="销量" width="80" />
            <el-table-column prop="status" label="状态" width="100">
              <template #default="{ row }">
                <el-tag :type="getStatusColor(row.status)" size="small">
                  {{ getStatusText(row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="createTime" label="创建时间" width="180">
              <template #default="{ row }">
                {{ formatDate(row.createTime) }}
              </template>
            </el-table-column>
            <el-table-column label="操作" width="200" fixed="right">
              <template #default="{ row }">
                <el-button
                  type="primary"
                  size="small"
                  @click="editItem(row)"
                >
                  <template #icon>
                    <EditIcon :size="14" />
                  </template>
                  编辑
                </el-button>
                <el-button
                  type="danger"
                  size="small"
                  @click="deleteItem(row)"
                >
                  <template #icon>
                    <TrashIcon :size="14" />
                  </template>
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>

          <!-- 分页 -->
          <div class="pagination-wrapper">
            <el-pagination
              v-model:current-page="itemPagination.page"
              v-model:page-size="itemPagination.pageSize"
              :total="itemPagination.total"
              :page-sizes="[10, 20, 50, 100]"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="handleItemSizeChange"
              @current-change="handleItemCurrentChange"
            />
          </div>
        </div>
      </el-tab-pane>

      <el-tab-pane label="兑换码管理" name="codes">
        <div class="codes-section">
          <div class="codes-header">
            <el-button type="success" @click="showCreateCodeDialog">
              <template #icon>
                <PlusIcon :size="16" />
              </template>
              创建兑换码
            </el-button>
          </div>

          <!-- 兑换码列表 -->
          <el-table
            v-loading="codesLoading"
            :data="redemptionCodes"
            stripe
            style="width: 100%"
          >
            <el-table-column prop="code" label="兑换码" width="150" />
            <el-table-column prop="type" label="类型" width="100">
              <template #default="{ row }">
                <el-tag :type="getCodeTypeColor(row.type)" size="small">
                  {{ getCodeTypeText(row.type) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="value" label="价值" width="100">
              <template #default="{ row }">
                {{ row.value }}{{ row.type === 'points' ? '积分' : '' }}
              </template>
            </el-table-column>
            <el-table-column prop="description" label="描述" min-width="200" />
            <el-table-column prop="maxUses" label="最大使用次数" width="120" />
            <el-table-column prop="usedCount" label="已使用" width="80" />
            <el-table-column prop="status" label="状态" width="100">
              <template #default="{ row }">
                <el-tag :type="row.status === 'active' ? 'success' : 'info'" size="small">
                  {{ row.status === 'active' ? '有效' : '无效' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="expiryTime" label="过期时间" width="180">
              <template #default="{ row }">
                {{ formatDate(row.expiryTime) }}
              </template>
            </el-table-column>
            <el-table-column label="操作" width="150" fixed="right">
              <template #default="{ row }">
                <el-button
                  :type="row.status === 'active' ? 'warning' : 'success'"
                  size="small"
                  @click="toggleCodeStatus(row)"
                >
                  {{ row.status === 'active' ? '禁用' : '启用' }}
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-tab-pane>
    </el-tabs>

    <!-- 商品编辑对话框 -->
    <el-dialog
      v-model="itemDialogVisible"
      :title="isEditingItem ? '编辑商品' : '新增商品'"
      width="600px"
      @close="resetItemForm"
    >
      <el-form
        ref="itemFormRef"
        :model="itemForm"
        :rules="itemRules"
        label-width="100px"
      >
        <el-form-item label="商品名称" prop="name">
          <el-input v-model="itemForm.name" placeholder="请输入商品名称" />
        </el-form-item>
        <el-form-item label="商品描述" prop="description">
          <el-input
            v-model="itemForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入商品描述"
          />
        </el-form-item>
        <el-form-item label="分类" prop="category">
          <el-select v-model="itemForm.category" style="width: 100%">
            <el-option label="虚拟商品" value="virtual" />
            <el-option label="实物商品" value="physical" />
            <el-option label="服务" value="service" />
          </el-select>
        </el-form-item>
        <el-form-item label="价格" prop="price">
          <el-input-number
            v-model="itemForm.price"
            :min="0"
            :max="999999"
            style="width: 100%"
          />
          <span class="form-help">单位：积分</span>
        </el-form-item>
        <el-form-item label="库存" prop="stock">
          <el-input-number
            v-model="itemForm.stock"
            :min="0"
            :max="999999"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="itemForm.status" style="width: 100%">
            <el-option label="上架" value="active" />
            <el-option label="下架" value="inactive" />
          </el-select>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="itemDialogVisible = false">取消</el-button>
        <el-button
          type="primary"
          :loading="itemSaving"
          @click="saveItem"
        >
          保存
        </el-button>
      </template>
    </el-dialog>

    <!-- 兑换码创建对话框 -->
    <el-dialog
      v-model="codeDialogVisible"
      title="创建兑换码"
      width="500px"
      @close="resetCodeForm"
    >
      <el-form
        ref="codeFormRef"
        :model="codeForm"
        :rules="codeRules"
        label-width="100px"
      >
        <el-form-item label="兑换码" prop="code">
          <el-input v-model="codeForm.code" placeholder="请输入兑换码" />
          <el-button @click="generateCode" style="margin-left: 8px;">随机生成</el-button>
        </el-form-item>
        <el-form-item label="类型" prop="type">
          <el-select v-model="codeForm.type" style="width: 100%">
            <el-option label="积分" value="points" />
            <el-option label="商品" value="item" />
            <el-option label="折扣" value="discount" />
          </el-select>
        </el-form-item>
        <el-form-item label="价值" prop="value">
          <el-input-number
            v-model="codeForm.value"
            :min="1"
            :max="99999"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input
            v-model="codeForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入兑换码描述"
          />
        </el-form-item>
        <el-form-item label="最大使用次数" prop="maxUses">
          <el-input-number
            v-model="codeForm.maxUses"
            :min="1"
            :max="99999"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="过期时间">
          <el-date-picker
            v-model="codeForm.expiryTime"
            type="datetime"
            placeholder="选择过期时间"
            style="width: 100%"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="codeDialogVisible = false">取消</el-button>
        <el-button
          type="primary"
          :loading="codeSaving"
          @click="saveCode"
        >
          创建
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  ShoppingBag as ShoppingBagIcon,
  CheckCircle as CheckCircleIcon,
  TrendingUp as TrendingUpIcon,
  Coins as CoinsIcon,
  RefreshCw as RefreshCwIcon,
  Plus as PlusIcon,
  Search as SearchIcon,
  RotateCcw as RotateCcwIcon,
  Edit as EditIcon,
  Trash as TrashIcon,
  Image as ImageIcon
} from 'lucide-vue-next'
import { callCloudFunction } from '@/api/wechat-api.js'

// 响应式数据
const itemsLoading = ref(false)
const codesLoading = ref(false)
const itemSaving = ref(false)
const codeSaving = ref(false)
const activeTab = ref('items')
const storeItems = ref([])
const redemptionCodes = ref([])
const stats = ref({})
const itemDialogVisible = ref(false)
const codeDialogVisible = ref(false)
const isEditingItem = ref(false)
const itemFormRef = ref()
const codeFormRef = ref()

const itemFilters = reactive({
  category: 'all',
  status: 'all',
  keyword: '',
  sortBy: 'sortOrder',
  sortOrder: 'asc'
})

const itemPagination = reactive({
  page: 1,
  pageSize: 20,
  total: 0
})

const itemForm = reactive({
  name: '',
  description: '',
  category: 'virtual',
  price: 0,
  stock: 0,
  status: 'active'
})

const itemRules = {
  name: [
    { required: true, message: '请输入商品名称', trigger: 'blur' }
  ],
  description: [
    { required: true, message: '请输入商品描述', trigger: 'blur' }
  ],
  category: [
    { required: true, message: '请选择分类', trigger: 'change' }
  ],
  price: [
    { required: true, message: '请输入价格', trigger: 'blur' },
    { type: 'number', min: 0, message: '价格不能为负数', trigger: 'blur' }
  ],
  stock: [
    { required: true, message: '请输入库存', trigger: 'blur' },
    { type: 'number', min: 0, message: '库存不能为负数', trigger: 'blur' }
  ]
}

const codeForm = reactive({
  code: '',
  type: 'points',
  value: 0,
  description: '',
  maxUses: 1,
  expiryTime: null
})

const codeRules = {
  code: [
    { required: true, message: '请输入兑换码', trigger: 'blur' },
    { min: 4, max: 20, message: '兑换码长度在4-20字符之间', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择类型', trigger: 'change' }
  ],
  value: [
    { required: true, message: '请输入价值', trigger: 'blur' },
    { type: 'number', min: 1, message: '价值必须大于0', trigger: 'blur' }
  ],
  description: [
    { required: true, message: '请输入描述', trigger: 'blur' }
  ],
  maxUses: [
    { required: true, message: '请输入最大使用次数', trigger: 'blur' },
    { type: 'number', min: 1, message: '使用次数必须大于0', trigger: 'blur' }
  ]
}

// 方法
async function loadStoreItems() {
  try {
    itemsLoading.value = true
    
    const result = await callCloudFunction('getStoreItemListAdmin', {
      ...itemFilters,
      page: itemPagination.page,
      pageSize: itemPagination.pageSize
    })

    if (result.success) {
      storeItems.value = result.data || []
      itemPagination.total = result.pagination?.total || 0
    } else {
      ElMessage.error(result.message || '获取商品列表失败')
    }
  } catch (error) {
    console.error('获取商品列表失败:', error)
    ElMessage.error('获取商品列表失败')
  } finally {
    itemsLoading.value = false
  }
}

async function loadRedemptionCodes() {
  try {
    codesLoading.value = true
    
    const result = await callCloudFunction('getRedemptionCodeListAdmin', {
      page: 1,
      pageSize: 100
    })

    if (result.success) {
      redemptionCodes.value = result.data || []
    } else {
      ElMessage.error(result.message || '获取兑换码列表失败')
    }
  } catch (error) {
    console.error('获取兑换码列表失败:', error)
    ElMessage.error('获取兑换码列表失败')
  } finally {
    codesLoading.value = false
  }
}

async function loadStats() {
  try {
    const result = await callCloudFunction('getStoreStatsAdmin', { period: '30d' })
    if (result.success) {
      stats.value = result.data || {}
    }
  } catch (error) {
    console.error('获取商店统计失败:', error)
  }
}

function resetItemFilters() {
  itemFilters.category = 'all'
  itemFilters.status = 'all'
  itemFilters.keyword = ''
  itemFilters.sortBy = 'sortOrder'
  itemFilters.sortOrder = 'asc'
  itemPagination.page = 1
  loadStoreItems()
}

function handleItemSizeChange(size) {
  itemPagination.pageSize = size
  itemPagination.page = 1
  loadStoreItems()
}

function handleItemCurrentChange(page) {
  itemPagination.page = page
  loadStoreItems()
}

async function refreshData() {
  await Promise.all([
    loadStoreItems(),
    loadRedemptionCodes(),
    loadStats()
  ])
  ElMessage.success('数据已刷新')
}

function showCreateItemDialog() {
  isEditingItem.value = false
  resetItemForm()
  itemDialogVisible.value = true
}

function showCreateCodeDialog() {
  resetCodeForm()
  codeDialogVisible.value = true
}

function editItem(item) {
  isEditingItem.value = true
  Object.assign(itemForm, item)
  itemDialogVisible.value = true
}

function resetItemForm() {
  Object.assign(itemForm, {
    name: '',
    description: '',
    category: 'virtual',
    price: 0,
    stock: 0,
    status: 'active'
  })
  itemFormRef.value?.clearValidate()
}

function resetCodeForm() {
  Object.assign(codeForm, {
    code: '',
    type: 'points',
    value: 0,
    description: '',
    maxUses: 1,
    expiryTime: null
  })
  codeFormRef.value?.clearValidate()
}

function generateCode() {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'
  let result = ''
  for (let i = 0; i < 8; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  codeForm.code = result
}

async function saveItem() {
  try {
    const valid = await itemFormRef.value.validate()
    if (!valid) return

    itemSaving.value = true

    const apiType = isEditingItem.value ? 'updateStoreItem' : 'createStoreItem'
    const data = isEditingItem.value ? { ...itemForm } : itemForm

    const result = await callCloudFunction(apiType, data)

    if (result.success) {
      ElMessage.success(isEditingItem.value ? '商品更新成功' : '商品创建成功')
      itemDialogVisible.value = false
      loadStoreItems()
    } else {
      ElMessage.error(result.message || '保存商品失败')
    }
  } catch (error) {
    console.error('保存商品失败:', error)
    ElMessage.error('保存商品失败')
  } finally {
    itemSaving.value = false
  }
}

async function saveCode() {
  try {
    const valid = await codeFormRef.value.validate()
    if (!valid) return

    codeSaving.value = true

    const result = await callCloudFunction('createRedemptionCode', codeForm)

    if (result.success) {
      ElMessage.success('兑换码创建成功')
      codeDialogVisible.value = false
      loadRedemptionCodes()
    } else {
      ElMessage.error(result.message || '创建兑换码失败')
    }
  } catch (error) {
    console.error('创建兑换码失败:', error)
    ElMessage.error('创建兑换码失败')
  } finally {
    codeSaving.value = false
  }
}

async function deleteItem(item) {
  try {
    await ElMessageBox.confirm(
      `确定要删除商品 "${item.name}" 吗？`,
      '删除商品',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const result = await callCloudFunction('deleteStoreItem', {
      id: item._id
    })

    if (result.success) {
      ElMessage.success('商品删除成功')
      loadStoreItems()
    } else {
      ElMessage.error(result.message || '删除商品失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除商品失败:', error)
      ElMessage.error('删除商品失败')
    }
  }
}

async function toggleCodeStatus(code) {
  try {
    const newStatus = code.status === 'active' ? 'inactive' : 'active'
    const action = newStatus === 'active' ? '启用' : '禁用'

    await ElMessageBox.confirm(
      `确定要${action}兑换码 "${code.code}" 吗？`,
      `${action}兑换码`,
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // TODO: 实现兑换码状态切换API
    ElMessage.success(`兑换码${action}成功`)
    loadRedemptionCodes()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('切换兑换码状态失败:', error)
      ElMessage.error('操作失败')
    }
  }
}

// 辅助函数
function getCategoryText(category) {
  const texts = {
    virtual: '虚拟',
    physical: '实物',
    service: '服务'
  }
  return texts[category] || category
}

function getStatusColor(status) {
  const colors = {
    active: 'success',
    inactive: 'info',
    out_of_stock: 'warning'
  }
  return colors[status] || 'info'
}

function getStatusText(status) {
  const texts = {
    active: '上架',
    inactive: '下架',
    out_of_stock: '缺货'
  }
  return texts[status] || status
}

function getCodeTypeColor(type) {
  const colors = {
    points: 'warning',
    item: 'primary',
    discount: 'success'
  }
  return colors[type] || 'info'
}

function getCodeTypeText(type) {
  const texts = {
    points: '积分',
    item: '商品',
    discount: '折扣'
  }
  return texts[type] || type
}

function formatDate(dateString) {
  if (!dateString) return '-'
  return new Date(dateString).toLocaleString('zh-CN')
}

// 生命周期
onMounted(() => {
  loadStoreItems()
  loadRedemptionCodes()
  loadStats()
})
</script>

<style scoped>
.store-container {
  padding: 24px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 8px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.page-description {
  color: #6b7280;
  margin: 0;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.stat-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.stat-icon.total { background: #3b82f6; }
.stat-icon.active { background: #10b981; }
.stat-icon.sales { background: #f59e0b; }
.stat-icon.revenue { background: #8b5cf6; }

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
}

.stat-label {
  font-size: 14px;
  color: #6b7280;
}

.store-tabs {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.filter-section {
  margin-bottom: 20px;
  padding: 16px;
  background: #f9fafb;
  border-radius: 8px;
}

.table-section {
  margin-top: 16px;
}

.codes-section {
  margin-top: 16px;
}

.codes-header {
  margin-bottom: 16px;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.item-icon {
  width: 50px;
  height: 50px;
  border-radius: 8px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.price-text {
  font-weight: 600;
  color: #f59e0b;
}

.form-help {
  font-size: 12px;
  color: #6b7280;
  margin-left: 8px;
}
</style>
