<template>
  <div class="setup-container">
    <div class="setup-card">
      <div class="setup-header">
        <h1 class="setup-title">
          <SettingsIcon :size="28" />
          微信云函数配置
        </h1>
        <p class="setup-description">
          请填写微信小程序的相关信息以连接云函数
        </p>
      </div>

      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="120px"
        class="setup-form"
      >
        <el-form-item label="AppID" prop="appId">
          <el-input
            v-model="form.appId"
            placeholder="请输入小程序AppID"
          >
            <template #prefix>
              <KeyIcon :size="16" />
            </template>
          </el-input>
          <div class="form-help">
            小程序的唯一标识符，在微信公众平台获取
          </div>
        </el-form-item>

        <el-form-item label="AppSecret" prop="appSecret">
          <el-input
            v-model="form.appSecret"
            type="password"
            placeholder="请输入小程序AppSecret"
            show-password
          >
            <template #prefix>
              <LockIcon :size="16" />
            </template>
          </el-input>
          <div class="form-help">
            小程序的密钥，在微信公众平台获取，请妥善保管
          </div>
        </el-form-item>

        <el-form-item label="云环境ID" prop="env">
          <el-input
            v-model="form.env"
            placeholder="请输入云开发环境ID"
          >
            <template #prefix>
              <CloudIcon :size="16" />
            </template>
          </el-input>
          <div class="form-help">
            云开发环境的唯一标识，如：cloud1-xxx
          </div>
        </el-form-item>

        <el-form-item label="云函数名称" prop="functionName">
          <el-input
            v-model="form.functionName"
            placeholder="请输入管理端云函数名称"
          >
            <template #prefix>
              <CodeIcon :size="16" />
            </template>
          </el-input>
          <div class="form-help">
            管理端云函数的名称，如：cloud-functions
          </div>
        </el-form-item>

        <el-form-item label="管理密钥" prop="secretKey">
          <el-input
            v-model="form.secretKey"
            type="password"
            placeholder="请输入管理端密钥"
            show-password
          >
            <template #prefix>
              <ShieldIcon :size="16" />
            </template>
          </el-input>
          <div class="form-help">
            管理端API的访问密钥，在云函数环境变量中配置
          </div>
        </el-form-item>

        <div class="form-actions">
          <el-button
            type="primary"
            size="large"
            :loading="testing"
            @click="handleTest"
          >
            <template #icon>
              <WifiIcon :size="16" />
            </template>
            测试连接
          </el-button>

          <el-button
            type="success"
            size="large"
            :loading="saving"
            :disabled="!tested"
            @click="handleSave"
          >
            <template #icon>
              <CheckIcon :size="16" />
            </template>
            保存配置
          </el-button>
        </div>

        <div v-if="testResult" class="test-result">
          <el-alert
            :title="testResult.success ? '连接成功' : '连接失败'"
            :type="testResult.success ? 'success' : 'error'"
            :description="testResult.message"
            show-icon
            :closable="false"
          />
        </div>
      </el-form>

      <div class="setup-footer">
        <el-divider />
        <div class="help-links">
          <el-link type="primary" href="https://developers.weixin.qq.com/miniprogram/dev/wxcloud/" target="_blank">
            <BookOpenIcon :size="16" />
            微信云开发文档
          </el-link>
          <el-link type="primary" href="https://mp.weixin.qq.com/" target="_blank">
            <ExternalLinkIcon :size="16" />
            微信公众平台
          </el-link>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useSystemStore } from '@/stores/system.js'
import {
  Settings as SettingsIcon,
  Key as KeyIcon,
  Lock as LockIcon,
  Cloud as CloudIcon,
  Code as CodeIcon,
  Shield as ShieldIcon,
  Wifi as WifiIcon,
  Check as CheckIcon,
  BookOpen as BookOpenIcon,
  ExternalLink as ExternalLinkIcon
} from 'lucide-vue-next'
import { 
  setWechatConfig, 
  getWechatConfig, 
  testConnection, 
  isConfigValid 
} from '@/api/wechat-api.js'

const router = useRouter()
const systemStore = useSystemStore()
const formRef = ref()

const form = reactive({
  appId: '',
  appSecret: '',
  env: '',
  functionName: '',
  secretKey: ''
})

const rules = {
  appId: [
    { required: true, message: '请输入AppID', trigger: 'blur' },
    { min: 10, message: 'AppID长度不能少于10位', trigger: 'blur' }
  ],
  appSecret: [
    { required: true, message: '请输入AppSecret', trigger: 'blur' },
    { min: 20, message: 'AppSecret长度不能少于20位', trigger: 'blur' }
  ],
  env: [
    { required: true, message: '请输入云环境ID', trigger: 'blur' },
    { min: 5, message: '云环境ID长度不能少于5位', trigger: 'blur' }
  ],
  functionName: [
    { required: true, message: '请输入云函数名称', trigger: 'blur' },
    { min: 3, message: '云函数名称长度不能少于3位', trigger: 'blur' }
  ],
  secretKey: [
    { required: true, message: '请输入管理密钥', trigger: 'blur' },
    { min: 8, message: '管理密钥长度不能少于8位', trigger: 'blur' }
  ]
}

const testing = ref(false)
const saving = ref(false)
const tested = ref(false)
const testResult = ref(null)

// 测试连接
async function handleTest() {
  try {
    // 验证表单
    const valid = await formRef.value.validate()
    if (!valid) return

    testing.value = true
    testResult.value = null

    // 设置临时配置
    setWechatConfig(form)

    // 测试连接
    const result = await testConnection()
    testResult.value = result
    tested.value = result.success

    // 更新系统连接状态
    systemStore.setApiConnected(result.success)

    if (result.success) {
      ElMessage.success('连接测试成功！')
    } else {
      ElMessage.error(`连接测试失败：${result.message}`)
    }

  } catch (error) {
    console.error('测试连接失败:', error)
    testResult.value = {
      success: false,
      message: error.message || '测试连接失败'
    }
    ElMessage.error('测试连接失败')
  } finally {
    testing.value = false
  }
}

// 保存配置
async function handleSave() {
  try {
    if (!tested.value) {
      ElMessage.warning('请先测试连接')
      return
    }

    saving.value = true

    // 确认保存
    await ElMessageBox.confirm(
      '确定要保存这些配置信息吗？配置将保存在本地。',
      '确认保存',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info'
      }
    )

    // 保存配置
    setWechatConfig(form)

    ElMessage.success('配置保存成功！')

    // 跳转到仪表板
    setTimeout(() => {
      router.push('/dashboard')
    }, 1000)

  } catch (error) {
    if (error !== 'cancel') {
      console.error('保存配置失败:', error)
      ElMessage.error('保存配置失败')
    }
  } finally {
    saving.value = false
  }
}

// 加载已保存的配置
function loadSavedConfig() {
  const config = getWechatConfig()
  if (config.appId) {
    Object.assign(form, config)
    tested.value = isConfigValid()
    console.log('已加载保存的配置')
  }
}

onMounted(() => {
  loadSavedConfig()
})
</script>

<style scoped>
.setup-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.setup-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  padding: 40px;
  width: 100%;
  max-width: 600px;
}

.setup-header {
  text-align: center;
  margin-bottom: 40px;
}

.setup-title {
  font-size: 28px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 10px 0;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
}

.setup-description {
  color: #7f8c8d;
  font-size: 16px;
  margin: 0;
}

.setup-form {
  margin-bottom: 30px;
}

.form-help {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
  line-height: 1.4;
}

.form-actions {
  display: flex;
  gap: 15px;
  justify-content: center;
  margin: 30px 0;
}

.form-actions .el-button {
  min-width: 120px;
}

.test-result {
  margin: 20px 0;
}

.setup-footer {
  margin-top: 30px;
}

.help-links {
  display: flex;
  justify-content: center;
  gap: 30px;
  margin-top: 20px;
}

.help-links .el-link {
  display: flex;
  align-items: center;
  gap: 5px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #2c3e50;
}

:deep(.el-input__wrapper) {
  border-radius: 8px;
}

:deep(.el-button) {
  border-radius: 8px;
  font-weight: 500;
}

/* 响应式布局 */
@media (max-width: 768px) {
  .setup-container {
    padding: 32px;
    align-items: flex-start;
    padding-top: 20px;
  }

  .setup-card {
    padding: 20px;
    margin: 0;
    border-radius: 8px;
  }

  .setup-header {
    margin: 25px 0;
  }

  .setup-title {
    font-size: 24px;
  }

  .setup-description {
    font-size: 14px;
  }
}

@media (max-width: 480px) {
  .setup-container {
    padding: 5px;
  }

  .setup-card {
    padding: 15px;
  }

  .setup-title {
    font-size: 20px;
    flex-direction: column;
    gap: 5px;
  }

  .form-help {
    font-size: 11px;
  }
}
</style>
