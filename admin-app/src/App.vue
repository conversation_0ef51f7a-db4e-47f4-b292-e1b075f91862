<template>
  <router-view />
</template>

<script setup>
import { onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { isConfigValid } from '@/api/wechat-api.js'

const router = useRouter()

onMounted(async () => {
  // 检查微信配置
  if (!isConfigValid()) {
    // 配置不完整，跳转到配置页面
    if (router.currentRoute.value.path !== '/setup') {
      router.push('/setup')
    }
    return
  }

  // 配置完整，直接进入管理后台
  if (router.currentRoute.value.path === '/setup') {
    router.push('/dashboard')
  }
})
</script>
