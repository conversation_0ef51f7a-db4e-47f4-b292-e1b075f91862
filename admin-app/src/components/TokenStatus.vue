<template>
  <div class="token-status-card">
    <div class="card-header">
      <h4>Access Token 状态</h4>
      <el-button
        type="primary"
        size="small"
        :loading="refreshing"
        @click="refreshToken"
      >
        <template #icon>
          <RefreshCwIcon :size="14" />
        </template>
        刷新Token
      </el-button>
    </div>
    
    <div class="status-grid">
      <div class="status-item">
        <label>Token状态:</label>
        <el-tag :type="tokenStatus.isValid ? 'success' : 'danger'">
          {{ tokenStatus.isValid ? '有效' : '无效/过期' }}
        </el-tag>
      </div>
      
      <div class="status-item">
        <label>剩余时间:</label>
        <span :class="getTimeClass(tokenStatus.expiresIn)">
          {{ formatExpiresIn(tokenStatus.expiresIn) }}
        </span>
      </div>
      
      <div class="status-item">
        <label>过期时间:</label>
        <span>{{ formatExpiresAt(tokenStatus.expiresAt) }}</span>
      </div>
      
      <div class="status-item">
        <label>自动重试:</label>
        <el-switch
          v-model="autoRetryEnabled"
          active-text="启用"
          inactive-text="禁用"
          size="small"
          @change="toggleAutoRetry"
        />
      </div>

      <div class="status-item">
        <label>重试次数:</label>
        <span>{{ retryCount }}</span>
      </div>

      <div class="status-item">
        <label>刷新状态:</label>
        <el-tag v-if="tokenStatus.isRefreshing" type="warning">
          <LoaderIcon :size="12" class="animate-spin" />
          刷新中
        </el-tag>
        <span v-else class="text-gray-500">空闲</span>
      </div>
    </div>
    
    <div v-if="!tokenStatus.hasToken" class="no-token-warning">
      <el-alert
        title="未获取到Access Token"
        description="请检查微信配置是否正确，或点击刷新Token按钮重新获取"
        type="warning"
        show-icon
        :closable="false"
      />
    </div>
    
    <div v-else-if="needsRefresh" class="refresh-warning">
      <el-alert
        title="Token即将过期"
        description="建议刷新Access Token以确保API调用正常"
        type="warning"
        show-icon
        :closable="false"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import {
  RefreshCw as RefreshCwIcon,
  Loader as LoaderIcon
} from 'lucide-vue-next'
import { getTokenStatus, needsTokenRefresh, testConnection, refreshAccessToken } from '@/api/wechat-api.js'

// 响应式数据
const refreshing = ref(false)
const autoRetryEnabled = ref(true)
const retryCount = ref(0)
const tokenStatus = ref({
  hasToken: false,
  isValid: false,
  expiresAt: 0,
  expiresIn: 0,
  isRefreshing: false
})

let updateTimer = null

// 计算属性
const needsRefresh = computed(() => {
  return tokenStatus.value.hasToken && tokenStatus.value.expiresIn < 300 // 5分钟内过期
})

// 方法
function updateTokenStatus() {
  tokenStatus.value = getTokenStatus()
}

async function refreshToken() {
  try {
    refreshing.value = true

    const result = await refreshAccessToken()

    if (result.success) {
      ElMessage.success('Access Token刷新成功')
      updateTokenStatus()
      retryCount.value++
      saveRetryCount()
    } else {
      ElMessage.error(`Token刷新失败: ${result.message}`)
    }
  } catch (error) {
    console.error('刷新Token失败:', error)
    ElMessage.error('Token刷新失败')
  } finally {
    refreshing.value = false
  }
}

function toggleAutoRetry(enabled) {
  autoRetryEnabled.value = enabled
  localStorage.setItem('auto_retry_enabled', enabled.toString())

  ElMessage.success(`自动重试已${enabled ? '启用' : '禁用'}`)
}

function loadSettings() {
  const autoRetry = localStorage.getItem('auto_retry_enabled')
  if (autoRetry !== null) {
    autoRetryEnabled.value = autoRetry === 'true'
  }

  const savedRetryCount = localStorage.getItem('token_retry_count')
  if (savedRetryCount) {
    retryCount.value = parseInt(savedRetryCount) || 0
  }
}

function saveRetryCount() {
  localStorage.setItem('token_retry_count', retryCount.value.toString())
}

function formatExpiresIn(seconds) {
  if (seconds <= 0) return '已过期'
  
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const secs = seconds % 60
  
  if (hours > 0) {
    return `${hours}小时${minutes}分钟`
  } else if (minutes > 0) {
    return `${minutes}分钟${secs}秒`
  } else {
    return `${secs}秒`
  }
}

function formatExpiresAt(timestamp) {
  if (!timestamp) return '-'
  return new Date(timestamp).toLocaleString('zh-CN')
}

function getTimeClass(seconds) {
  if (seconds <= 0) return 'text-red-500'
  if (seconds < 300) return 'text-orange-500' // 5分钟内
  if (seconds < 1800) return 'text-yellow-500' // 30分钟内
  return 'text-green-500'
}

function startUpdateTimer() {
  updateTimer = setInterval(() => {
    updateTokenStatus()
  }, 1000) // 每秒更新一次
}

function stopUpdateTimer() {
  if (updateTimer) {
    clearInterval(updateTimer)
    updateTimer = null
  }
}

// 生命周期
onMounted(() => {
  loadSettings()
  updateTokenStatus()
  startUpdateTimer()
})

onUnmounted(() => {
  stopUpdateTimer()
})
</script>

<style scoped>
.token-status-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  margin-bottom: 16px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.card-header h4 {
  margin: 0;
  color: #1f2937;
}

.status-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 16px;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-item label {
  font-weight: 500;
  color: #374151;
  min-width: 80px;
}

.no-token-warning,
.refresh-warning {
  margin-top: 16px;
}

.text-red-500 { color: #ef4444; }
.text-orange-500 { color: #f97316; }
.text-yellow-500 { color: #eab308; }
.text-green-500 { color: #22c55e; }
.text-gray-500 { color: #6b7280; }

.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}
</style>
