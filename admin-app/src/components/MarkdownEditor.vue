<template>
  <div class="markdown-editor">
    <div class="editor-toolbar">
      <el-button-group>
        <el-button 
          :type="mode === 'edit' ? 'primary' : ''"
          size="small"
          @click="mode = 'edit'"
        >
          <template #icon>
            <EditIcon :size="14" />
          </template>
          编辑
        </el-button>
        <el-button 
          :type="mode === 'preview' ? 'primary' : ''"
          size="small"
          @click="mode = 'preview'"
        >
          <template #icon>
            <EyeIcon :size="14" />
          </template>
          预览
        </el-button>
        <el-button 
          :type="mode === 'split' ? 'primary' : ''"
          size="small"
          @click="mode = 'split'"
        >
          <template #icon>
            <ColumnsIcon :size="14" />
          </template>
          分屏
        </el-button>
      </el-button-group>
      
      <div class="toolbar-actions">
        <el-button size="small" @click="insertMarkdown('**', '**')">
          <BoldIcon :size="14" />
        </el-button>
        <el-button size="small" @click="insertMarkdown('*', '*')">
          <ItalicIcon :size="14" />
        </el-button>
        <el-button size="small" @click="insertMarkdown('`', '`')">
          <CodeIcon :size="14" />
        </el-button>
        <el-button size="small" @click="insertMarkdown('[', '](url)')">
          <LinkIcon :size="14" />
        </el-button>
        <el-button size="small" @click="insertMarkdown('![', '](url)')">
          <ImageIcon :size="14" />
        </el-button>
      </div>
    </div>
    
    <div class="editor-content" :class="mode">
      <!-- 编辑区域 -->
      <div v-show="mode === 'edit' || mode === 'split'" class="edit-area">
        <el-input
          ref="textareaRef"
          v-model="content"
          type="textarea"
          :rows="rows"
          :placeholder="placeholder"
          @input="handleInput"
          @keydown="handleKeydown"
        />
      </div>
      
      <!-- 预览区域 -->
      <div v-show="mode === 'preview' || mode === 'split'" class="preview-area">
        <div class="markdown-preview" v-html="renderedContent"></div>
      </div>
    </div>
    
    <div class="editor-footer">
      <span class="word-count">{{ wordCount }} 字符</span>
      <span class="line-count">{{ lineCount }} 行</span>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, nextTick } from 'vue'
import { marked } from 'marked'
import hljs from 'highlight.js'
import 'highlight.js/styles/github.css'
import {
  Edit as EditIcon,
  Eye as EyeIcon,
  Columns as ColumnsIcon,
  Bold as BoldIcon,
  Italic as ItalicIcon,
  Code as CodeIcon,
  Link as LinkIcon,
  Image as ImageIcon
} from 'lucide-vue-next'

// Props
const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  placeholder: {
    type: String,
    default: '请输入Markdown内容...'
  },
  rows: {
    type: Number,
    default: 15
  }
})

// Emits
const emit = defineEmits(['update:modelValue'])

// 响应式数据
const mode = ref('edit')
const content = ref(props.modelValue)
const textareaRef = ref()

// 配置marked
marked.setOptions({
  highlight: function(code, lang) {
    if (lang && hljs.getLanguage(lang)) {
      try {
        return hljs.highlight(code, { language: lang }).value
      } catch (err) {
        console.error('代码高亮失败:', err)
      }
    }
    return hljs.highlightAuto(code).value
  },
  breaks: true,
  gfm: true
})

// 计算属性
const renderedContent = computed(() => {
  try {
    return marked(content.value || '')
  } catch (error) {
    console.error('Markdown渲染失败:', error)
    return '<p>Markdown渲染失败</p>'
  }
})

const wordCount = computed(() => {
  return content.value.length
})

const lineCount = computed(() => {
  return content.value.split('\n').length
})

// 监听器
watch(() => props.modelValue, (newValue) => {
  content.value = newValue
})

watch(content, (newValue) => {
  emit('update:modelValue', newValue)
})

// 方法
function handleInput() {
  emit('update:modelValue', content.value)
}

function handleKeydown(event) {
  // Tab键插入空格
  if (event.key === 'Tab') {
    event.preventDefault()
    insertText('  ')
  }
  
  // Ctrl+B 加粗
  if (event.ctrlKey && event.key === 'b') {
    event.preventDefault()
    insertMarkdown('**', '**')
  }
  
  // Ctrl+I 斜体
  if (event.ctrlKey && event.key === 'i') {
    event.preventDefault()
    insertMarkdown('*', '*')
  }
  
  // Ctrl+K 链接
  if (event.ctrlKey && event.key === 'k') {
    event.preventDefault()
    insertMarkdown('[', '](url)')
  }
}

function insertText(text) {
  const textarea = textareaRef.value?.textarea
  if (!textarea) return
  
  const start = textarea.selectionStart
  const end = textarea.selectionEnd
  const value = content.value
  
  content.value = value.substring(0, start) + text + value.substring(end)
  
  nextTick(() => {
    textarea.focus()
    textarea.setSelectionRange(start + text.length, start + text.length)
  })
}

function insertMarkdown(prefix, suffix) {
  const textarea = textareaRef.value?.textarea
  if (!textarea) return
  
  const start = textarea.selectionStart
  const end = textarea.selectionEnd
  const value = content.value
  const selectedText = value.substring(start, end)
  
  const newText = prefix + selectedText + suffix
  content.value = value.substring(0, start) + newText + value.substring(end)
  
  nextTick(() => {
    textarea.focus()
    if (selectedText) {
      // 如果有选中文本，选中整个新文本
      textarea.setSelectionRange(start, start + newText.length)
    } else {
      // 如果没有选中文本，光标定位到prefix和suffix之间
      textarea.setSelectionRange(start + prefix.length, start + prefix.length)
    }
  })
}

// 暴露方法给父组件
defineExpose({
  insertText,
  insertMarkdown,
  focus: () => textareaRef.value?.focus()
})
</script>

<style scoped>
.markdown-editor {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  overflow: hidden;
}

.editor-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: #f5f7fa;
  border-bottom: 1px solid #dcdfe6;
}

.toolbar-actions {
  display: flex;
  gap: 4px;
}

.editor-content {
  display: flex;
  min-height: 300px;
}

.editor-content.edit .edit-area {
  width: 100%;
}

.editor-content.preview .preview-area {
  width: 100%;
}

.editor-content.split .edit-area,
.editor-content.split .preview-area {
  width: 50%;
}

.edit-area {
  border-right: 1px solid #dcdfe6;
}

.edit-area :deep(.el-textarea__inner) {
  border: none;
  border-radius: 0;
  resize: none;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 14px;
  line-height: 1.6;
}

.preview-area {
  background: #fff;
  overflow-y: auto;
}

.markdown-preview {
  padding: 16px;
  line-height: 1.6;
  color: #333;
}

.markdown-preview :deep(h1),
.markdown-preview :deep(h2),
.markdown-preview :deep(h3),
.markdown-preview :deep(h4),
.markdown-preview :deep(h5),
.markdown-preview :deep(h6) {
  margin: 16px 0 8px 0;
  font-weight: 600;
  line-height: 1.25;
}

.markdown-preview :deep(h1) {
  font-size: 2em;
  border-bottom: 1px solid #eaecef;
  padding-bottom: 8px;
}

.markdown-preview :deep(h2) {
  font-size: 1.5em;
  border-bottom: 1px solid #eaecef;
  padding-bottom: 8px;
}

.markdown-preview :deep(p) {
  margin: 8px 0;
}

.markdown-preview :deep(blockquote) {
  margin: 16px 0;
  padding: 0 16px;
  color: #6a737d;
  border-left: 4px solid #dfe2e5;
}

.markdown-preview :deep(code) {
  padding: 2px 4px;
  background: #f6f8fa;
  border-radius: 3px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 85%;
}

.markdown-preview :deep(pre) {
  padding: 16px;
  background: #f6f8fa;
  border-radius: 6px;
  overflow-x: auto;
  margin: 16px 0;
}

.markdown-preview :deep(pre code) {
  background: none;
  padding: 0;
}

.markdown-preview :deep(table) {
  border-collapse: collapse;
  margin: 16px 0;
  width: 100%;
}

.markdown-preview :deep(table th),
.markdown-preview :deep(table td) {
  border: 1px solid #dfe2e5;
  padding: 6px 13px;
}

.markdown-preview :deep(table th) {
  background: #f6f8fa;
  font-weight: 600;
}

.markdown-preview :deep(ul),
.markdown-preview :deep(ol) {
  margin: 8px 0;
  padding-left: 24px;
}

.markdown-preview :deep(li) {
  margin: 4px 0;
}

.markdown-preview :deep(a) {
  color: #0366d6;
  text-decoration: none;
}

.markdown-preview :deep(a:hover) {
  text-decoration: underline;
}

.markdown-preview :deep(img) {
  max-width: 100%;
  height: auto;
  border-radius: 4px;
  margin: 8px 0;
}

.editor-footer {
  display: flex;
  justify-content: flex-end;
  gap: 16px;
  padding: 8px 12px;
  background: #f5f7fa;
  border-top: 1px solid #dcdfe6;
  font-size: 12px;
  color: #909399;
}
</style>
