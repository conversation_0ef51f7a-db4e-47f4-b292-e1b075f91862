<template>
  <div class="admin-layout">
    <!-- 侧边栏 -->
    <aside 
      class="sidebar" 
      :class="{ 'sidebar--collapsed': appStore.sidebarCollapsed }"
    >
      <div class="sidebar__header">
        <div class="logo">
          <img src="/tauri.svg" alt="Logo" class="logo__image" />
          <span v-show="!appStore.sidebarCollapsed" class="logo__text">
            后台管理
          </span>
        </div>
      </div>
      
      <nav class="sidebar__nav">
        <SidebarMenu />
      </nav>
    </aside>

    <!-- 主内容区 -->
    <div class="main-container">
      <!-- 顶部导航栏 -->
      <header class="header">
        <div class="header__left">
          <el-button 
            type="text" 
            @click="appStore.toggleSidebar"
            class="sidebar-toggle"
          >
            <el-icon><Expand v-if="appStore.sidebarCollapsed" /><Fold v-else /></el-icon>
          </el-button>
          
          <Breadcrumb />
        </div>
        
        <div class="header__right">
          <HeaderActions />
        </div>
      </header>

      <!-- 页面内容 -->
      <main class="content">
        <router-view />
      </main>
    </div>

    <!-- 全局加载遮罩 -->
    <div v-if="appStore.loading" class="loading-overlay">
      <div class="loading-spinner">
        <div class="spinner-ring"></div>
      </div>
      <div class="loading-text">加载中...</div>
    </div>
  </div>
</template>

<script setup>
import { onMounted } from 'vue'
import { useAppStore } from '@/stores/app.js'
import { useAuthStore } from '@/stores/auth.js'
import SidebarMenu from './SidebarMenu.vue'
import Breadcrumb from './Breadcrumb.vue'
import HeaderActions from './HeaderActions.vue'
import { Expand, Fold } from '@element-plus/icons-vue'

const appStore = useAppStore()
const authStore = useAuthStore()

onMounted(() => {
  // 加载应用设置
  appStore.loadSettings()
  
  // 初始化认证状态
  authStore.initAuth()
  
  // 检查API连接
  if (authStore.isLoggedIn) {
    authStore.checkConnection()
  }
})
</script>

<style scoped>
.admin-layout {
  display: flex;
  height: 100vh;
  background-color: var(--el-bg-color-page);
}

.sidebar {
  width: 240px;
  background-color: var(--el-bg-color);
  border-right: 1px solid var(--el-border-color);
  transition: width 0.3s ease;
  overflow: hidden;
}

.sidebar--collapsed {
  width: 64px;
}

.sidebar__header {
  height: 60px;
  display: flex;
  align-items: center;
  padding: 0 16px;
  border-bottom: 1px solid var(--el-border-color);
}

.logo {
  display: flex;
  align-items: center;
  gap: 8px;
}

.logo__image {
  width: 32px;
  height: 32px;
}

.logo__text {
  font-size: 18px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  white-space: nowrap;
}

.sidebar__nav {
  height: calc(100vh - 60px);
  overflow-y: auto;
}

.main-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.header {
  height: 60px;
  background-color: var(--el-bg-color);
  border-bottom: 1px solid var(--el-border-color);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
}

.header__left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.sidebar-toggle {
  padding: 8px;
  font-size: 18px;
}

.header__right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(4px);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  position: relative;
}

.spinner-ring {
  width: 100%;
  height: 100%;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #409eff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text {
  margin-top: 16px;
  color: #606266;
  font-size: 14px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.content {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
  background-color: var(--el-bg-color-page);
}

.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}
</style>
