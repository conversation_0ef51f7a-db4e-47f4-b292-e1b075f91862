<template>
  <div class="header-actions">
    <!-- API连接状态 -->
    <div class="connection-status">
      <el-tooltip
        :content="connectionTooltip"
        placement="bottom"
      >
        <el-icon
          :class="connectionStatusClass"
          @click="checkConnection"
        >
          <CircleCheck v-if="authStore.apiConnected" />
          <CircleClose v-else />
        </el-icon>
      </el-tooltip>
    </div>

    <!-- 通知 -->
    <el-dropdown trigger="click" @command="handleNotificationCommand">
      <div class="notification-trigger">
        <el-badge :value="appStore.unreadNotifications" :hidden="appStore.unreadNotifications === 0">
          <el-icon><Bell /></el-icon>
        </el-badge>
      </div>
      <template #dropdown>
        <el-dropdown-menu class="notification-dropdown">
          <div class="notification-header">
            <span>通知</span>
            <el-button
              v-if="appStore.unreadNotifications > 0"
              type="text"
              size="small"
              @click="markAllRead"
            >
              全部已读
            </el-button>
          </div>
          
          <div class="notification-list">
            <template v-if="appStore.notifications.length > 0">
              <div
                v-for="notification in recentNotifications"
                :key="notification.id"
                class="notification-item"
                :class="{ 'notification-item--unread': !notification.read }"
                @click="markAsRead(notification.id)"
              >
                <div class="notification-content">
                  <div class="notification-title">{{ notification.title }}</div>
                  <div class="notification-message">{{ notification.message }}</div>
                  <div class="notification-time">{{ formatTime(notification.timestamp) }}</div>
                </div>
              </div>
            </template>
            <div v-else class="notification-empty">
              暂无通知
            </div>
          </div>
          
          <div class="notification-footer">
            <el-button type="text" size="small" @click="clearAllNotifications">
              清空通知
            </el-button>
          </div>
        </el-dropdown-menu>
      </template>
    </el-dropdown>

    <!-- 主题切换 -->
    <el-tooltip content="切换主题" placement="bottom">
      <el-icon class="theme-toggle" @click="toggleTheme">
        <Sunny v-if="appStore.isDarkTheme" />
        <Moon v-else />
      </el-icon>
    </el-tooltip>

    <!-- 用户菜单 -->
    <el-dropdown trigger="click" @command="handleUserCommand">
      <div class="user-avatar">
        <el-avatar :size="32" :src="authStore.user?.avatar">
          <el-icon><User /></el-icon>
        </el-avatar>
        <span class="username">{{ authStore.user?.name || authStore.user?.username }}</span>
        <el-icon class="dropdown-icon"><ArrowDown /></el-icon>
      </div>
      <template #dropdown>
        <el-dropdown-menu>
          <el-dropdown-item command="profile">
            <el-icon><User /></el-icon>
            个人资料
          </el-dropdown-item>
          <el-dropdown-item command="settings">
            <el-icon><Setting /></el-icon>
            系统设置
          </el-dropdown-item>
          <el-dropdown-item divided command="logout">
            <el-icon><SwitchButton /></el-icon>
            退出登录
          </el-dropdown-item>
        </el-dropdown-menu>
      </template>
    </el-dropdown>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessageBox, ElMessage } from 'element-plus'
import {
  Bell,
  CircleCheck,
  CircleClose,
  Sunny,
  Moon,
  User,
  Setting,
  SwitchButton,
  ArrowDown
} from '@element-plus/icons-vue'
import { useAuthStore } from '@/stores/auth.js'
import { useAppStore } from '@/stores/app.js'
import dayjs from 'dayjs'

const router = useRouter()
const authStore = useAuthStore()
const appStore = useAppStore()

// 连接状态
const connectionStatusClass = computed(() => ({
  'connection-icon': true,
  'connection-icon--connected': authStore.apiConnected,
  'connection-icon--disconnected': !authStore.apiConnected
}))

const connectionTooltip = computed(() => {
  return authStore.apiConnected ? 'API连接正常' : 'API连接失败，点击重试'
})

// 最近的通知（最多显示5条）
const recentNotifications = computed(() => {
  return appStore.notifications.slice(0, 5)
})

// 检查API连接
async function checkConnection() {
  try {
    await authStore.checkConnection()
    if (authStore.apiConnected) {
      ElMessage.success('API连接正常')
    }
  } catch (error) {
    ElMessage.error('API连接失败')
  }
}

// 切换主题
function toggleTheme() {
  const newTheme = appStore.isDarkTheme ? 'light' : 'dark'
  appStore.setTheme(newTheme)
}

// 格式化时间
function formatTime(timestamp) {
  return dayjs(timestamp).format('MM-DD HH:mm')
}

// 标记通知为已读
function markAsRead(id) {
  appStore.markNotificationRead(id)
}

// 标记所有通知为已读
function markAllRead() {
  appStore.markAllNotificationsRead()
}

// 清空所有通知
function clearAllNotifications() {
  appStore.clearNotifications()
}

// 处理通知命令
function handleNotificationCommand(command) {
  // 预留给未来的通知操作
  console.log('Notification command:', command)
}

// 处理用户菜单命令
async function handleUserCommand(command) {
  switch (command) {
    case 'profile':
      // 跳转到个人资料页面
      router.push('/profile')
      break
    case 'settings':
      // 跳转到系统设置页面
      router.push('/system/settings')
      break
    case 'logout':
      await handleLogout()
      break
  }
}

// 处理退出登录
async function handleLogout() {
  try {
    await ElMessageBox.confirm(
      '确定要退出登录吗？',
      '确认退出',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    authStore.logout()
    ElMessage.success('已退出登录')
    router.push('/login')
  } catch (error) {
    // 用户取消退出
  }
}
</script>

<style scoped>
.header-actions {
  display: flex;
  align-items: center;
  gap: 16px;
}

.connection-status {
  cursor: pointer;
}

.connection-icon {
  font-size: 18px;
  transition: color 0.3s ease;
}

.connection-icon--connected {
  color: var(--el-color-success);
}

.connection-icon--disconnected {
  color: var(--el-color-danger);
}

.connection-icon:hover {
  opacity: 0.8;
}

.notification-trigger {
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: background-color 0.3s ease;
}

.notification-trigger:hover {
  background-color: var(--el-fill-color-light);
}

.theme-toggle {
  font-size: 18px;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.theme-toggle:hover {
  background-color: var(--el-fill-color-light);
  color: var(--el-color-primary);
}

.user-avatar {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 6px;
  transition: background-color 0.3s ease;
}

.user-avatar:hover {
  background-color: var(--el-fill-color-light);
}

.username {
  font-size: 14px;
  color: var(--el-text-color-primary);
  max-width: 100px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.dropdown-icon {
  font-size: 12px;
  color: var(--el-text-color-regular);
  transition: transform 0.3s ease;
}

.user-avatar:hover .dropdown-icon {
  transform: rotate(180deg);
}

/* 通知下拉菜单样式 */
.notification-dropdown {
  width: 300px;
  max-height: 400px;
}

.notification-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid var(--el-border-color-lighter);
  font-weight: 500;
}

.notification-list {
  max-height: 300px;
  overflow-y: auto;
}

.notification-item {
  padding: 12px 16px;
  border-bottom: 1px solid var(--el-border-color-lighter);
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.notification-item:hover {
  background-color: var(--el-fill-color-light);
}

.notification-item--unread {
  background-color: var(--el-color-primary-light-9);
}

.notification-content {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.notification-title {
  font-size: 14px;
  font-weight: 500;
  color: var(--el-text-color-primary);
}

.notification-message {
  font-size: 12px;
  color: var(--el-text-color-regular);
  line-height: 1.4;
}

.notification-time {
  font-size: 11px;
  color: var(--el-text-color-placeholder);
}

.notification-empty {
  padding: 40px 16px;
  text-align: center;
  color: var(--el-text-color-placeholder);
  font-size: 14px;
}

.notification-footer {
  padding: 8px 16px;
  border-top: 1px solid var(--el-border-color-lighter);
  text-align: center;
}
</style>
