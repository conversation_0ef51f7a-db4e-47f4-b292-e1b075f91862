<template>
  <el-breadcrumb separator="/" class="breadcrumb">
    <el-breadcrumb-item
      v-for="(item, index) in breadcrumbList"
      :key="item.path"
      :to="index === breadcrumbList.length - 1 ? undefined : item.path"
    >
      <el-icon v-if="item.icon && index === 0">
        <component :is="item.icon" />
      </el-icon>
      {{ item.title }}
    </el-breadcrumb-item>
  </el-breadcrumb>
</template>

<script setup>
import { computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'

const route = useRoute()
const router = useRouter()

// 生成面包屑列表
const breadcrumbList = computed(() => {
  const matched = route.matched.filter(item => item.meta?.title)
  const breadcrumbs = []

  // 添加首页
  if (route.path !== '/dashboard') {
    breadcrumbs.push({
      path: '/dashboard',
      title: '首页',
      icon: 'House'
    })
  }

  // 添加匹配的路由
  matched.forEach((item, index) => {
    // 跳过根路径
    if (item.path === '/') return

    const breadcrumb = {
      path: item.path,
      title: item.meta.title,
      icon: index === 0 ? item.meta.icon : undefined
    }

    // 避免重复添加
    if (!breadcrumbs.find(b => b.path === breadcrumb.path)) {
      breadcrumbs.push(breadcrumb)
    }
  })

  // 如果当前路由有特殊的面包屑配置
  if (route.meta?.breadcrumb) {
    return route.meta.breadcrumb
  }

  return breadcrumbs
})
</script>

<style scoped>
.breadcrumb {
  font-size: 14px;
}

:deep(.el-breadcrumb__item:last-child .el-breadcrumb__inner) {
  color: var(--el-text-color-regular);
  cursor: default;
}

:deep(.el-breadcrumb__item .el-breadcrumb__inner) {
  font-weight: normal;
}

:deep(.el-breadcrumb__item .el-breadcrumb__inner:hover) {
  color: var(--el-color-primary);
}
</style>
