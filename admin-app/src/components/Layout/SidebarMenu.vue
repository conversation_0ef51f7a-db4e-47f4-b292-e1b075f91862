<template>
  <el-menu
    :default-active="activeMenu"
    :collapse="appStore.sidebarCollapsed"
    :unique-opened="true"
    router
    class="sidebar-menu"
  >
    <!-- 仪表板 -->
    <el-menu-item index="/dashboard">
      <el-icon>
        <BarChart3Icon />
      </el-icon>
      <template #title>仪表板</template>
    </el-menu-item>

    <!-- 公告管理 -->
    <el-menu-item index="/announcements">
      <el-icon>
        <MegaphoneIcon />
      </el-icon>
      <template #title>公告管理</template>
    </el-menu-item>

    <!-- 用户管理 -->
    <el-menu-item index="/users">
      <el-icon>
        <UsersIcon />
      </el-icon>
      <template #title>用户管理</template>
    </el-menu-item>

    <!-- 反馈管理 -->
    <el-menu-item index="/feedback">
      <el-icon>
        <MessageSquareIcon />
      </el-icon>
      <template #title>反馈管理</template>
    </el-menu-item>

    <!-- 积分管理 -->
    <el-menu-item index="/points">
      <el-icon>
        <CoinsIcon />
      </el-icon>
      <template #title>积分管理</template>
    </el-menu-item>

    <!-- 商店管理 -->
    <el-menu-item index="/store">
      <el-icon>
        <ShoppingBagIcon />
      </el-icon>
      <template #title>商店管理</template>
    </el-menu-item>

    <!-- 签到管理 -->
    <el-menu-item index="/checkin">
      <el-icon>
        <CalendarCheckIcon />
      </el-icon>
      <template #title>签到管理</template>
    </el-menu-item>

    <!-- 友情应用 -->
    <el-menu-item index="/friend-apps">
      <el-icon>
        <LinkIcon />
      </el-icon>
      <template #title>友情应用</template>
    </el-menu-item>

    <!-- 摸鱼状态 -->
    <el-menu-item index="/fishing-status">
      <el-icon>
        <FishIcon />
      </el-icon>
      <template #title>摸鱼状态</template>
      </el-menu-item>

      <!-- 缓存管理 -->
      <el-menu-item index="/cache">
        <el-icon>
          <DatabaseIcon />
        </el-icon>
        <template #title>缓存管理</template>
    </el-menu-item>

    <!-- 数据导出 -->
    <el-menu-item index="/data-export">
      <el-icon>
        <DownloadIcon />
      </el-icon>
      <template #title>数据导出</template>
    </el-menu-item>

    <!-- 系统设置 -->
    <el-menu-item index="/settings">
      <el-icon>
        <SettingsIcon />
      </el-icon>
      <template #title>系统设置</template>
    </el-menu-item>
  </el-menu>
</template>

<script setup>
import { computed } from 'vue'
import { useRoute } from 'vue-router'
import { useAppStore } from '@/stores/app.js'
import {
  BarChart3 as BarChart3Icon,
  Megaphone as MegaphoneIcon,
  Users as UsersIcon,
  MessageSquare as MessageSquareIcon,
  Coins as CoinsIcon,
  ShoppingBag as ShoppingBagIcon,
  CalendarCheck as CalendarCheckIcon,
  Link as LinkIcon,
  Fish as FishIcon,
  Crown as CrownIcon,
  Database as DatabaseIcon,
  Download as DownloadIcon,
  Settings as SettingsIcon
} from 'lucide-vue-next'

const route = useRoute()
const appStore = useAppStore()

// 当前激活的菜单项
const activeMenu = computed(() => {
  const { path } = route
  return path
})
</script>

<style scoped>
.sidebar-menu {
  border-right: none;
  height: 100%;
}

.sidebar-menu:not(.el-menu--collapse) {
  width: 240px;
}

:deep(.el-menu-item),
:deep(.el-sub-menu__title) {
  height: 48px;
  line-height: 48px;
  padding-left: 20px !important;
}

:deep(.el-menu-item.is-active) {
  background-color: var(--el-color-primary-light-9);
  color: var(--el-color-primary);
}

:deep(.el-menu-item:hover),
:deep(.el-sub-menu__title:hover) {
  background-color: var(--el-fill-color-light);
}

:deep(.el-sub-menu .el-menu-item) {
  padding-left: 40px !important;
  height: 40px;
  line-height: 40px;
}

:deep(.el-menu--collapse .el-menu-item),
:deep(.el-menu--collapse .el-sub-menu__title) {
  padding-left: 20px !important;
}
</style>
