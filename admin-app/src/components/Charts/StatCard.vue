<template>
  <div class="stat-card" :class="{ 'loading': loading }">
    <div class="stat-icon" :class="iconClass">
      <component :is="icon" :size="iconSize" />
    </div>
    <div class="stat-content">
      <div class="stat-value">
        <span v-if="loading" class="loading-placeholder"></span>
        <CountUp v-else :end-val="value" :duration="1.5" />
      </div>
      <div class="stat-label">{{ label }}</div>
      <div v-if="trend" class="stat-trend" :class="trend.type">
        <component :is="getTrendIcon(trend.type)" :size="12" />
        <span>{{ trend.value }}</span>
        <span class="trend-text">{{ trend.text || '较上期' }}</span>
      </div>
    </div>
    <div v-if="showChart && chartData" class="stat-chart">
      <MiniChart :data="chartData" :type="chartType" :color="chartColor" />
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import {
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  Minus as MinusIcon
} from 'lucide-vue-next'
import CountUp from 'vue-countup-v3'
import MiniChart from './MiniChart.vue'

// Props
const props = defineProps({
  label: {
    type: String,
    required: true
  },
  value: {
    type: Number,
    default: 0
  },
  icon: {
    type: [String, Object],
    required: true
  },
  iconClass: {
    type: String,
    default: ''
  },
  iconSize: {
    type: Number,
    default: 24
  },
  trend: {
    type: Object,
    default: null
    // { type: 'up|down|stable', value: '+12%', text: '较上期' }
  },
  loading: {
    type: Boolean,
    default: false
  },
  showChart: {
    type: Boolean,
    default: false
  },
  chartData: {
    type: Array,
    default: () => []
  },
  chartType: {
    type: String,
    default: 'line',
    validator: (value) => ['line', 'bar', 'area'].includes(value)
  },
  chartColor: {
    type: String,
    default: '#5470c6'
  }
})

// 计算属性
const getTrendIcon = computed(() => {
  return (type) => {
    switch (type) {
      case 'up':
        return TrendingUpIcon
      case 'down':
        return TrendingDownIcon
      case 'stable':
      default:
        return MinusIcon
    }
  }
})
</script>

<style scoped>
.stat-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.stat-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.stat-card.loading {
  opacity: 0.7;
}

.stat-card {
  display: flex;
  align-items: flex-start;
  gap: 16px;
}

.stat-icon {
  flex-shrink: 0;
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

/* 图标主题色 */
.stat-icon.users {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-icon.announcements {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-icon.feedback {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-icon.points {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-icon.vip {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

.stat-icon.checkins {
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
}

.stat-icon.default {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-content {
  flex: 1;
  min-width: 0;
}

.stat-value {
  font-size: 28px;
  font-weight: 700;
  color: #1f2937;
  line-height: 1.2;
  margin-bottom: 4px;
}

.loading-placeholder {
  display: inline-block;
  width: 80px;
  height: 28px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
  border-radius: 4px;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

.stat-label {
  font-size: 14px;
  color: #6b7280;
  font-weight: 500;
  margin-bottom: 8px;
}

.stat-trend {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  font-weight: 600;
}

.stat-trend.up {
  color: #10b981;
}

.stat-trend.down {
  color: #ef4444;
}

.stat-trend.stable {
  color: #6b7280;
}

.trend-text {
  font-weight: 400;
  opacity: 0.8;
}

.stat-chart {
  position: absolute;
  right: 16px;
  bottom: 16px;
  width: 80px;
  height: 40px;
  opacity: 0.6;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .stat-card {
    padding: 16px;
    gap: 12px;
  }
  
  .stat-icon {
    width: 40px;
    height: 40px;
  }
  
  .stat-value {
    font-size: 24px;
  }
  
  .stat-chart {
    display: none;
  }
}

@media (max-width: 480px) {
  .stat-card {
    flex-direction: column;
    text-align: center;
  }
  
  .stat-icon {
    align-self: center;
  }
}
</style>
