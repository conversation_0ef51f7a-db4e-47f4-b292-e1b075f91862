<template>
  <div class="trend-chart">
    <div class="chart-header">
      <h3>{{ title }}</h3>
      <div class="chart-controls">
        <el-select v-model="selectedPeriod" size="small" @change="handlePeriodChange">
          <el-option label="7天" value="7d" />
          <el-option label="30天" value="30d" />
          <el-option label="90天" value="90d" />
        </el-select>
      </div>
    </div>
    <div class="chart-container">
      <v-chart 
        ref="chartRef"
        :option="chartOption" 
        :style="{ height: height + 'px' }"
        autoresize
      />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { LineChart, BarChart } from 'echarts/charts'
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  DataZoomComponent
} from 'echarts/components'
import VChart from 'vue-echarts'

// 注册必要的组件
use([
  CanvasRenderer,
  LineChart,
  BarChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  DataZoomComponent
])

// Props
const props = defineProps({
  title: {
    type: String,
    default: '趋势图表'
  },
  data: {
    type: Array,
    default: () => []
  },
  type: {
    type: String,
    default: 'line', // line, bar
    validator: (value) => ['line', 'bar'].includes(value)
  },
  height: {
    type: Number,
    default: 300
  },
  period: {
    type: String,
    default: '7d'
  }
})

// Emits
const emit = defineEmits(['period-change'])

// 响应式数据
const chartRef = ref()
const selectedPeriod = ref(props.period)

// 计算属性
const chartOption = computed(() => {
  if (!props.data || props.data.length === 0) {
    return {
      title: {
        text: '暂无数据',
        left: 'center',
        top: 'middle',
        textStyle: {
          color: '#999',
          fontSize: 14
        }
      }
    }
  }

  // 提取数据
  const categories = props.data.map(item => item.date || item.name)
  const series = []

  // 获取所有数据键（除了date/name）
  const dataKeys = Object.keys(props.data[0] || {}).filter(key => 
    !['date', 'name', 'timestamp'].includes(key)
  )

  // 为每个数据键创建一个系列
  dataKeys.forEach((key, index) => {
    const values = props.data.map(item => item[key] || 0)
    
    series.push({
      name: getSeriesName(key),
      type: props.type,
      data: values,
      smooth: props.type === 'line',
      symbol: props.type === 'line' ? 'circle' : 'none',
      symbolSize: 6,
      lineStyle: props.type === 'line' ? {
        width: 2
      } : undefined,
      itemStyle: {
        color: getSeriesColor(index)
      },
      areaStyle: props.type === 'line' ? {
        opacity: 0.1
      } : undefined
    })
  })

  return {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        label: {
          backgroundColor: '#6a7985'
        }
      },
      formatter: function(params) {
        let result = `<div style="margin-bottom: 4px;">${params[0].axisValue}</div>`
        params.forEach(param => {
          result += `
            <div style="display: flex; align-items: center; margin-bottom: 2px;">
              <span style="display: inline-block; width: 10px; height: 10px; background-color: ${param.color}; border-radius: 50%; margin-right: 8px;"></span>
              <span style="margin-right: 8px;">${param.seriesName}:</span>
              <span style="font-weight: bold;">${formatValue(param.value, param.seriesName)}</span>
            </div>
          `
        })
        return result
      }
    },
    legend: {
      data: series.map(s => s.name),
      top: 10,
      textStyle: {
        fontSize: 12
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: props.type === 'bar',
      data: categories,
      axisLabel: {
        fontSize: 11,
        color: '#666'
      },
      axisLine: {
        lineStyle: {
          color: '#e0e0e0'
        }
      }
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        fontSize: 11,
        color: '#666',
        formatter: function(value) {
          return formatAxisValue(value)
        }
      },
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      splitLine: {
        lineStyle: {
          color: '#f0f0f0',
          type: 'dashed'
        }
      }
    },
    series: series,
    animation: true,
    animationDuration: 1000,
    animationEasing: 'cubicOut'
  }
})

// 方法
function handlePeriodChange(period) {
  selectedPeriod.value = period
  emit('period-change', period)
}

function getSeriesName(key) {
  const nameMap = {
    users: '用户数',
    announcements: '公告数',
    feedback: '反馈数',
    points: '积分数',
    checkIns: '签到数',
    vipUsers: 'VIP用户',
    totalEarned: '获得积分',
    totalSpent: '消费积分',
    activeUsers: '活跃用户',
    newUsers: '新用户'
  }
  return nameMap[key] || key
}

function getSeriesColor(index) {
  const colors = [
    '#5470c6', '#91cc75', '#fac858', '#ee6666',
    '#73c0de', '#3ba272', '#fc8452', '#9a60b4',
    '#ea7ccc', '#5470c6', '#91cc75', '#fac858'
  ]
  return colors[index % colors.length]
}

function formatValue(value, seriesName) {
  if (seriesName.includes('积分')) {
    return value.toLocaleString()
  }
  return value
}

function formatAxisValue(value) {
  if (value >= 10000) {
    return (value / 10000).toFixed(1) + 'w'
  } else if (value >= 1000) {
    return (value / 1000).toFixed(1) + 'k'
  }
  return value
}

// 监听器
watch(() => props.period, (newPeriod) => {
  selectedPeriod.value = newPeriod
})

// 生命周期
onMounted(() => {
  selectedPeriod.value = props.period
})
</script>

<style scoped>
.trend-chart {
  background: white;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.chart-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.chart-controls {
  display: flex;
  gap: 8px;
  align-items: center;
}

.chart-container {
  position: relative;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .chart-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .chart-controls {
    width: 100%;
    justify-content: flex-end;
  }
}
</style>
