<template>
  <div class="mini-chart">
    <v-chart 
      :option="chartOption" 
      :style="{ width: '100%', height: '100%' }"
      autoresize
    />
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { use } from 'echarts/core'
import { <PERSON>vas<PERSON>enderer } from 'echarts/renderers'
import { Line<PERSON>hart, Bar<PERSON>hart } from 'echarts/charts'
import VChart from 'vue-echarts'

// 注册必要的组件
use([
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>
])

// Props
const props = defineProps({
  data: {
    type: Array,
    default: () => []
  },
  type: {
    type: String,
    default: 'line',
    validator: (value) => ['line', 'bar', 'area'].includes(value)
  },
  color: {
    type: String,
    default: '#5470c6'
  }
})

// 计算属性
const chartOption = computed(() => {
  if (!props.data || props.data.length === 0) {
    return {}
  }

  const values = props.data.map(item => item.value || item)
  
  return {
    grid: {
      left: 0,
      right: 0,
      top: 0,
      bottom: 0
    },
    xAxis: {
      type: 'category',
      show: false,
      data: values.map((_, index) => index)
    },
    yAxis: {
      type: 'value',
      show: false
    },
    series: [{
      type: props.type === 'area' ? 'line' : props.type,
      data: values,
      smooth: props.type === 'line' || props.type === 'area',
      symbol: 'none',
      lineStyle: {
        width: 2,
        color: props.color
      },
      itemStyle: {
        color: props.color
      },
      areaStyle: props.type === 'area' ? {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [{
            offset: 0,
            color: props.color
          }, {
            offset: 1,
            color: 'transparent'
          }]
        }
      } : undefined
    }],
    animation: false
  }
})
</script>

<style scoped>
.mini-chart {
  width: 100%;
  height: 100%;
}
</style>
