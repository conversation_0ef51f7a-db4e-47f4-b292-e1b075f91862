/**
 * 路由配置
 */

import { createRouter, createWebHistory } from 'vue-router'
import routes from './routes.js'



const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach((to, from, next) => {
  // 设置页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - 后台管理系统`
  }

  // 检查是否需要认证
  if (to.meta.requiresAuth !== false) {
    // 这里可以添加认证逻辑
    // 暂时跳过认证检查
    next()
  } else {
    next()
  }
})

export default router
