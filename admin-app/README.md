# 摸鱼记账小程序 - 后台管理系统

基于 Tauri + Vue 3 + Element Plus 构建的跨平台后台管理应用，支持桌面端和移动端（Android/iOS）。

## 📋 项目概述

这是摸鱼记账小程序的后台管理系统，提供完整的数据管理、用户管理、内容管理等功能。

### 🎯 主要功能

- **📊 数据仪表板**：实时统计数据展示，包括用户数据、积分数据、反馈数据等
- **👥 用户管理**：用户信息查看、VIP管理、用户行为分析
- **💰 积分系统**：积分记录管理、积分排行榜、积分规则配置
- **🛒 商店管理**：商品管理、库存管理、销售统计
- **✅ 签到管理**：签到记录查看、签到统计分析
- **💬 反馈管理**：用户反馈处理、回复管理
- **📢 公告管理**：系统公告发布、公告统计
- **🔗 友情应用**：友情链接管理、点击统计
- **🎣 摸鱼状态**：摸鱼记录管理、状态统计
- **👑 VIP管理**：VIP用户管理、VIP记录、赠送VIP
- **⚙️ 系统设置**：系统配置管理、参数调整

### 🏗️ 技术架构

- **前端框架**：Vue 3 + Composition API
- **UI组件库**：Element Plus
- **桌面端**：Tauri (Rust)
- **移动端**：Tauri Mobile (Android/iOS)
- **构建工具**：Vite
- **包管理器**：pnpm
- **网络请求**：Tauri HTTP Plugin + Axios
- **云服务**：微信云开发

## 🚀 快速开始

### 环境要求

- **Node.js**: >= 18.0.0
- **pnpm**: >= 8.0.0
- **Rust**: >= 1.70.0
- **Tauri CLI**: >= 2.0.0

### 安装依赖

```bash
# 安装前端依赖
pnpm install

# 安装 Tauri CLI（如果未安装）
cargo install tauri-cli --version "^2.0.0"
```

### 开发模式

```bash
# 桌面端开发
pnpm tauri dev

# Android 端开发（需要 Android SDK）
pnpm tauri android dev

# iOS 端开发（需要 Xcode，仅 macOS）
pnpm tauri ios dev

# 仅前端开发（浏览器）
pnpm dev
```

### 构建应用

```bash
# 构建桌面端应用
pnpm tauri build

# 构建 Android APK
pnpm tauri android build

# 构建 iOS 应用
pnpm tauri ios build
```

## 📁 项目结构

```
admin-app/
├── src/                    # 前端源码
│   ├── api/                # API 接口
│   │   ├── wechat-api.js   # 微信云开发 API
│   │   ├── tauri-api.js    # Tauri 原生 API
│   │   └── dashboard.js    # 仪表板 API
│   ├── components/         # 公共组件
│   ├── views/              # 页面组件
│   │   ├── Dashboard/      # 仪表板
│   │   ├── Users/          # 用户管理
│   │   ├── Points/         # 积分管理
│   │   ├── Shop/           # 商店管理
│   │   ├── CheckIn/        # 签到管理
│   │   ├── Feedback/       # 反馈管理
│   │   ├── Announcements/  # 公告管理
│   │   ├── FriendApps/     # 友情应用
│   │   ├── FishingStatus/  # 摸鱼状态
│   │   ├── VIP/            # VIP管理
│   │   ├── Settings/       # 系统设置
│   │   └── Setup/          # 初始配置
│   ├── router/             # 路由配置
│   ├── stores/             # 状态管理
│   └── utils/              # 工具函数
├── src-tauri/              # Tauri 后端
│   ├── src/                # Rust 源码
│   │   ├── lib.rs          # 主模块
│   │   └── http_client.rs  # HTTP 客户端
│   ├── capabilities/       # 权限配置
│   ├── gen/                # 生成的文件
│   └── Cargo.toml          # Rust 依赖
├── public/                 # 静态资源
└── dist/                   # 构建输出
```

## 🔧 配置说明

### 微信云开发配置

首次使用需要在 Setup 页面配置微信云开发参数：

1. **AppID**: 小程序的 AppID
2. **AppSecret**: 小程序的密钥
3. **云环境ID**: 微信云开发环境ID
4. **云函数名称**: 管理端云函数名（默认：`cloud-functions-admin`）
5. **管理密钥**: 管理端访问密钥（需与云函数环境变量一致）

### 环境变量配置

在云函数中需要配置以下环境变量：

```bash
# 管理端密钥（多个密钥用逗号分隔）
ADMIN_SECRET_KEYS=MDMPADMINKEY,YOUR_CUSTOM_KEY
```

## 📱 移动端开发

### Android 开发

1. **安装 Android Studio** 和 Android SDK
2. **配置环境变量**：
   ```bash
   export ANDROID_HOME=/path/to/android-sdk
   export PATH=$PATH:$ANDROID_HOME/tools:$ANDROID_HOME/platform-tools
   ```
3. **添加 Android 目标**：
   ```bash
   rustup target add aarch64-linux-android armv7-linux-androideabi
   ```
4. **运行开发模式**：
   ```bash
   pnpm tauri android dev
   ```

### iOS 开发（仅 macOS）

1. **安装 Xcode** 和 iOS SDK
2. **添加 iOS 目标**：
   ```bash
   rustup target add aarch64-apple-ios x86_64-apple-ios
   ```
3. **运行开发模式**：
   ```bash
   pnpm tauri ios dev
   ```

## 🔌 API 接口

### 网络请求架构

应用采用双重网络请求架构：

- **Tauri 环境**：使用 Tauri HTTP Plugin 进行原生网络请求
- **浏览器环境**：使用 Axios 进行网络请求，支持开发代理

### 主要 API 模块

1. **wechat-api.js**: 微信云开发接口封装
2. **tauri-api.js**: Tauri 原生接口封装
3. **dashboard.js**: 仪表板数据接口

### 云函数调用

```javascript
import { callCloudFunction } from '@/api/wechat-api'

// 获取用户列表
const result = await callCloudFunction('getUserList', {
  page: 1,
  limit: 20,
  keyword: ''
}, {
  showSuccess: false, // 不显示成功提示
  showError: true,    // 显示错误提示
  maxRetries: 3       // 最大重试次数
})

// 获取系统统计
const stats = await callCloudFunction('getSystemStats', {}, {
  showSuccess: false,
  showError: true
})

// 更新用户状态
const updateResult = await callCloudFunction('updateUserStatus', {
  userId: 'user123',
  isVip: true
}, {
  showSuccess: true,  // 显示成功提示
  showError: true
})
```

## 🎨 UI 组件

### Element Plus 配置

项目使用 Element Plus 作为 UI 组件库，支持：

- **自动导入**：组件和样式自动按需导入
- **主题定制**：支持自定义主题色彩
- **响应式设计**：适配桌面端和移动端
- **国际化**：支持中文界面

### 自定义组件

- **StatCard**: 统计卡片组件
- **TrendChart**: 趋势图表组件
- **DataTable**: 数据表格组件
- **FormDialog**: 表单对话框组件

## 🔍 调试指南

### 开发者工具

- **桌面端**：按 F12 打开开发者工具
- **移动端**：使用 Android Studio Logcat 或 Xcode Console

### 日志查看

```javascript
// 前端日志
console.log('调试信息')

// Tauri 后端日志
println!("Rust 调试信息");
```

### 网络请求调试

应用提供详细的网络请求日志：

```
发送 Tauri HTTP 请求: https://api.weixin.qq.com/cgi-bin/token
Tauri HTTP 请求详情: {...}
Tauri HTTP 响应状态: 200
Tauri HTTP 响应数据: {...}
```

## 🚨 常见问题

### 1. Android 端网络请求失败

**问题**: 提示"缺少API类型参数"或网络请求失败

**解决方案**:
- 检查网络权限配置
- 确认 Tauri HTTP Plugin 正确安装
- 验证微信云开发配置

### 2. 配置保存失败

**问题**: 测试连接成功但配置未保存

**解决方案**:
- 检查 localStorage 权限
- 确认配置键名一致性
- 验证配置格式正确性

### 3. 云函数调用失败

**问题**: 云函数返回错误或超时

**解决方案**:
- 检查云函数部署状态
- 验证环境变量配置
- 确认网络连接稳定

## 📚 开发规范

### 代码风格

- 使用 ESLint + Prettier 进行代码格式化
- 遵循 Vue 3 Composition API 最佳实践
- 使用 TypeScript 类型注解（可选）

### 提交规范

```bash
# 功能开发
git commit -m "feat: 添加用户管理功能"

# 问题修复
git commit -m "fix: 修复Android端网络请求问题"

# 文档更新
git commit -m "docs: 更新API文档"
```

### 测试流程

1. **本地测试**: 桌面端和浏览器端功能测试
2. **移动端测试**: Android/iOS 设备真机测试
3. **云函数测试**: 验证后端接口正常工作
4. **集成测试**: 端到端功能验证

## 📄 许可证

本项目采用 MIT 许可证，详见 [LICENSE](../LICENSE) 文件。

## 🤝 贡献指南

欢迎提交 Issue 和 Pull Request 来改进项目！

1. Fork 本仓库
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📞 技术支持

如有问题，请通过以下方式联系：

- 提交 GitHub Issue
- 查看项目文档
- 参考开发规范文档
