use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use tauri::command;

#[derive(Debug, Serialize, Deserialize)]
pub struct HttpRequest {
    pub method: String,
    pub url: String,
    pub headers: Option<HashMap<String, String>>,
    pub body: Option<String>,
    pub timeout: Option<u64>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct HttpResponse {
    pub status: u16,
    pub status_text: String,
    pub headers: HashMap<String, String>,
    pub body: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct WechatConfig {
    pub app_id: String,
    pub app_secret: String,
    pub env: String,
    pub function_name: String,
    pub secret_key: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct CloudFunctionRequest {
    pub config: WechatConfig,
    pub function_type: String,
    pub data: serde_json::Value,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct CloudFunctionResponse {
    pub success: bool,
    pub message: String,
    pub data: Option<serde_json::Value>,
}

/// 配置验证命令
#[command]
pub fn validate_config(config: WechatConfig) -> Result<String, String> {
    println!("验证微信配置: {}", config.app_id);

    if config.app_id.is_empty() {
        return Err("AppID 不能为空".to_string());
    }

    if config.app_secret.is_empty() {
        return Err("AppSecret 不能为空".to_string());
    }

    if config.env.is_empty() {
        return Err("云环境ID 不能为空".to_string());
    }

    if config.function_name.is_empty() {
        return Err("云函数名称 不能为空".to_string());
    }

    if config.secret_key.is_empty() {
        return Err("管理密钥 不能为空".to_string());
    }

    Ok("配置验证通过".to_string())
}


