<!-- 日收入计算器模态框 -->
<view class="modal {{modalVisible ? 'show' : ''}}" wx:if="{{visible}}" bind:tap="onClose">
  <view class="modal-content daily-income-calculator-modal" catch:tap="onStopPropagation">
    <view class="modal-header">
      <view class="modal-title">日收入计算器</view>
      <view class="modal-close" bind:tap="onClose">×</view>
    </view>

    <view class="modal-body">
      <view class="calculator-description">
        <text class="description-text">💡 通常双休为 21.75 天，单休为 26 天</text>
      </view>

      <view class="input-group-flex">
        <!-- 月收入 -->
        <view class="input-group">
          <view class="input-label">月收入</view>
          <view class="input-with-unit">
            <input class="number-input"
                   type="digit"
                   placeholder="请输入月收入"
                   value="{{monthlyIncome}}"
                   bind:input="onMonthlyIncomeChange" />
            <text class="input-unit">{{currencyUnit}}</text>
          </view>
        </view>

        <!-- 工作天数 -->
        <view class="input-group">
          <view class="input-label">工作天数</view>
          <view class="input-with-unit">
            <input class="number-input"
                   type="digit"
                   placeholder="请输入工作天数"
                   value="{{workDays}}"
                   bind:input="onWorkDaysChange" />
            <text class="input-unit">天</text>
          </view>
        </view>
      </view>

      <!-- 计算结果 -->
      <view class="calculation-result">
        <view class="result-label">日收入</view>
        <view class="result-value">
          <text class="result-number">{{result}}</text>
          <text class="result-unit">{{currencyDailyUnit}}</text>
        </view>
      </view>
    </view>

    <view class="modal-footer">
      <view class="btn-secondary modal-btn" bind:tap="onCancel">
        <text>取消</text>
      </view>
      <view class="btn-primary modal-btn" bind:tap="onConfirm">
        <text>确定使用</text>
      </view>
    </view>
  </view>
</view>
