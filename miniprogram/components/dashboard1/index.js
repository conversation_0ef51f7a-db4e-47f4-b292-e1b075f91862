
// 简约主题组件
import { api } from '../../core/api/index.js'
const { setNavbarHeightData } = require('../../utils/navbar-height')
const { SimpleRealTimeIncome, CrossDateRealTimeIncome } = require('../../utils/real-time-income.js')
const { formatDateKey, minutesToTimeDisplay } = require('../../utils/helpers/time-utils.js')

Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 刷新间隔 (毫秒)
    refreshInterval: {
      type: Number,
      value: 1000
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    // 状态栏高度
    statusBarHeight: 0,

    // 是否显示设置模态框
    showSettings: false,

    // 仪表盘配置
    dashboardConfig: {},

     // 小数位数选项
    decimalOptions: [
      { label: '0 位', value: 0 },
      { label: '1 位', value: 1 },
      { label: '2 位', value: 2 },
      { label: '3 位', value: 3 }
    ],
    selectedDecimalIndex: 3, // 默认3位小数

    // 飘动动画选项
    floatingAnimationOptions: [
      { label: '每 +1', value: 'per_yuan' },
      { label: '每秒', value: 'per_second' },
      { label: '关闭', value: 'disabled' }
    ],
    selectedFloatingAnimationIndex: 1, // 默认选择"每秒"

    // 当前时间
    currentTime: '',
    currentDate: null,
    currentDateText: '',
    currentDateDisplay: '',

    // 工作履历相关
    currentWork: null,
    currentWorkId: null,
    currentWorkDisplayName: '',
    hasWorkHistory: false,
    currentCompany: '',
    currentPosition: '',

    // 脱敏状态
    companyMasked: false,
    positionMasked: false,

    // 显示设置
    showCurrentWork: true,

    // 收入统计
    todayIncome: '0.00',
    monthIncome: '0.00',
    todayIncomeMasked: '***.**',
    monthIncomeMasked: '***.**',

    // 当前工作日时间安排
    todaySegments: [], // 当前工作日的工作时间段
    chartSegments: [], // 图表用的时间段数据
    todayFishes: [], // 当前工作日的摸鱼记录
    currentWorkDate: null, // 当前使用的工作日期
    isUsingYesterdayData: false, // 是否正在使用昨天的数据
    workDateDisplayText: '今日', // 工作日期显示文本

    // 当前状态
    currentStatus: {
      type: 'off',
      icon: '😴',
      text: '未开始工作',
      countdown: null,
      nextStatusText: ''
    },

    // 实时收入
    currentIncome: '0.00',      // 净收入（基础收入 + 额外收入 - 扣款）
    baseIncome: '0.00',         // 基础收入（时间段收入）
    workIncome: '0.00',
    fishingIncome: '0.00',
    extraIncome: '0.00',        // 额外收入（格式化字符串）
    deductions: '0.00',         // 扣款（格式化字符串）
    extraIncomeValue: 0,        // 额外收入（数字，用于判断）
    deductionsValue: 0,         // 扣款（数字，用于判断）
    currentHourlyRate: '0.00',
    currentIncomeMasked: '***.**',
    workIncomeMasked: '***.**',
    fishingIncomeMasked: '***.**',
    currentHourlyRateMasked: '***.**',
    isWorking: false,

    // 飘动数字效果
    floatingNumbers: [],        // 当前显示的飘动数字数组
    lastIncome: 0,              // 上次的收入值（数字）
    floatingIdCounter: 0,       // 飘动元素ID计数器
    isFishing: false,
    isActive: false,

    // 每秒飘动相关
    perSecondFloatingTimer: null,  // 每秒飘动定时器
    lastSecondIncome: 0,           // 上一秒的收入值
    perSecondFloatingCounter: 0,   // 每秒飘动计数器，前2次不显示

    // 当前摸鱼状态
    currentFishingState: null, // 是否处于活跃状态（工作或摸鱼）

    // 摸鱼时间调整按钮状态
    canIncreaseFishingTime: false, // 是否可以增加摸鱼时间
    canDecreaseFishingTime: false, // 是否可以减少摸鱼时间

    // 发薪日倒计时
    payDay: '0',
    payDayName: '未设置',

    // 摸鱼人数统计
    fishingCount: 0,
    fishingCountLoading: false,
    showFishingCount: true, // 可通过设置控制显示

    // 统一信息显示
    currentInfoText: '',
    infoTextAnimation: '',
    showFishingMessage: false, // 简单的布尔值控制显示摸鱼话术还是倒计时
    fishingMessageCache: '', // 缓存的摸鱼话术，避免频繁切换
    manualShowFishingMessage: false, // 手动显示摸鱼话术的状态
    manualFishingMessageTimer: null, // 手动显示摸鱼话术的定时器
    fishingMessageTemplates: {
      // 用户在摸鱼，只有自己（count = 1, 用户在摸鱼）
      userFishingAlone: [
        '一个人的摸鱼时光 ⭐',
        '世界那么大，只有我摸鱼 🌍',
        '享受独处的摸鱼时间 🧘',
        '全世界都在卷，只有我在摸鱼躺平 🛌',
        '别人都在忙，唯我独享摸鱼时光 🕒',
        '孤独但快乐的摸鱼 🎭',
        '摸鱼界的独行侠 🦸',
        '摸鱼的小天地，只有我自己 🏠',
        '没想到只有自己在摸鱼 🐟',
        '独自摸鱼的感觉真不错 😌'
      ],

      // 用户在摸鱼，还有其他人（count > 1, 用户在摸鱼）
      userFishingWithOthers: [
        '今日摸鱼同伙 {otherCount} 人，一起划水真开心 💪',
        '摸鱼大军已达 {count} 人，你们是最棒的！',
        '摸鱼联盟成员 {count} 人集结完毕 🤝',
        '还有 {otherCount} 人在陪你摸鱼 🐟',
        '发现 {otherCount} 位摸鱼战友，继续保持！',
        '你不是一个人！还有 {otherCount} 人在摸鱼',
        '当前有 {otherCount} 人与你并肩摸鱼',
        '摸鱼总数 {count} 人，大家一起快乐划水 📈',
        '{otherCount} 个摸鱼搭子已上线，继续保持！',
        '摸鱼之路不孤单，{otherCount} 人与你同行'
      ],

      // 用户不在摸鱼，有其他人在摸鱼（count > 0, 用户不在摸鱼）
      userNotFishingButOthersAre: [
        '发现 {count} 位摸鱼高手正在行动 ',
        '当前 {count} 摸鱼人在线 📈',
        '有 {count} 人正在享受摸鱼时光 🐟',
        '摸鱼人数突破 {count}，要不要加入？',
        '观察到 {count} 个摸鱼搭子已上线',
        '发现摸鱼大军 {count} 人，要不要加入？',
        '侦测到 {count} 人正在快乐摸鱼中 🎭',
        '摸鱼联盟 {count} 人集结中，等你加入 🤝',
        '目击到 {count} 位摸鱼达人在线活动',
        '发现 {count} 人的摸鱼小分队，要不要加入？'
      ],

      // 用户不在摸鱼，也没有其他人摸鱼（count = 0, 用户不在摸鱼）
      noOneFishing: [
        '当前没有人在摸鱼，要不要成为第一个？',
        '摸鱼人数为0，是时候打破僵局了！',
        '全世界都在认真工作，只差你来摸鱼了',
        '摸鱼大军等待你的加入',
        '发现摸鱼真空地带，机会来了！',
        '摸鱼联盟急需队长，就等你了 🤝',
        '没人摸鱼的世界太无聊了',
        '摸鱼界需要一位先锋，就是你！',
        '摸鱼空窗期，正是你出手的时候'
      ]
    },

    // 货币符号和设置（默认人民币，会在loadUserSettings中更新）
    currencySymbol: '¥',
    decimalPlaces: 2,

    // 隐私脱敏状态
    privacyMask: {
      todayIncome: false,
      monthIncome: false,
      currentIncome: false,
      workIncome: false,
      fishingIncome: false,
      currentHourlyRate: false
    },

    // 新增：工作时间判断和Switch状态
    isInWorkTime: false, // 当前是否处于工作时间段
    switchLoading: false, // Switch组件加载状态

    // 当前时间段时长显示
    currentSegmentDuration: '00:00:00', // 当前时间段时长
    currentSegmentTimer: null, // 时长更新定时器
    currentSegmentStartTime: null, // 当前时间段开始时间

    // 摸鱼备注编辑
    showRemarkEditor: false, // 是否显示备注编辑器
    currentFishingRemark: '', // 当前摸鱼备注

    // 设置模态框动画状态
    settingsVisible: false,

    // 下一个时间段倒计时
    nextSegmentCountdown: null, // 下一个时间段倒计时信息
    countdownTimer: null, // 倒计时定时器
    encouragementShown: false, // 是否已经显示了鼓励话语

    // 当前时间段类型
    currentSegmentType: 'work' // work, overtime, rest
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached: function() {
      // 使用统一的导航栏高度计算工具
      setNavbarHeightData(this, {
        enableLog: true,
        logPrefix: 'Dashboard1'
      })

      this.initializeComponent()
    },

    detached: function() {
      this.cleanup()
    }
  },

  /**
   * 页面生命周期
   */
  pageLifetimes: {
    show: function() {
      // 页面显示时立即更新时间显示并启动定时器
      this.updateCurrentTime()
      this.startAutoRefresh()

      // 页面显示时重新检查摸鱼状态和启动定时器
      this.updateCurrentFishingState()
      this.startCountdownTimer()
      this.updateFishingCount()

      // 如果在摸鱼状态，启动切换定时器
      if (this.data.isFishing) {
        this.startFishingToggleTimer()
      }
    },

    hide: function() {
      // 页面隐藏时停止所有定时器
      this.cleanup()
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 初始化组件
     */
    initializeComponent: function() {
      console.log('[Dashboard1] 开始初始化组件...')

      // 初始化服务
      const app = getApp()
      this.baseService = app.getDashboardBaseService()
      this.dashboardService = app.getDashboardService()
      this.workHistoryService = app.getWorkHistoryService()
      this.incomeAdjustmentService = app.getIncomeAdjustmentService()

      // 初始化实时收入计算器
      this.realTimeIncome = new SimpleRealTimeIncome()
      this.crossDateRealTimeIncome = new CrossDateRealTimeIncome()

      // 重置收入跟踪
      this.resetIncomeTracking()

      // 设置当前日期
      const today = new Date()
      console.log('[Dashboard1] 设置当前日期:', today.toDateString())
      this.setData({
        currentDate: today,
        currentDateText: formatDateKey(today),
        currentDateDisplay: this.formatDate(today)
      })

      // 加载配置
      console.log('[Dashboard1] 加载仪表盘配置...')
      this.loadDashboardConfig()

      // 加载数据
      console.log('[Dashboard1] 开始加载数据...')
      this.loadData()

      // 启动自动刷新
      console.log('[Dashboard1] 启动自动刷新...')
      this.startAutoRefresh()

      // 启动倒计时定时器
      console.log('[Dashboard1] 启动倒计时定时器...')
      this.startCountdownTimer()

      // 启动时间段时长定时器
      console.log('[Dashboard1] 启动时间段时长定时器...')
      this.startCurrentSegmentTimer()

      // 启动摸鱼人数更新定时器
      console.log('[Dashboard1] 启动摸鱼人数更新定时器...')
      this.startFishingCountTimer()

      // 添加摸鱼自动结束监听
      console.log('[Dashboard1] 添加摸鱼自动结束监听...')
      this.addFishingAutoEndListener()

      console.log('[Dashboard1] 组件初始化完成')
    },

    /**
     * 加载数据
     */
    loadData: function() {
      console.log('[Dashboard1] 开始加载数据流程...')
      this.loadUserSettings()
      this.loadCurrentWork()
      console.log('[Dashboard1] 即将加载时间安排...')
      this.loadTodaySchedule()  // 先加载时间安排，确定使用哪天的数据
      console.log('[Dashboard1] 时间安排加载完成，即将更新统计数据...')
      this.updateStatistics()   // 再更新统计数据，使用正确的数据源
      this.updateCurrentTime()
      console.log('[Dashboard1] 数据加载流程完成')
    },

    /**
     * 加载用户设置
     */
    loadUserSettings: function() {
      try {
        const settings = this.baseService.getUserSettings()
        const currencySymbol = this.baseService.getCurrencySymbol()

        // 从仪表盘配置中获取小数位数
        const dashboardService = getApp().getDashboardService()
        const dashboardConfig = dashboardService.getDashboardSettings('dashboard1')
        const decimalPlaces = dashboardConfig.incomeDecimalPlaces !== undefined ? dashboardConfig.incomeDecimalPlaces : 3

        console.log('[DEBUG] Dashboard1 loadUserSettings:', {
          dashboardConfig: dashboardConfig,
          decimalPlaces: decimalPlaces
        })

        const privacyMask = settings.display?.privacyMask || {}

        // 设置实时收入计算器的小数位数
        if (this.realTimeIncome) {
          console.log('[DEBUG] 设置实时收入计算器小数位数:', decimalPlaces)
          this.realTimeIncome.setDecimalPlaces(decimalPlaces)
        } else {
          console.warn('Dashboard1 实时收入计算器不存在，无法设置小数位数')
        }

        // 设置跨日期实时收入计算器的小数位数
        if (this.crossDateRealTimeIncome) {
          console.log('[DEBUG] 设置跨日期实时收入计算器小数位数:', decimalPlaces)
          this.crossDateRealTimeIncome.setDecimalPlaces(decimalPlaces)
        }

        this.setData({
          currencySymbol: currencySymbol,
          decimalPlaces: decimalPlaces,
          privacyMask: {
            todayIncome: privacyMask.todayIncome || false,
            monthIncome: privacyMask.monthIncome || false,
            currentIncome: privacyMask.currentIncome || false,
            currentHourlyRate: privacyMask.currentHourlyRate || false
          }
        })

      } catch (error) {
        console.error('加载用户设置失败:', error)
      }
    },

    /**
     * 加载当前工作履历
     */
    loadCurrentWork: function() {
      try {
        const hasWorkHistory = this.baseService.hasWorkHistory()
        
        if (!hasWorkHistory) {
          this.setData({
            hasWorkHistory: false,
            currentWork: null,
            currentWorkId: null,
            currentCompany: '',
            currentPosition: ''
          })
          return
        }

        const currentWork = this.baseService.getCurrentWork()

        // 获取仪表盘配置
        const dashboardConfig = this.baseService.getDashboardConfig('dashboard1')
        const showCurrentWork = dashboardConfig.showCurrentWork !== false // 默认显示

        this.setData({
          hasWorkHistory: true,
          currentWork: currentWork,
          currentWorkId: currentWork ? currentWork.id : null,
          // 分别存储公司和职位，便于单独脱敏
          currentCompany: currentWork ? currentWork.company : '',
          currentPosition: currentWork ? currentWork.position : '',
          // 显示设置
          showCurrentWork: showCurrentWork
        })

        // 重置收入跟踪（工作切换时）
        this.resetIncomeTracking()

      } catch (error) {
        console.error('加载当前工作履历失败:', error)
      }
    },

    /**
     * 更新统计数据
     */
    updateStatistics: function() {
      console.log('[Dashboard1] 开始更新统计数据...')

      if (!this.data.hasWorkHistory || !this.data.currentWorkId) {
        console.log('[Dashboard1] 没有工作履历，设置默认值')
        this.setData({
          payDay: '0',
          payDayName: '未设置'
        })
        return
      }

      try {
        // 使用当前工作日期（可能是昨天）
        const workDate = this.data.currentWorkDate || this.data.currentDate

        console.log('[Dashboard1] 统计数据计算参数:', {
          workDate: workDate.toDateString(),
          isUsingYesterdayData: this.data.isUsingYesterdayData,
          currentDate: this.data.currentDate.toDateString()
        })

        // 获取当前工作日的数据
        let workDayData
        if (this.data.isUsingYesterdayData) {
          console.log('[Dashboard1] 使用昨天数据，计算跨日期收入...')
          workDayData = this.calculateCrossDateIncome(workDate)
        } else {
          console.log('[Dashboard1] 使用今天数据...')
          workDayData = this.baseService.getTodayData(workDate, this.data.currentWorkId)
        }

        console.log('[Dashboard1] 工作日数据:', {
          dailyIncome: workDayData.dailyIncome,
          workIncome: workDayData.workIncome,
          fishingIncome: workDayData.fishingIncome
        })

        // 获取月度数据
        const monthlyStats = this.baseService.getMonthlyStats(this.data.currentDate, this.data.currentWorkId)

        // 格式化数据
        const todayIncome = this.baseService.formatAmount(workDayData.dailyIncome, 'dashboard1')
        const monthIncome = this.baseService.formatAmount(monthlyStats.totalIncome, 'dashboard1')

        console.log('[Dashboard1] 格式化后的收入:', {
          todayIncome: todayIncome,
          monthIncome: monthIncome
        })

        // 计算发薪日倒计时
        const payDayInfo = this.workHistoryService.getNextPayDayInfo(this.data.currentWorkId)

        this.setData({
          todayIncome: todayIncome,
          monthIncome: monthIncome,
          todayIncomeMasked: this.baseService.generateMaskedText(todayIncome),
          monthIncomeMasked: this.baseService.generateMaskedText(monthIncome),
          payDay: payDayInfo.days.toString(),
          payDayName: payDayInfo.payDayName
        })

        console.log('[Dashboard1] 统计数据更新完成')

      } catch (error) {
        console.error('[Dashboard1] 更新统计数据失败:', error)
      }
    },

    /**
     * 计算跨日期收入
     * 当使用昨天数据时，需要计算从昨天开始到现在的收入
     * @param {Date} yesterdayDate - 昨天的日期
     * @returns {Object} 收入数据
     */
    calculateCrossDateIncome: function(yesterdayDate) {
      console.log('[Dashboard1] 开始计算跨日期收入...')

      try {
        const now = new Date()
        const todayStart = new Date(now.getFullYear(), now.getMonth(), now.getDate())
        const currentMs = now.getTime() - todayStart.getTime()
        const currentMinutes = Math.floor(currentMs / (60 * 1000)) // 用于日志显示

        console.log(`[Dashboard1] 当前时间: ${now.getHours()}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}.${now.getMilliseconds().toString().padStart(3, '0')} (${currentMs}毫秒)`)

        // 获取昨天的完整数据
        const yesterdayData = this.baseService.getTodayData(yesterdayDate, this.data.currentWorkId)

        console.log('[Dashboard1] 昨天的原始数据:', {
          dailyIncome: yesterdayData.dailyIncome,
          segments: yesterdayData.segments,
          fishes: yesterdayData.fishes
        })

        // 找到当前时间所在的跨日期时间段
        let crossDateSegment = null

        // 首先尝试找到当前时间所在的时间段
        for (const segment of yesterdayData.segments) {
          const isCrossDate = segment.start >= 1440 || segment.end >= 1440
          if (isCrossDate) {
            // 检查当前时间是否在这个时间段内
            if (segment.start >= 1440) {
              // 整个时间段都在次日
              const startTimeToday = segment.start - 1440
              const endTimeToday = segment.end >= 1440 ? segment.end - 1440 : segment.end
              if (currentMinutes >= startTimeToday && currentMinutes <= endTimeToday) {
                crossDateSegment = segment
                break
              }
            } else {
              // 时间段跨越两天
              const endTimeToday = segment.end >= 1440 ? segment.end - 1440 : segment.end
              if (currentMinutes <= endTimeToday) {
                crossDateSegment = segment
                break
              }
            }
          }
        }

        // 如果没找到当前时间所在的时间段，使用第一个跨日期时间段
        if (!crossDateSegment) {
          crossDateSegment = yesterdayData.segments.find(segment =>
            segment.start >= 1440 || segment.end >= 1440
          )
        }

        if (!crossDateSegment) {
          console.log('[Dashboard1] 没有找到跨日期时间段，返回昨天原始数据')
          return yesterdayData
        }

        const startTimeDisplay = minutesToTimeDisplay(crossDateSegment.start)
        const endTimeDisplay = minutesToTimeDisplay(crossDateSegment.end)
        console.log(`[Dashboard1] 找到跨日期时间段: ${startTimeDisplay}-${endTimeDisplay}`)

        // 计算跨日期的总收入（包含昨天已完成的时间段 + 当前时间段的已工作部分）
        let totalCrossDateIncome = 0
        let totalWorkIncome = 0
        let totalFishingIncome = 0

        // 1. 计算昨天已完成的所有时间段收入
        for (const segment of yesterdayData.segments) {
          const isSegmentCrossDate = segment.start >= 1440 || segment.end >= 1440

          if (!isSegmentCrossDate) {
            // 昨天的普通时间段，全部计入收入
            totalCrossDateIncome += segment.income || 0
            console.log(`[Dashboard1] 昨天已完成时间段: ${minutesToTimeDisplay(segment.start)}-${minutesToTimeDisplay(segment.end)}, 收入: ${segment.income}`)
          } else if (segment !== crossDateSegment) {
            // 昨天的其他跨日期时间段（已完成的）
            totalCrossDateIncome += segment.income || 0
            console.log(`[Dashboard1] 昨天已完成跨日期时间段: ${minutesToTimeDisplay(segment.start)}-${minutesToTimeDisplay(segment.end)}, 收入: ${segment.income}`)
          }
        }

        // 2. 计算当前跨日期时间段的已工作收入（毫秒级精度，扣除摸鱼时间）
        let currentSegmentWorkedIncome = 0
        let currentSegmentFishingIncome = 0

        if (crossDateSegment.income) {
          const segmentTotalMs = (crossDateSegment.end - crossDateSegment.start) * 60 * 1000 // 时间段总毫秒数
          let workedMs = 0

          if (crossDateSegment.start >= 1440) {
            // 整个时间段都在次日
            const startTimeTodayMs = (crossDateSegment.start - 1440) * 60 * 1000
            workedMs = currentMs - startTimeTodayMs
            workedMs = Math.max(0, workedMs)
          } else {
            // 时间段跨越两天
            const yesterdayMs = (1440 - crossDateSegment.start) * 60 * 1000 // 昨天部分毫秒数
            const endTimeTodayMs = (crossDateSegment.end >= 1440 ? crossDateSegment.end - 1440 : crossDateSegment.end) * 60 * 1000
            const todayMs = Math.min(currentMs, endTimeTodayMs) // 今天部分毫秒数
            workedMs = yesterdayMs + Math.max(0, todayMs)
          }

          // 计算当前时间段内的摸鱼时间（毫秒）
          let fishingMsInCurrentSegment = 0

          // 检查已完成的摸鱼记录
          if (yesterdayData.fishes && yesterdayData.fishes.length > 0) {
            yesterdayData.fishes.forEach(fish => {
              if (fish.start >= crossDateSegment.start && fish.end <= crossDateSegment.end) {
                const fishingMinutes = fish.end - fish.start
                fishingMsInCurrentSegment += fishingMinutes * 60 * 1000
              }
            })
          }

          // 检查正在进行的摸鱼（如果有）
          const dataManager = getApp().getDataManager()
          const currentFishingState = dataManager.getCurrentFishingState()
          if (currentFishingState && currentFishingState.isActive) {
            const fishingStartTime = new Date(currentFishingState.startTime)
            const fishingStartMs = fishingStartTime.getTime() - todayStart.getTime()

            // 检查摸鱼是否在当前跨日期时间段内
            const segmentStartMs = crossDateSegment.start >= 1440 ?
              (crossDateSegment.start - 1440) * 60 * 1000 :
              (1440 - crossDateSegment.start) * 60 * 1000
            const segmentEndMs = crossDateSegment.end >= 1440 ?
              (crossDateSegment.end - 1440) * 60 * 1000 :
              crossDateSegment.end * 60 * 1000

            if (fishingStartMs >= segmentStartMs && fishingStartMs <= segmentEndMs) {
              const currentFishingMs = currentMs - fishingStartMs
              fishingMsInCurrentSegment += Math.max(0, currentFishingMs)
            }
          }

          // 实际工作时间 = 已工作时间 - 摸鱼时间
          const actualWorkedMs = Math.max(0, workedMs - fishingMsInCurrentSegment)

          // 按实际工作时间比例计算工作收入
          currentSegmentWorkedIncome = crossDateSegment.income * (actualWorkedMs / segmentTotalMs)

          // 按摸鱼时间比例计算摸鱼收入
          currentSegmentFishingIncome = crossDateSegment.income * (fishingMsInCurrentSegment / segmentTotalMs)
        }

        // 3. 合并工作收入（只计算实际工作时间的收入）
        totalWorkIncome += currentSegmentWorkedIncome

        // 4. 计算已完成时间段的摸鱼收入
        if (yesterdayData.fishes && yesterdayData.fishes.length > 0) {
          yesterdayData.fishes.forEach(fish => {
            // 找到摸鱼所在的时间段
            const fishSegment = yesterdayData.segments.find(segment =>
              fish.start >= segment.start && fish.end <= segment.end
            )

            if (fishSegment && fishSegment.income) {
              // 只计算非当前跨日期时间段的摸鱼收入（当前时间段的摸鱼收入已在上面计算）
              if (fishSegment !== crossDateSegment) {
                const fishDuration = fish.end - fish.start
                const segmentDuration = fishSegment.end - fishSegment.start
                const fishIncome = fishSegment.income * (fishDuration / segmentDuration)
                totalFishingIncome += fishIncome
              }
            }
          })
        }

        // 5. 加上当前时间段的摸鱼收入
        totalFishingIncome += currentSegmentFishingIncome

        // 6. 计算总收入
        totalCrossDateIncome = totalWorkIncome + totalFishingIncome

        console.log('[Dashboard1] 跨日期收入汇总:', {
          totalCrossDateIncome: totalCrossDateIncome.toFixed(2),
          totalWorkIncome: totalWorkIncome.toFixed(2),
          totalFishingIncome: totalFishingIncome.toFixed(2)
        })

        const result = {
          dailyIncome: totalCrossDateIncome,
          workIncome: totalWorkIncome,
          fishingIncome: totalFishingIncome,
          segments: yesterdayData.segments,
          fishes: yesterdayData.fishes || []
        }

        console.log('[Dashboard1] 跨日期收入计算结果:', result)

        return result

      } catch (error) {
        console.error('[Dashboard1] 计算跨日期收入失败:', error)
        // 出错时返回昨天的数据
        return this.baseService.getTodayData(yesterdayDate, this.data.currentWorkId)
      }
    },

    /**
     * 确定当前应该使用的工作日期
     * 考虑跨日期时间段的情况
     * @returns {Object} { date: Date, isYesterday: boolean, displayText: string }
     */
    determineCurrentWorkDate: function() {
      console.log('[Dashboard1] 开始确定当前工作日期...')

      if (!this.data.hasWorkHistory || !this.data.currentWorkId) {
        console.log('[Dashboard1] 没有工作履历，使用今日数据')
        const today = new Date()
        return {
          date: today,
          isYesterday: false,
          displayText: '今日'
        }
      }

      const now = new Date()
      const today = new Date(now)
      const yesterday = new Date(now)
      yesterday.setDate(yesterday.getDate() - 1)

      const currentMinutes = now.getHours() * 60 + now.getMinutes()

      console.log(`[Dashboard1] 当前时间: ${now.getHours()}:${now.getMinutes().toString().padStart(2, '0')} (${currentMinutes}分钟)`)
      console.log(`[Dashboard1] 今天日期: ${today.toDateString()}`)
      console.log(`[Dashboard1] 昨天日期: ${yesterday.toDateString()}`)

      try {
        // 获取昨天的数据
        console.log('[Dashboard1] 尝试获取昨天的工作安排...')
        const yesterdayData = this.baseService.getTodayData(yesterday, this.data.currentWorkId)

        console.log('[Dashboard1] 昨天的数据:', {
          hasSegments: !!(yesterdayData.segments && yesterdayData.segments.length > 0),
          segmentsCount: yesterdayData.segments ? yesterdayData.segments.length : 0,
          segments: yesterdayData.segments
        })

        if (yesterdayData.segments && yesterdayData.segments.length > 0) {
          // 检查昨天是否有跨日期的时间段
          console.log('[Dashboard1] 检查昨天是否有跨日期时间段...')

          for (let i = 0; i < yesterdayData.segments.length; i++) {
            const segment = yesterdayData.segments[i]
            const startTimeDisplay = minutesToTimeDisplay(segment.start)
            const endTimeDisplay = minutesToTimeDisplay(segment.end)
            console.log(`[Dashboard1] 检查时间段 ${i}: ${startTimeDisplay}-${endTimeDisplay} (${segment.start}-${segment.end}分钟)`)

            // 跨日期判断：开始时间或结束时间超过24小时（1440分钟）
            const isCrossDate = segment.start >= 1440 || segment.end >= 1440
            if (isCrossDate) {
              const startTimeDisplay = minutesToTimeDisplay(segment.start)
              const endTimeDisplay = minutesToTimeDisplay(segment.end)
              console.log(`[Dashboard1] 发现跨日期时间段: ${startTimeDisplay}-${endTimeDisplay}`)

              // 将跨日期时间段的结束时间转换为当天时间（减去24小时）
              const endTimeToday = segment.end >= 1440 ? segment.end - 1440 : segment.end

              // 如果当前时间在跨日期时间段的结束时间之前，使用昨天的数据
              if (currentMinutes <= endTimeToday) {
                console.log(`[Dashboard1] 当前时间(${currentMinutes})在跨日期时间段结束时间(${endTimeToday})之前，使用昨天数据`)
                return {
                  date: yesterday,
                  isYesterday: true,
                  displayText: '昨日到现在'
                }
              } else {
                console.log(`[Dashboard1] 当前时间(${currentMinutes})已超过跨日期时间段结束时间(${endTimeToday})，使用今日数据`)
              }
            }
          }

          console.log('[Dashboard1] 昨天没有跨日期时间段，使用今日数据')
        } else {
          console.log('[Dashboard1] 昨天没有工作安排，使用今日数据')
        }
      } catch (error) {
        console.error('[Dashboard1] 检查昨天数据失败:', error)
      }

      // 默认使用今天的数据
      console.log('[Dashboard1] 最终决定使用今日数据')
      return {
        date: today,
        isYesterday: false,
        displayText: '今日'
      }
    },

    /**
     * 加载当前工作日的时间安排
     */
    loadTodaySchedule: function() {
      console.log('[Dashboard1] 开始加载工作日时间安排...')

      if (!this.data.hasWorkHistory || !this.data.currentWorkId) {
        console.log('[Dashboard1] 没有工作履历，清空时间安排')
        this.setData({
          todaySegments: [],
          chartSegments: [],
          currentWorkDate: null,
          workDateDisplayText: '今日',
          isUsingYesterdayData: false
        })
        return
      }

      try {
        // 确定当前应该使用的工作日期
        console.log('[Dashboard1] 确定当前工作日期...')
        const workDateInfo = this.determineCurrentWorkDate()

        console.log('[Dashboard1] 工作日期信息:', {
          date: workDateInfo.date.toDateString(),
          isYesterday: workDateInfo.isYesterday,
          displayText: workDateInfo.displayText
        })

        // 获取工作日数据
        console.log('[Dashboard1] 获取工作日数据...')
        const workData = this.baseService.getTodayData(workDateInfo.date, this.data.currentWorkId)

        console.log('[Dashboard1] 工作日数据:', {
          hasSegments: !!(workData.segments && workData.segments.length > 0),
          segmentsCount: workData.segments ? workData.segments.length : 0,
          segments: workData.segments,
          hasFishes: !!(workData.fishes && workData.fishes.length > 0),
          fishesCount: workData.fishes ? workData.fishes.length : 0
        })

        // 为时间图表组件准备数据
        const chartSegments = workData.segments.map(segment => {
          return Object.assign({}, segment)
        })

        console.log('[Dashboard1] 设置数据到组件状态...')
        this.setData({
          todaySegments: workData.segments,
          chartSegments: chartSegments,
          todayFishes: workData.fishes || [],
          currentWorkDate: workDateInfo.date,
          isUsingYesterdayData: workDateInfo.isYesterday,
          workDateDisplayText: workDateInfo.displayText
        })

        console.log('[Dashboard1] 工作日时间安排加载完成:', {
          segmentsCount: workData.segments.length,
          isUsingYesterdayData: workDateInfo.isYesterday,
          displayText: workDateInfo.displayText
        })

      } catch (error) {
        console.error('[Dashboard1] 加载工作日时间安排失败:', error)
      }
    },

    /**
     * 更新当前时间
     */
    updateCurrentTime: function() {
      const now = new Date()

      this.setData({
        currentTime: this.formatTime(now),
        currentDate: now,
        currentDateText: formatDateKey(now),
        currentDateDisplay: this.formatDate(now)
      })

      // 更新当前状态
      this.updateCurrentStatus(now)

      // 获取当前摸鱼状态（不重启定时器）
      this.refreshCurrentFishingState()

      // 总是更新实时收入（方法内部会判断是否在工作）
      this.updateCurrentIncome(now)
    },

    /**
     * 更新当前状态
     */
    updateCurrentStatus: function(now) {
      const currentStatus = this.baseService.getCurrentWorkStatus(now, this.data.todaySegments)

      // 判断当前是否处于工作时间段
      const isInWorkTime = this.checkIsInWorkTime(now)

      // 获取当前时间段类型
      const currentSegmentType = this.getCurrentSegmentType(now)

      this.setData({
        currentStatus: currentStatus,
        isWorking: currentStatus.isWorking,
        isInWorkTime: isInWorkTime,
        currentSegmentType: currentSegmentType
      })
    },

    /**
     * 检查当前是否处于工作时间段
     */
    checkIsInWorkTime: function(now) {
      if (!this.data.hasWorkHistory || !this.data.todaySegments || this.data.todaySegments.length === 0) {
        return false
      }

      // 将当前时间转换为秒数，提供更精确的时间检测
      const currentSeconds = now.getHours() * 3600 + now.getMinutes() * 60 + now.getSeconds()

      // 检查是否在任何工作时间段内（非休息时间段）
      const inWorkSegment = this.data.todaySegments.find(segment => {
        if (segment.type === 'rest') {
          return false
        }

        // 将时间段的分钟数转换为秒数
        const segmentStartSeconds = segment.start * 60
        const segmentEndSeconds = segment.end * 60

        // 处理跨日期时间段
        const isCrossDate = segment.start >= 1440 || segment.end >= 1440
        if (isCrossDate) {
          // 跨日期时间段
          if (this.data.isUsingYesterdayData) {
            // 使用昨天数据：检查当前时间是否在结束时间之前
            const endTimeToday = segment.end >= 1440 ? segment.end - 1440 : segment.end
            const endTimeTodaySeconds = endTimeToday * 60
            return currentSeconds < endTimeTodaySeconds
          } else {
            // 使用今天数据：检查当前时间是否在开始时间之后
            const startTimeToday = segment.start >= 1440 ? segment.start - 1440 : segment.start
            const startTimeTodaySeconds = startTimeToday * 60
            return currentSeconds >= startTimeTodaySeconds
          }
        } else {
          // 普通时间段：使用秒级精确比较
          return currentSeconds >= segmentStartSeconds && currentSeconds < segmentEndSeconds
        }
      })

      return !!inWorkSegment
    },

    /**
     * 获取当前时间段类型
     */
    getCurrentSegmentType: function(now) {
      // 如果正在摸鱼，返回摸鱼类型
      if (this.data.isFishing) {
        return 'fishing'
      }

      if (!this.data.hasWorkHistory || !this.data.todaySegments || this.data.todaySegments.length === 0) {
        return 'work'
      }

      // 将当前时间转换为秒数，提供更精确的时间检测
      const currentSeconds = now.getHours() * 3600 + now.getMinutes() * 60 + now.getSeconds()

      // 查找当前所在的时间段
      const currentSegment = this.data.todaySegments.find(segment => {
        const segmentStartSeconds = segment.start * 60
        const segmentEndSeconds = segment.end * 60
        return currentSeconds >= segmentStartSeconds &&
               currentSeconds <= segmentEndSeconds
      })

      return currentSegment ? currentSegment.type : 'work'
    },

    /**
     * 计算下一个时间段倒计时
     */
    calculateNextSegmentCountdown: function(now) {
      if (!this.data.hasWorkHistory || !this.data.todaySegments || this.data.todaySegments.length === 0) {
        this.setData({
          nextSegmentCountdown: null
        })

        // 更新统一信息显示
        this.updateUnifiedInfo()
        return
      }

      // 使用秒级精度进行更准确的时间计算
      const currentSeconds = now.getHours() * 3600 + now.getMinutes() * 60 + now.getSeconds()
      const segments = this.data.todaySegments

      // 找到下一个时间段
      let nextSegment = null
      let nextSegmentType = ''
      let targetSeconds = 0

      // 首先查找今天剩余的时间段
      for (let i = 0; i < segments.length; i++) {
        const segment = segments[i]
        const segmentStartSeconds = segment.start * 60
        const segmentEndSeconds = segment.end * 60

        // 如果当前在这个时间段内，找下一个时间段
        if (currentSeconds >= segmentStartSeconds && currentSeconds < segmentEndSeconds) {
          // 当前在时间段内，下一个目标是这个时间段的结束
          targetSeconds = segmentEndSeconds

          // 判断下一个状态
          if (i + 1 < segments.length) {
            const nextSeg = segments[i + 1]
            nextSegmentType = this.getSegmentTypeName(nextSeg.type)
          } else {
            nextSegmentType = '下班'
          }
          break
        } else if (currentSeconds < segmentStartSeconds) {
          // 当前在时间段之前，下一个目标是这个时间段的开始
          targetSeconds = segmentStartSeconds
          nextSegmentType = this.getSegmentTypeName(segment.type)
          break
        }
      }

      // 如果没有找到，说明已经过了所有时间段
      if (!nextSegmentType) {
        nextSegmentType = '下班'
        // 找到最后一个时间段的结束时间
        if (segments.length > 0) {
          targetSeconds = segments[segments.length - 1].end * 60
        }
      }

      // 计算精确的剩余时间（秒级精度）
      const remainingTotalSeconds = targetSeconds - currentSeconds

      // 检查是否已经过了所有时间段
      const lastSegment = segments[segments.length - 1]
      const lastSegmentEndSeconds = lastSegment.end * 60
      if (currentSeconds >= lastSegmentEndSeconds) {
        // 已经过了所有时间段，显示鼓励话语
        if (!this.data.encouragementShown) {
          const encouragements = [
            '今日安排已结束，好好休息吧！🌙',
            '辛苦了一天，是时候放松一下了！✨',
            '今天的工作完成了，享受属于你的时间！🎉',
            '工作结束，记得好好照顾自己哦！💖',
            '今日任务达成，为自己点个赞！👏'
          ]
          const randomEncouragement = encouragements[Math.floor(Math.random() * encouragements.length)]

          this.setData({
            nextSegmentCountdown: {
              type: 'finished',
              message: randomEncouragement,
              isFinished: true
            },
            encouragementShown: true
          })

          // 更新统一信息显示
          this.updateUnifiedInfo()
        }
        return
      }

      // 如果剩余时间为0或负数，说明已经到点了
      if (remainingTotalSeconds <= 0) {
        this.setData({
          nextSegmentCountdown: null,
          encouragementShown: false
        })

        // 更新统一信息显示
        this.updateUnifiedInfo()
        return
      }

      // 格式化倒计时（基于总秒数）
      const hours = Math.floor(remainingTotalSeconds / 3600)
      const minutes = Math.floor((remainingTotalSeconds % 3600) / 60)
      const seconds = remainingTotalSeconds % 60

      const formattedTime = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`

      this.setData({
        nextSegmentCountdown: {
          type: nextSegmentType,
          time: formattedTime,
          totalMinutes: Math.ceil(remainingTotalSeconds / 60),
          isFinished: false
        }
      })

      // 更新统一信息显示
      this.updateUnifiedInfo()
    },

    /**
     * 获取时间段类型的中文名称
     */
    getSegmentTypeName: function(type) {
      switch (type) {
        case 'work':
          return '工作'
        case 'rest':
          return '休息'
        case 'overtime':
          return '加班'
        default:
          return '工作'
      }
    },

    /**
     * 启动倒计时定时器
     */
    startCountdownTimer: function() {
      // 清除现有定时器
      this.stopCountdownTimer()

      // 立即更新一次
      this.calculateNextSegmentCountdown(new Date())

      // 启动定时器，每秒更新
      this.data.countdownTimer = setInterval(() => {
        this.calculateNextSegmentCountdown(new Date())
      }, 1000)
    },

    /**
     * 停止倒计时定时器
     */
    stopCountdownTimer: function() {
      if (this.data.countdownTimer) {
        clearInterval(this.data.countdownTimer)
        this.setData({
          countdownTimer: null,
          nextSegmentCountdown: null
        })
      }
    },

    /**
     * 更新当前摸鱼状态（包含定时器管理）
     */
    updateCurrentFishingState: function() {
      try {
        const dataManager = getApp().getDataManager()
        const fishingState = dataManager.getCurrentFishingState()

        this.setData({
          currentFishingState: fishingState,
          currentFishingRemark: fishingState ? (fishingState.remark || '') : '',
          isFishing: !!(fishingState && fishingState.isActive)
        })

        // 更新摸鱼时间调整按钮状态
        this.updateFishingTimeAdjustButtons()

        // 重启当前时间段时长定时器（因为状态可能发生了变化）
        this.restartCurrentSegmentTimer()
      } catch (error) {
        console.error('获取摸鱼状态失败:', error)
        this.setData({
          currentFishingState: null,
          isFishing: false
        })
        this.stopCurrentSegmentTimer()
      }
    },

    /**
     * 刷新当前摸鱼状态（仅更新数据，不管理定时器）
     */
    refreshCurrentFishingState: function() {
      try {
        const dataManager = getApp().getDataManager()
        const fishingState = dataManager.getCurrentFishingState()

        this.setData({
          currentFishingState: fishingState,
          currentFishingRemark: fishingState ? (fishingState.remark || '') : '',
          isFishing: !!(fishingState && fishingState.isActive)
        })
      } catch (error) {
        console.error('获取摸鱼状态失败:', error)
        this.setData({
          currentFishingState: null,
          isFishing: false
        })
      }
    },

    /**
     * 启动当前时间段时长定时器
     */
    startCurrentSegmentTimer: function() {
      // 如果定时器已经在运行，不要重复启动
      if (this.data.currentSegmentTimer) {
        return
      }

      // 确定当前时间段的开始时间
      this.determineCurrentSegmentStartTime()

      // 立即更新一次
      this.updateCurrentSegmentDuration()

      // 启动定时器，每秒更新
      this.data.currentSegmentTimer = setInterval(() => {
        this.updateCurrentSegmentDuration()
      }, 1000)
    },

    /**
     * 停止当前时间段时长定时器
     */
    stopCurrentSegmentTimer: function() {
      if (this.data.currentSegmentTimer) {
        clearInterval(this.data.currentSegmentTimer)
        this.setData({
          currentSegmentTimer: null,
          currentSegmentDuration: '00:00:00',
          currentSegmentStartTime: null
        })
      }
    },

    /**
     * 强制重启当前时间段时长定时器（用于状态切换）
     */
    restartCurrentSegmentTimer: function() {
      this.stopCurrentSegmentTimer()
      this.startCurrentSegmentTimer()
    },

    /**
     * 确定当前时间段的开始时间
     */
    determineCurrentSegmentStartTime: function() {
      const now = new Date()

      // 如果正在摸鱼，使用摸鱼开始时间
      if (this.data.isFishing && this.data.currentFishingState) {
        this.setData({
          currentSegmentStartTime: new Date(this.data.currentFishingState.startTime)
        })
        return
      }

      // 否则使用当前时间段的开始时间（包括休息时间段）
      if (this.data.todaySegments && this.data.todaySegments.length > 0) {
        const currentMinutes = now.getHours() * 60 + now.getMinutes()
        const currentSegment = this.data.todaySegments.find(segment => {
          return currentMinutes >= segment.start &&
                 currentMinutes <= segment.end
        })

        if (currentSegment) {
          // 计算时间段开始时间
          const segmentStartTime = new Date(now)
          segmentStartTime.setHours(Math.floor(currentSegment.start / 60))
          segmentStartTime.setMinutes(currentSegment.start % 60)
          segmentStartTime.setSeconds(0)
          segmentStartTime.setMilliseconds(0)

          this.setData({
            currentSegmentStartTime: segmentStartTime
          })
          return
        }
      }

      // 默认使用当前时间
      this.setData({
        currentSegmentStartTime: now
      })
    },

    /**
     * 更新当前时间段时长显示
     */
    updateCurrentSegmentDuration: function() {
      if (!this.data.currentSegmentStartTime) {
        return
      }

      let formattedDuration

      // 如果在摸鱼状态，使用统一的摸鱼时长计算
      if (this.data.isFishing) {
        const dataManager = getApp().getDataManager()
        formattedDuration = dataManager.getCurrentFishingDurationString()
      } else {
        // 普通工作时间段的时长计算
        const now = new Date()
        const startTime = this.data.currentSegmentStartTime
        const durationMs = now.getTime() - startTime.getTime()
        const durationSeconds = Math.floor(durationMs / 1000)

        const hours = Math.floor(durationSeconds / 3600)
        const minutes = Math.floor((durationSeconds % 3600) / 60)
        const seconds = durationSeconds % 60

        formattedDuration = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
      }

      // 获取当前时间段类型
      const now = new Date()
      const currentSegmentType = this.getCurrentSegmentType(now)

      this.setData({
        currentSegmentDuration: formattedDuration,
        currentSegmentType: currentSegmentType
      })

      // 如果在摸鱼状态，更新时间调整按钮状态
      if (this.data.isFishing) {
        this.updateFishingTimeAdjustButtons()
      }
    },

    /**
     * 处理自动结束摸鱼
     * @param {Object} fishingState - 摸鱼状态
     * @param {number} endMinutes - 结束时间（分钟数）
     */
    handleAutoEndFishing: function(fishingState, endMinutes) {
      try {
        const dataManager = getApp().getDataManager()

        // 创建摸鱼记录
        const fishingRecord = {
          id: 0,
          start: fishingState.startMinutes,
          end: endMinutes,
          remark: (fishingState.remark || '') + ' (自动结束)'
        }

        // 保存摸鱼记录
        // 使用本地时间创建日期对象，避免时区问题
        const dateParts = fishingState.date.split('-')
        const date = new Date(parseInt(dateParts[0]), parseInt(dateParts[1]) - 1, parseInt(dateParts[2]))
        dataManager.addFishing(fishingState.workId, date, fishingRecord)

        // 清除摸鱼状态
        dataManager.fishingManager.clearFishingState()

        // 立即更新UI状态
        this.setData({
          currentFishingState: null,
          isFishing: false,
          isActive: this.data.isWorking // 只保留工作状态
        })

        // 通知fishing-control组件刷新状态
        const fishingControl = this.selectComponent('#fishing-control')
        if (fishingControl) {
          fishingControl.refresh()
        }

        // 只更新必要的数据，避免触发完整的数据重新加载
        this.updateStatistics()
        this.updateCurrentTime()

        // 注意：不调用 dataManager.notifyChange() 和 this.loadTodaySchedule()
        // 以避免触发无限循环

        // 显示提示
        wx.showToast({
          title: '摸鱼已自动结束',
          icon: 'none',
          duration: 2000
        })

      } catch (error) {
        console.error('自动结束摸鱼失败:', error)
      }
    },

    /**
     * 更新实时收入 - 重构版本
     */
    updateCurrentIncome: function(now) {
      if (!this.data.hasWorkHistory || !this.data.currentWorkId) {

        // 停止实时更新
        if (this.realTimeIncome) {
          this.realTimeIncome.stop()
        }

        // 停止每秒飘动
        this.stopPerSecondFloating()

        // 显示0
        this.setData({
          currentIncome: '0.00',
          baseIncome: '0.00',
          workIncome: '0.00',
          fishingIncome: '0.00',
          extraIncome: '0.00',
          deductions: '0.00',
          extraIncomeValue: 0,
          deductionsValue: 0,
          currentHourlyRate: '0.00',
          currentIncomeMasked: '****',
          workIncomeMasked: '****',
          fishingIncomeMasked: '****',
          currentHourlyRateMasked: '****',
          isWorking: false,
          isFishing: false,
          isActive: false
        })
        return
      }

      // 管理每秒飘动定时器
      const floatingMode = this.data.dashboardConfig.floatingAnimationMode || 'per_yuan'
      if (floatingMode === 'per_second') {
        // 如果是每秒模式且定时器未启动，启动定时器
        if (!this.data.perSecondFloatingTimer) {
          // 在启动定时器前，先将 lastSecondIncome 设置为当前收入，避免显示整个收入
          this.setData({
            lastSecondIncome: this.data.lastIncome
          })
          this.startPerSecondFloating()
        }
      } else {
        // 如果不是每秒模式，停止定时器
        this.stopPerSecondFloating()
      }

      // 使用简化的实时收入计算器
      if (!this.realTimeIncome) {
        console.error('Dashboard1 实时收入计算器不存在')
        return
      }

      // 获取必要数据
      const currentWork = this.data.currentWork

      // 减少日志输出频率，只在状态变化时输出
      if (this.lastLoggedState !== JSON.stringify({
        isUsingYesterdayData: this.data.isUsingYesterdayData,
        currentWorkDate: this.data.currentWorkDate ? this.data.currentWorkDate.toDateString() : null,
        currentDate: this.data.currentDate.toDateString()
      })) {
        console.log('[Dashboard1] 更新实时收入，当前状态:', {
          isUsingYesterdayData: this.data.isUsingYesterdayData,
          currentWorkDate: this.data.currentWorkDate ? this.data.currentWorkDate.toDateString() : null,
          currentDate: this.data.currentDate.toDateString()
        })
        this.lastLoggedState = JSON.stringify({
          isUsingYesterdayData: this.data.isUsingYesterdayData,
          currentWorkDate: this.data.currentWorkDate ? this.data.currentWorkDate.toDateString() : null,
          currentDate: this.data.currentDate.toDateString()
        })
      }

      // 使用当前工作日期的数据
      let workDayData
      if (this.data.isUsingYesterdayData && this.data.currentWorkDate) {
        console.log('[Dashboard1] 实时收入使用昨天数据，计算跨日期收入...')
        workDayData = this.calculateCrossDateIncome(this.data.currentWorkDate)

        // 对于跨日期情况，使用专用的跨日期实时收入计算器
        console.log('[Dashboard1] 启动跨日期实时收入计算器...')

        // 设置小数位数
        const decimalPlaces = this.baseService.getDecimalPlaces('dashboard1')
        this.crossDateRealTimeIncome.setDecimalPlaces(decimalPlaces)

        // 启动跨日期实时收入更新
        this.crossDateRealTimeIncome.start(
          (result) => {
            // 获取工作日期的收入调整数据（跨日期情况使用工作日期）
            const adjustmentSummary = this.incomeAdjustmentService.getDayAdjustmentSummary(
              this.data.currentWorkDate,
              this.data.currentWorkId
            )

            // 计算净收入（基础收入 + 额外收入 - 扣款）
            const baseIncome = parseFloat(result.formattedDailyIncome) || 0
            const extraIncome = adjustmentSummary.extraIncome || 0
            const deductions = adjustmentSummary.deductions || 0
            const netIncome = baseIncome + extraIncome - deductions

            // 格式化净收入
            const formattedNetIncome = this.baseService.formatAmount(netIncome, 'dashboard1')

            this.setData({
              currentIncome: formattedNetIncome,  // 显示净收入
              baseIncome: result.formattedDailyIncome,  // 保留基础收入
              workIncome: result.formattedWorkIncome,
              fishingIncome: result.formattedFishingIncome,
              extraIncome: this.baseService.formatAmount(extraIncome, 'dashboard1'),
              deductions: this.baseService.formatAmount(deductions, 'dashboard1'),
              // 新增：数字类型的判断字段
              extraIncomeValue: extraIncome,  // 数字类型，用于判断是否显示
              deductionsValue: deductions,    // 数字类型，用于判断是否显示
              currentHourlyRate: result.formattedHourlyRate,
              currentIncomeMasked: this.baseService.generateMaskedText(formattedNetIncome),
              workIncomeMasked: this.baseService.generateMaskedText(result.formattedWorkIncome),
              fishingIncomeMasked: this.baseService.generateMaskedText(result.formattedFishingIncome),
              currentHourlyRateMasked: this.baseService.generateMaskedText(result.formattedHourlyRate)
            })

            // 检测收入变化并触发飘动效果
            this.checkIncomeChange(netIncome)
          },
          {
            currentWorkDate: this.data.currentWorkDate,
            currentWorkId: this.data.currentWorkId,
            baseService: this.baseService,
            calculateCrossDateIncome: (workDate) => this.calculateCrossDateIncome(workDate)
          }
        )
        return
      } else {
        // 减少日志输出
        if (!this.lastTodayDataLog || Date.now() - this.lastTodayDataLog > 60000) { // 每分钟最多输出一次
          console.log('[Dashboard1] 实时收入使用今天数据...')
          this.lastTodayDataLog = Date.now()
        }
        workDayData = this.baseService.getTodayData(this.data.currentDate, this.data.currentWorkId)
      }

      // 设置自动结束摸鱼的回调
      this.realTimeIncome.autoEndFishingCallback = (fishingState, endMinutes) => {
        this.handleAutoEndFishing(fishingState, endMinutes)
      }

      // 启动实时更新
      this.realTimeIncome.start(
        (result) => {
          // 获取当日的收入调整数据
          const adjustmentSummary = this.incomeAdjustmentService.getDayAdjustmentSummary(
            this.data.currentDate,
            this.data.currentWorkId
          )

          // 计算净收入（基础收入 + 额外收入 - 扣款）
          const baseIncome = parseFloat(result.formattedIncome) || 0
          const extraIncome = adjustmentSummary.extraIncome || 0
          const deductions = adjustmentSummary.deductions || 0
          const netIncome = baseIncome + extraIncome - deductions

          // 格式化净收入
          const formattedNetIncome = this.baseService.formatAmount(netIncome, 'dashboard1')

          this.setData({
            currentIncome: formattedNetIncome,  // 显示净收入
            workIncome: result.formattedWorkIncome,
            fishingIncome: result.formattedFishingIncome,
            // 新增：收入调整相关数据
            extraIncome: this.baseService.formatAmount(extraIncome, 'dashboard1'),
            deductions: this.baseService.formatAmount(deductions, 'dashboard1'),
            baseIncome: result.formattedIncome,  // 保留基础收入
            // 新增：数字类型的判断字段
            extraIncomeValue: extraIncome,  // 数字类型，用于判断是否显示
            deductionsValue: deductions,    // 数字类型，用于判断是否显示
            currentHourlyRate: result.hourlyRate.toFixed(2),
            currentIncomeMasked: this.baseService.generateMaskedText(formattedNetIncome),
            workIncomeMasked: this.baseService.generateMaskedText(result.formattedWorkIncome),
            fishingIncomeMasked: this.baseService.generateMaskedText(result.formattedFishingIncome),
            currentHourlyRateMasked: this.baseService.generateMaskedText(result.hourlyRate.toFixed(2)),
            isWorking: result.isWorking,
            isFishing: result.isFishing,
            isActive: result.isActive
          })

          // 检测收入变化并触发飘动效果
          this.checkIncomeChange(netIncome)
        },
        {
          workData: workDayData,
          currentWork: currentWork,
          baseService: this.baseService,
          getFishingState: () => {
            // 获取摸鱼状态的函数
            const dataManager = getApp().getDataManager()
            return dataManager.getCurrentFishingState()
          }
        }
      )
    },

    /**
     * 启动自动刷新
     */
    startAutoRefresh: function() {
      // 如果定时器已经在运行，先停止
      if (this.refreshTimer) {
        this.stopAutoRefresh()
      }

      this.refreshTimer = setInterval(() => {
        this.updateCurrentTime()
      }, this.data.refreshInterval)
    },

    /**
     * 停止自动刷新
     */
    stopAutoRefresh: function() {
      if (this.refreshTimer) {
        clearInterval(this.refreshTimer)
        this.refreshTimer = null
      }
    },

    /**
     * 清理资源
     */
    cleanup: function() {
      this.stopAutoRefresh()
      this.stopCurrentSegmentTimer()
      this.stopCountdownTimer()
      this.stopFishingCountTimer()
      this.stopFishingToggleTimer()
      this.clearManualFishingMessageTimer()
      this.stopPerSecondFloating()

      // 停止实时收入更新
      if (this.realTimeIncome) {
        this.realTimeIncome.stop()
      }

      // 停止跨日期实时收入更新
      if (this.crossDateRealTimeIncome) {
        this.crossDateRealTimeIncome.stop()
      }

      // 清理摸鱼自动结束监听
      this.removeFishingAutoEndListener()
    },

    /**
     * 格式化时间
     */
    formatTime: function(date) {
      const hours = date.getHours().toString().padStart(2, '0')
      const minutes = date.getMinutes().toString().padStart(2, '0')
      const seconds = date.getSeconds().toString().padStart(2, '0')
      return `${hours}:${minutes}:${seconds}`
    },

    /**
     * 格式化日期（包含周几）
     */
    formatDate: function(date) {
      const dateStr = formatDateKey(date)

      const weekDays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
      const weekDay = weekDays[date.getDay()]

      return `${dateStr}（${weekDay}）`
    },

    /**
     * 切换隐私脱敏状态
     */
    togglePrivacyMask: function(e) {
      const field = e.currentTarget.dataset.field
      if (!field) return
      
      const newPrivacyMask = Object.assign({}, this.data.privacyMask)
      newPrivacyMask[field] = !newPrivacyMask[field]
      
      this.setData({
        privacyMask: newPrivacyMask
      })
      
      // 触发事件通知父组件
      this.triggerEvent('privacyMaskChange', {
        field: field,
        value: newPrivacyMask[field]
      })
    },

    /**
     * 跳转到工作履历页面
     */
    goToWorkHistory: function() {
      this.triggerEvent('navigate', {
        url: '/pages/work-history/index',
        type: 'switchTab'
      })
    },

    /**
     * 跳转到日历页面
     */
    goToCalendar: function() {
      this.triggerEvent('navigate', {
        url: '/pages/calendar/index',
        type: 'switchTab'
      })
    },

    /**
     * 切换公司名脱敏显示
     */
    toggleCompanyMask: function() {
      this.setData({
        companyMasked: !this.data.companyMasked
      })
    },

    /**
     * 切换职位脱敏显示
     */
    togglePositionMask: function() {
      this.setData({
        positionMasked: !this.data.positionMasked
      })
    },

    /**
     * 显示仪表盘切换器
     */
    onShowDashboardSwitcher: function() {
      this.triggerEvent('showDashboardSwitcher')
    },

    /**
     * 处理仪表盘切换事件
     */
    onDashboardChange: function(event) {
      const { dashboardId } = event.detail
      console.log('Dashboard1 收到仪表盘切换事件:', dashboardId)

      // 向父组件传递事件
      this.triggerEvent('dashboardChange', {
        dashboardId: dashboardId
      })
    },

    /**
     * 显示设置模态框
     */
    onShowSettings: function() {
      console.log('显示 Dashboard1 设置')

      // 加载当前配置
      this.loadDashboardConfig()

      this.setData({
        showSettings: true
      })

      // 延迟显示动画
      setTimeout(() => {
        this.setData({ settingsVisible: true })
      }, 50)
    },

    /**
     * 隐藏设置模态框
     */
    onHideSettings: function() {
      // 开始出场动画
      this.setData({ settingsVisible: false })

      // 等待动画完成后关闭模态框
      setTimeout(() => {
        this.setData({
          showSettings: false
        })
      }, 300) // 与CSS动画时长一致
    },

    /**
     * 模态框内容点击（阻止冒泡）
     */
    onModalContentTap: function() {
      // 阻止事件冒泡，防止关闭模态框
    },

    /**
     * 加载仪表盘配置
     */
    loadDashboardConfig: function() {
      try {
        const dashboardService = getApp().getDashboardService()
        const config = dashboardService.getDashboardSettings('dashboard1')

        // 设置默认值
        const defaultConfig = {
          showCurrentWork: true,
          incomeDecimalPlaces: 3,
          floatingAnimationMode: 'per_yuan' // 默认每+1元显示
        }

        // 合并配置，确保有默认值
        const mergedConfig = Object.assign({}, defaultConfig, config)

        // 加载小数位数设置
        const decimalPlaces = mergedConfig.incomeDecimalPlaces
        const selectedDecimalIndex = this.data.decimalOptions.findIndex(option => option.value === decimalPlaces)

        // 加载飘动动画设置
        const floatingAnimationMode = mergedConfig.floatingAnimationMode
        const selectedFloatingAnimationIndex = this.data.floatingAnimationOptions.findIndex(option => option.value === floatingAnimationMode)

        this.setData({
          dashboardConfig: mergedConfig,
          selectedDecimalIndex: selectedDecimalIndex >= 0 ? selectedDecimalIndex : 3,
          selectedFloatingAnimationIndex: selectedFloatingAnimationIndex >= 0 ? selectedFloatingAnimationIndex : 0
        })
      } catch (error) {
        console.error('加载 Dashboard1 配置失败:', error)
      }
    },

    /**
     * 配置项变化
     */
    onConfigChange: function(event) {
      const key = event.currentTarget.dataset.key
      let value = event.detail.value

      const newConfig = Object.assign({}, this.data.dashboardConfig)
      newConfig[key] = value

      this.setData({
        dashboardConfig: newConfig
      })

      console.log('Dashboard1 配置项更新:', key, value)
    },

    /**
     * 小数位数按钮点击
     */
    onDecimalButtonTap: function(event) {
      const selectedIndex = parseInt(event.currentTarget.dataset.index)
      const decimalPlaces = this.data.decimalOptions[selectedIndex].value

      // 更新配置
      const newConfig = Object.assign({}, this.data.dashboardConfig)
      newConfig.incomeDecimalPlaces = decimalPlaces

      this.setData({
        selectedDecimalIndex: selectedIndex,
        dashboardConfig: newConfig
      })
    },

    /**
     * 飘动动画按钮点击
     */
    onFloatingAnimationButtonTap: function(event) {
      const selectedIndex = parseInt(event.currentTarget.dataset.index)
      const floatingAnimationMode = this.data.floatingAnimationOptions[selectedIndex].value

      // 更新配置
      const newConfig = Object.assign({}, this.data.dashboardConfig)
      newConfig.floatingAnimationMode = floatingAnimationMode

      this.setData({
        selectedFloatingAnimationIndex: selectedIndex,
        dashboardConfig: newConfig
      })

      // 如果切换到每秒模式，需要重新初始化相关数据
      if (floatingAnimationMode === 'per_second') {
        this.setData({
          lastSecondIncome: this.data.lastIncome,
          perSecondFloatingCounter: 0
        })
      }
    },

    /**
     * 保存设置
     */
    onSaveSettings: function() {
      try {
        const dashboardService = getApp().getDashboardService()
        dashboardService.updateDashboardConfig('dashboard1', this.data.dashboardConfig)

        // 如果小数位数发生变化，重新加载用户设置以更新显示
        this.loadUserSettings()

        // 重新加载数据以应用新的小数位数
        this.loadData()

        // 显示成功提示
        wx.showToast({
          title: '保存成功',
          icon: 'success',
          duration: 1000
        })

        // 关闭设置模态框（带动画）
        this.onHideSettings()
      } catch (error) {
        console.error('保存 Dashboard1 设置失败:', error)
        wx.showToast({
          title: '保存失败',
          icon: 'error'
        })
      }
    },

    /**
     * 工作摸鱼切换事件处理
     */
    onWorkFishingToggle: function(e) {
      console.log('工作摸鱼切换点击')

      // 防止重复点击
      if (this.data.switchLoading) {
        return
      }

      // 设置Switch加载状态
      this.setData({
        switchLoading: true
      })

      if (this.data.isFishing) {
        // 当前在摸鱼，切换到工作状态 - 结束摸鱼
        this.endFishing()
      } else {
        // 当前在工作，切换到摸鱼状态 - 开始摸鱼
        this.startFishing()
      }
    },

    /**
     * 开始摸鱼
     */
    startFishing: function() {
      try {
        const dataManager = getApp().getDataManager()

        // 检查是否可以开始摸鱼
        if (!this.data.isInWorkTime) {
          wx.showToast({
            title: '当前不在工作时间',
            icon: 'none'
          })
          this.completeSwitchToggle()
          return
        }

        // 开始摸鱼
        const result = dataManager.startFishing('')

        if (result.success) {
          // 立即更新摸鱼状态
          this.setData({
            isFishing: true
          })

          // 更新状态
          this.updateCurrentFishingState()
          this.updateCurrentTime()

          // 立即重新启动时间段定时器，确保时长立即更新
          this.startCurrentSegmentTimer()

          wx.showToast({
            title: '开始摸鱼',
            icon: 'none'
          })
        } else {
          wx.showToast({
            title: result.message || '开始摸鱼失败',
            icon: 'error'
          })
        }
      } catch (error) {
        console.error('开始摸鱼失败:', error)
        wx.showToast({
          title: '开始摸鱼失败',
          icon: 'error'
        })
      }

      this.completeSwitchToggle()
    },

    /**
     * 结束摸鱼
     */
    endFishing: function() {
      try {
        const dataManager = getApp().getDataManager()
        const fishingState = dataManager.getCurrentFishingState()

        if (!fishingState || !fishingState.isActive) {
          wx.showToast({
            title: '当前没有进行中的摸鱼',
            icon: 'none'
          })
          this.completeSwitchToggle()
          return
        }

        // 使用标准的结束摸鱼方法
        const result = dataManager.endFishing()

        if (result.success) {
          // 立即更新状态
          this.setData({
            isFishing: false,
            currentFishingState: null
          })
          // 重新启动定时器以显示工作时长
          this.startCurrentSegmentTimer()
          this.updateStatistics()
          this.loadTodaySchedule()
          this.updateCurrentTime()

          // 根据是否保存记录给出不同的提示
          if (result.saved) {
            wx.showToast({
              title: `摸鱼结束\n时长：${result.duration}`,
              icon: 'success',
              duration: 2000
            })
          } else {
            wx.showToast({
              title: `摸鱼结束\n时长：${result.duration}，未记录`,
              icon: 'none',
              duration: 2000
            })
          }
        } else {
          wx.showToast({
            title: result.message || '结束摸鱼失败',
            icon: 'error'
          })
        }
      } catch (error) {
        console.error('结束摸鱼失败:', error)
        wx.showToast({
          title: '结束摸鱼失败',
          icon: 'error'
        })
      }

      this.completeSwitchToggle()
    },


    /**
     * 完成Switch切换
     */
    completeSwitchToggle: function() {
      this.setData({
        switchLoading: false
      })
    },

    /**
     * 编辑摸鱼备注
     */
    onEditFishingRemark: function() {
      if (!this.data.currentFishingState || !this.data.currentFishingState.isActive) {
        return
      }

      this.setData({
        showRemarkEditor: true,
        currentFishingRemark: this.data.currentFishingState.remark || ''
      })
    },

    /**
     * 备注编辑确认
     */
    onRemarkConfirm: function(e) {
      const newRemark = e.detail.remark || ''

      try {
        // 更新摸鱼状态中的备注
        const dataManager = getApp().getDataManager()
        const result = dataManager.updateCurrentFishingRemark(newRemark)

        if (result.success) {
          this.setData({
            currentFishingRemark: newRemark
          })

          wx.showToast({
            title: '备注已更新',
            icon: 'success'
          })
        } else {
          throw new Error(result.message || '更新失败')
        }
      } catch (error) {
        console.error('更新摸鱼备注失败:', error)
        wx.showToast({
          title: '更新备注失败',
          icon: 'error'
        })
      }

      // 总是关闭模态框
      this.setData({
        showRemarkEditor: false
      })
    },

    /**
     * 备注编辑取消
     */
    onRemarkCancel: function() {
      this.setData({
        showRemarkEditor: false
      })
    },

    /**
     * 外部调用的数据刷新方法
     */
    refreshData: function() {
      // 重新加载所有数据
      this.loadUserSettings()
      this.loadCurrentWork()
      this.updateStatistics()
      this.loadTodaySchedule()
      this.updateCurrentTime()
    },

    /**
     * 启动摸鱼人数更新定时器
     */
    startFishingCountTimer: function() {
      // 立即更新一次
      this.updateFishingCount()

      // 每分钟更新一次摸鱼人数
      this.fishingCountTimer = setInterval(() => {
        this.updateFishingCount()
      }, 60000) // 60秒
    },

    /**
     * 停止摸鱼人数更新定时器
     */
    stopFishingCountTimer: function() {
      if (this.fishingCountTimer) {
        clearInterval(this.fishingCountTimer)
        this.fishingCountTimer = null
      }
    },

    /**
     * 更新摸鱼人数
     */
    updateFishingCount: function() {
      if (!this.data.showFishingCount) {
        return
      }

      this.setData({ fishingCountLoading: true })

      console.log('[Dashboard1] 开始更新摸鱼人数')

      api.fishing.getCurrentFishingCount().then(result => {
        if (result.success) {
          this.setData({
            fishingCount: result.data.count,
            fishingCountLoading: false
          })
          console.log('[Dashboard1] 摸鱼人数更新成功:', result.data.count)
        } else {
          this.setData({ fishingCountLoading: false })
        }
      }).catch(error => {
        console.error('[Dashboard1] 摸鱼人数更新失败:', error)
        this.setData({
          fishingCount: '--',
          fishingCountLoading: false
        })
      })
    },

    /**
     * 启动摸鱼切换定时器（仅在摸鱼状态下）
     */
    startFishingToggleTimer: function() {
      this.stopFishingToggleTimer()

      // 每5秒切换一次显示内容
      this.fishingToggleTimer = setInterval(() => {
        this.toggleFishingDisplay()
      }, 5000)
    },

    /**
     * 停止摸鱼切换定时器
     */
    stopFishingToggleTimer: function() {
      if (this.fishingToggleTimer) {
        clearInterval(this.fishingToggleTimer)
        this.fishingToggleTimer = null
      }
    },

    /**
     * 切换摸鱼显示（定时器调用）
     */
    toggleFishingDisplay: function() {
      // 简单切换显示状态
      const newShowFishingMessage = !this.data.showFishingMessage

      // 如果切换到摸鱼话术，生成新的话术
      if (newShowFishingMessage) {
        this.setData({
          fishingMessageCache: this.generateFishingMessage(true), // 用户在摸鱼状态
          showFishingMessage: newShowFishingMessage
        })
      } else {
        this.setData({
          showFishingMessage: newShowFishingMessage
        })
      }

      // 更新显示
      this.updateUnifiedInfo()
    },

    /**
     * 获取没有时间段安排时的智能信息文本
     * @returns {string} 智能信息文本
     */
    getNoScheduleInfoText: function() {
      const currentStatus = this.data.currentStatus

      if (!currentStatus) {
        return '今天没有工作安排，自由安排时间！'
      }

      // 根据状态类型返回相应的文本
      return this.getStatusInfoText(currentStatus)
    },

    /**
     * 根据状态类型获取信息文本
     * @param {Object} status 状态对象
     * @returns {string} 信息文本
     */
    getStatusInfoText: function(status) {
      // 节假日管理器识别的特殊日期
      if (status.type === 'holiday') {
        return `今天是${status.holidayName || '节假日'}，享受假期时光！🎉`
      }
      if (status.type === 'weekend') {
        return `今天是${status.weekendName || '周末'}，好好休息放松！🌈`
      }

      // 工作/出勤状态类
      const workStatusTexts = {
        'rest_day': '今天是休息日，好好放松！😴',
        'rotation_rest_day': '今天是轮休日，享受轮休时光！🔄',
        'compensatory_rest_day': '今天是补休日，好好休息补偿一下！⏰',
        'duty_day': '今天是值班日，保持警觉！🛡️',
        'standby_day': '今天是待岗日，耐心等待安排！⏳',
        'work_suspension_day': '今天是停工留薪期，安心休养！⏸️'
      }

      // 法定/特殊假日类
      const legalHolidayTexts = {
        'weekend_day': '今天是公休日，享受周末时光！🏖️',
        'legal_holiday': '今天是法定节假日，尽情享受假期！🎉',
        'adjusted_leave_day': '今天是调休日，好好休息！🔄',
        'annual_leave_day': '今天是年休假，享受美好假期！🌴',
        'festival_leave_day': '今天是节日假，庆祝特殊节日！🎊'
      }

      // 请假/缺勤类
      const personalLeaveTexts = {
        'personal_leave_day': '今天是事假日，处理个人事务！🏠',
        'sick_leave_day': '今天是病假日，好好休息早日康复！🤒',
        'marriage_leave_day': '今天是婚假日，享受人生大事！💒',
        'bereavement_leave_day': '今天是丧假日，节哀顺变！🕯️',
        'maternity_leave_day': '今天是产假日，好好照顾自己和宝宝！👶',
        'paternity_leave_day': '今天是陪产假日，陪伴家人！👨‍👶',
        'family_visit_leave_day': '今天是探亲假日，享受家庭团聚！👨‍👩‍👧‍👦',
        'work_injury_leave_day': '今天是工伤假日，安心治疗康复！🏥',
        'absent_day': '今天标记为旷工，请注意出勤！❌'
      }

      // 查找对应的文本
      const statusText = workStatusTexts[status.type] ||
                        legalHolidayTexts[status.type] ||
                        personalLeaveTexts[status.type]

      if (statusText) {
        return statusText
      }

      // 默认情况
      if (status.type === 'no_schedule') {
        return '今天没有工作安排，自由安排时间！📅'
      }

      // 未知状态
      return `今天是${status.text || '未知状态'}，合理安排时间！`
    },

    /**
     * 更新统一信息显示
     */
    updateUnifiedInfo: function() {
      const nextSegmentCountdown = this.data.nextSegmentCountdown
      const isFishing = this.data.isFishing

      let newText = ''
      let shouldAnimate = false

      if (nextSegmentCountdown && nextSegmentCountdown.isFinished) {
        // 已经下班，显示结束鼓励话语
        newText = nextSegmentCountdown.message
        shouldAnimate = true
      } else if (isFishing && this.data.showFishingCount && this.data.showFishingMessage) {
        // 摸鱼状态下显示摸鱼话术
        if (!this.data.fishingMessageCache) {
          this.setData({
            fishingMessageCache: this.generateFishingMessage(true) // 用户在摸鱼状态
          })
        }
        newText = this.data.fishingMessageCache
        shouldAnimate = true
      } else if (!isFishing && this.data.manualShowFishingMessage && this.data.showFishingCount) {
        // 非摸鱼状态下手动显示摸鱼话术
        if (!this.data.fishingMessageCache) {
          this.setData({
            fishingMessageCache: this.generateFishingMessage(false) // 用户不在摸鱼状态
          })
        }
        newText = this.data.fishingMessageCache
        shouldAnimate = true
      } else if (nextSegmentCountdown && !nextSegmentCountdown.isFinished) {
        // 显示倒计时
        newText = `⏱️ 距离${nextSegmentCountdown.type}还有 ${nextSegmentCountdown.time}`
        // 检查是否是从摸鱼话术切换回倒计时，如果是则需要动画
        const wasShowingFishingMessage = this.data.currentInfoText &&
                                        !this.data.currentInfoText.includes('⏱️') &&
                                        !this.data.currentInfoText.includes('准备开始工作')
        shouldAnimate = wasShowingFishingMessage
      } else {
        // 没有倒计时时，使用智能信息文本
        newText = this.getNoScheduleInfoText()
        shouldAnimate = true
      }

      // 更新显示内容
      if (newText !== this.data.currentInfoText) {
        if (shouldAnimate) {
          this.animateTextChange(newText)
        } else {
          this.setData({
            currentInfoText: newText
          })
        }
      }
    },

    /**
     * 生成摸鱼消息
     * @param {boolean} isCurrentUserFishing - 当前用户是否在摸鱼，默认为 true
     */
    generateFishingMessage: function(isCurrentUserFishing = true) {
      if (this.data.fishingCountLoading) {
        return '正在统计摸鱼人数...'
      }

      const count = this.data.fishingCount
      const templates = this.data.fishingMessageTemplates

      if (isCurrentUserFishing) {
        if (count <= 1) {
          // 用户在摸鱼，只有自己
          const templateArray = templates.userFishingAlone
          const randomIndex = Math.floor(Math.random() * templateArray.length)
          return templateArray[randomIndex]
        } else {
          // 用户在摸鱼，还有其他人
          const templateArray = templates.userFishingWithOthers
          const randomIndex = Math.floor(Math.random() * templateArray.length)
          const template = templateArray[randomIndex]

          let message = template.replace('{count}', count.toString())
          message = message.replace('{otherCount}', (count - 1).toString())

          return message
        }
      } else {
        if (count <= 0) {
          // 用户不在摸鱼，也没有其他人摸鱼
          const templateArray = templates.noOneFishing
          const randomIndex = Math.floor(Math.random() * templateArray.length)
          return templateArray[randomIndex]
        } else {
          // 用户不在摸鱼，但有其他人在摸鱼
          const templateArray = templates.userNotFishingButOthersAre
          const randomIndex = Math.floor(Math.random() * templateArray.length)
          const template = templateArray[randomIndex]

          return template.replace('{count}', count.toString())
        }
      }
    },

    /**
     * 文本切换动画
     */
    animateTextChange: function(newText) {
      // 淡出动画
      this.setData({
        infoTextAnimation: 'fade-out'
      })

      // 延迟更新文本并淡入
      setTimeout(() => {
        this.setData({
          currentInfoText: newText,
          infoTextAnimation: 'fade-in'
        })

        // 清除动画类
        setTimeout(() => {
          this.setData({
            infoTextAnimation: ''
          })
        }, 300)
      }, 150)
    },

    /**
     * 点击信息文本事件
     */
    onInfoTextTap: function() {
      const isFishing = this.data.isFishing

      if (isFishing && this.data.showFishingCount) {
        // 摸鱼状态下的切换逻辑（原有逻辑）
        this.toggleFishingDisplay()
      } else if (!isFishing && this.data.showFishingCount && this.data.nextSegmentCountdown && !this.data.nextSegmentCountdown.isFinished) {
        // 非摸鱼状态下，点击倒计时切换到摸鱼话术
        this.showManualFishingMessage()
      }
    },

    /**
     * 手动显示摸鱼话术（非摸鱼状态下点击倒计时）
     */
    showManualFishingMessage: function() {
      // 清除之前的定时器
      this.clearManualFishingMessageTimer()

      // 生成摸鱼话术并显示
      this.setData({
        manualShowFishingMessage: true,
        fishingMessageCache: this.generateFishingMessage(false) // 用户不在摸鱼状态
      })

      // 更新显示
      this.updateUnifiedInfo()

      // 5秒后自动切换回倒计时
      this.data.manualFishingMessageTimer = setTimeout(() => {
        this.hideManualFishingMessage()
      }, 5000)
    },

    /**
     * 隐藏手动显示的摸鱼话术
     */
    hideManualFishingMessage: function() {
      this.setData({
        manualShowFishingMessage: false,
        fishingMessageCache: ''
      })

      // 更新显示
      this.updateUnifiedInfo()

      // 清除定时器
      this.clearManualFishingMessageTimer()
    },

    /**
     * 清除手动显示摸鱼话术的定时器
     */
    clearManualFishingMessageTimer: function() {
      if (this.data.manualFishingMessageTimer) {
        clearTimeout(this.data.manualFishingMessageTimer)
        this.setData({
          manualFishingMessageTimer: null
        })
      }
    },

    /**
     * 开始摸鱼时的处理
     */
    onFishingStart: function() {
      // 清除手动显示摸鱼话术的状态（避免冲突）
      this.clearManualFishingMessageTimer()

      // 更新摸鱼状态
      this.updateCurrentFishingState()

      // 生成摸鱼话术并立即显示
      this.setData({
        fishingMessageCache: this.generateFishingMessage(true), // 用户在摸鱼状态
        showFishingMessage: true,
        manualShowFishingMessage: false
      })

      // 启动切换定时器
      this.startFishingToggleTimer()

      // 更新显示
      this.updateUnifiedInfo()
    },

    /**
     * 结束摸鱼时的处理
     */
    onFishingEnd: function() {
      // 停止切换定时器
      this.stopFishingToggleTimer()

      // 清除手动显示摸鱼话术的状态
      this.clearManualFishingMessageTimer()

      // 清除摸鱼相关状态
      this.setData({
        fishingMessageCache: '',
        showFishingMessage: false,
        manualShowFishingMessage: false
      })

      // 更新显示
      this.updateUnifiedInfo()
    },

    /**
     * 添加摸鱼自动结束监听
     */
    addFishingAutoEndListener: function() {
      const dataManager = getApp().getDataManager()

      // 绑定监听器函数到this，确保可以正确移除
      this.fishingAutoEndListener = this.handleFishingAutoEnd.bind(this)

      // 添加监听器
      dataManager.addFishingAutoEndListener(this.fishingAutoEndListener)
    },

    /**
     * 移除摸鱼自动结束监听
     */
    removeFishingAutoEndListener: function() {
      if (this.fishingAutoEndListener) {
        const dataManager = getApp().getDataManager()
        dataManager.removeFishingAutoEndListener(this.fishingAutoEndListener)
        this.fishingAutoEndListener = null
      }
    },

    /**
     * 处理摸鱼自动结束
     * @param {Object} eventData - 事件数据
     */
    handleFishingAutoEnd: function(eventData) {
      const { sessionId, reason, result } = eventData

      console.log('[Dashboard1] 摸鱼自动结束:', sessionId, reason)

      // 调用原有的结束摸鱼处理，但不触发数据重新加载
      this.onFishingEnd()

      // 只更新必要的统计数据
      this.updateStatistics()
      this.updateCurrentTime()

      // 通知fishing-control组件刷新状态
      const fishingControl = this.selectComponent('#fishing-control')
      if (fishingControl) {
        fishingControl.refresh()
      }
    },

    /**
     * 更新摸鱼时间调整按钮状态
     */
    updateFishingTimeAdjustButtons: function() {
      if (!this.data.isFishing) {
        this.setData({
          canIncreaseFishingTime: false,
          canDecreaseFishingTime: false
        })
        return
      }

      const dataManager = getApp().getDataManager()
      const checkResult = dataManager.checkFishingTimeAdjustment()

      this.setData({
        canIncreaseFishingTime: checkResult.canIncrease || false,
        canDecreaseFishingTime: checkResult.canDecrease || false
      })
    },

    /**
     * 增加摸鱼时间（开始时间提前1分钟）
     */
    onIncreaseFishingTime: function(e) {
      // 阻止事件冒泡
      if (e && e.stopPropagation) {
        e.stopPropagation()
      }

      if (!this.data.canIncreaseFishingTime) {
        return
      }

      this.adjustFishingTime(1)
    },

    /**
     * 减少摸鱼时间（开始时间延后1分钟）
     */
    onDecreaseFishingTime: function(e) {
      // 阻止事件冒泡
      if (e && e.stopPropagation) {
        e.stopPropagation()
      }

      if (!this.data.canDecreaseFishingTime) {
        return
      }

      this.adjustFishingTime(-1)
    },

    /**
     * 调整摸鱼时间
     * @param {number} adjustMinutes - 调整分钟数，正数增加时长，负数减少时长
     */
    adjustFishingTime: function(adjustMinutes) {
      const dataManager = getApp().getDataManager()
      const result = dataManager.adjustFishingTime(adjustMinutes)

      if (result.success) {
        // 更新当前摸鱼状态
        this.updateCurrentFishingState()

        // 刷新实时收入显示
        this.updateCurrentTime()

        // 重新加载今日时间安排以更新时间图表
        this.loadTodaySchedule()

      } else {
        // 显示错误提示
        wx.showToast({
          title: result.message,
          icon: 'none',
          duration: 2000
        })
      }
    },

    /**
     * 处理摸鱼时间调整事件
     * @param {Object} eventData - 事件数据
     */
    handleFishingTimeAdjusted: function(eventData) {
      console.log('[Dashboard1] 摸鱼时间已调整:', eventData)

      // 更新当前摸鱼状态以反映时间调整
      this.updateCurrentFishingState()

      // 刷新实时收入显示
      this.updateCurrentTime()

      // 重新加载今日时间安排以更新时间图表
      this.loadTodaySchedule()
    },

    /**
     * 检测收入变化并触发飘动效果
     * @param {number} newIncome - 新的收入值（数字）
     */
    checkIncomeChange: function(newIncome) {
      if (typeof newIncome !== 'number' || isNaN(newIncome)) {
        return
      }

      // 获取当前飘动动画模式
      const floatingMode = this.data.dashboardConfig.floatingAnimationMode || 'per_yuan'

      // 如果关闭了飘动动画，直接返回
      if (floatingMode === 'disabled') {
        this.setData({
          lastIncome: newIncome
        })
        return
      }

      // 如果是第一次设置收入，直接记录，不触发飘动
      if (this.data.lastIncome === 0) {
        this.setData({
          lastIncome: newIncome
        })
        return
      }

      // 计算收入增加量
      const increase = newIncome - this.data.lastIncome

      // 只有当收入增加时才处理飘动效果
      if (increase > 0) {
        if (floatingMode === 'per_yuan') {
          // 每+1元模式：只有当跨越整数点时才触发飘动效果
          const lastIncomeFloor = Math.floor(this.data.lastIncome)
          const newIncomeFloor = Math.floor(newIncome)

          if (newIncomeFloor > lastIncomeFloor) {
            // 计算跨越了多少个整数点
            const crossedIntegers = newIncomeFloor - lastIncomeFloor

            // 为每个跨越的整数点触发一个+1飘动效果
            for (let i = 0; i < crossedIntegers; i++) {
              this.triggerFloatingNumber(1.00)
            }
          }
        } else if (floatingMode === 'per_second') {
          // 每秒模式：记录增量，由定时器处理
          // 这里只记录增量，实际的飘动效果由 startPerSecondFloating 方法处理
        }
      }

      // 更新上次收入值
      this.setData({
        lastIncome: newIncome
      })
    },

    /**
     * 触发飘动数字效果
     * @param {number} amount - 飘动的金额
     */
    triggerFloatingNumber: function(amount) {
      // 检查是否启用飘动动画
      if (this.data.dashboardConfig.floatingAnimationMode === 'disabled') {
        return
      }

      const id = ++this.data.floatingIdCounter
      const offsetX = Math.random() * 40 - 20 // 随机偏移 -20 到 20rpx
      const delay = Math.random() * 200 // 随机延迟 0-200ms

      // 格式化金额显示
      const formattedAmount = this.baseService.formatAmount(amount, 'dashboard1')

      const floatingNumber = {
        id: id,
        amount: formattedAmount,
        offsetX: offsetX,
        delay: delay,
        animating: false
      }

      // 添加到飘动数组
      const newFloatingNumbers = [...this.data.floatingNumbers, floatingNumber]

      // 限制同时存在的飘动数字数量（最多5个）
      if (newFloatingNumbers.length > 5) {
        newFloatingNumbers.shift()
      }

      this.setData({
        floatingNumbers: newFloatingNumbers,
        floatingIdCounter: id
      }, () => {
        // 延迟启动动画
        setTimeout(() => {
          const updatedNumbers = this.data.floatingNumbers.map(item => {
            if (item.id === id) {
              return { ...item, animating: true }
            }
            return item
          })
          this.setData({
            floatingNumbers: updatedNumbers
          })
        }, 50)
      })
    },

    /**
     * 飘动动画结束处理
     */
    onFloatingAnimationEnd: function(e) {
      const id = parseInt(e.currentTarget.dataset.id)
      this.removeFloatingNumber(id)
    },

    /**
     * 移除完成的飘动数字
     * @param {number} id - 飘动数字ID
     */
    removeFloatingNumber: function(id) {
      const filteredNumbers = this.data.floatingNumbers.filter(item => item.id !== id)
      this.setData({
        floatingNumbers: filteredNumbers
      })
    },

    /**
     * 启动每秒飘动效果
     */
    startPerSecondFloating: function() {
      // 如果定时器已经在运行，先停止
      this.stopPerSecondFloating()

      // 初始化计数器和上一秒收入值
      this.setData({
        lastSecondIncome: this.data.lastIncome,
        perSecondFloatingCounter: 0
      })

      // 启动定时器，每秒检查一次
      this.data.perSecondFloatingTimer = setInterval(() => {
        const currentIncome = this.data.lastIncome
        const lastSecondIncome = this.data.lastSecondIncome
        const counter = this.data.perSecondFloatingCounter

        // 计算这一秒的收入增量
        const secondIncrease = currentIncome - lastSecondIncome

        // 前1次不显示飘动效果，从第3次开始显示
        if (counter >= 1 && secondIncrease > 0) {
          this.triggerFloatingNumber(secondIncrease)
        }

        // 更新上一秒收入值和计数器
        this.setData({
          lastSecondIncome: currentIncome,
          perSecondFloatingCounter: counter + 1
        })
      }, 1000)
    },

    /**
     * 停止每秒飘动效果
     */
    stopPerSecondFloating: function() {
      if (this.data.perSecondFloatingTimer) {
        clearInterval(this.data.perSecondFloatingTimer)
        this.setData({
          perSecondFloatingTimer: null,
          perSecondFloatingCounter: 0
        })
      }
    },

    /**
     * 重置收入跟踪
     */
    resetIncomeTracking: function() {
      // 停止每秒飘动定时器
      this.stopPerSecondFloating()

      this.setData({
        lastIncome: 0,
        lastSecondIncome: 0,
        perSecondFloatingCounter: 0,
        floatingNumbers: []
      })
    },

  }
})
