<!--摸鱼记录编辑器-->
<view class="fishing-editor {{visible ? 'show' : ''}}" bindtap="onClose">
  <!-- 编辑器内容 -->
  <view class="editor-content" catchtap="onStopPropagation">
    <!-- 标题栏 -->
    <view class="editor-header">
      <text class="editor-title">{{mode === 'add' ? '添加摸鱼记录' : '编辑摸鱼记录'}}</text>
      <view class="close-btn" bindtap="onClose">
        <text class="close-icon">✕</text>
      </view>
    </view>

    <!-- 表单内容 -->
    <view class="editor-body">
      <!-- 时间范围 -->
      <view class="form-group">
        <view class="form-label">摸鱼时间</view>
        <view class="time-range-selector" bind:tap="onOpenTimeRangePicker">
          <view class="time-range-display">
            <view class="time-range-text">
              <text class="start-time">{{formData.startTime || '00:00'}}</text>
              <text class="start-next-day-indicator" wx:if="{{formData.startIsNextDay}}">次日</text>
              <text class="time-separator">-</text>
              <text class="end-time">{{formData.endTime || '00:00'}}</text>
              <text class="end-next-day-indicator" wx:if="{{formData.endIsNextDay}}">次日</text>
              <text class="duration-text" wx:if="{{timeRangeDuration}}">{{timeRangeDuration}}</text>
            </view>
          </view>
          <view class="time-range-arrow">
            <text class="arrow-icon">›</text>
          </view>
        </view>
        <view wx:if="{{errors.startTime || errors.endTime || errors.timeRange}}" class="error-text">
          {{errors.startTime || errors.endTime || errors.timeRange}}
        </view>
      </view>

      <!-- 工作时间段验证错误 -->
      <view wx:if="{{errors.workSegment}}" class="form-group">
        <view class="error-text">{{errors.workSegment}}</view>
      </view>

      <!-- 时间冲突验证错误 -->
      <view wx:if="{{errors.timeConflict}}" class="form-group">
        <view class="error-text">{{errors.timeConflict}}</view>
      </view>

      <!-- 备注 -->
      <view class="form-group">
        <view class="form-label">
          <text>摸鱼备注</text>
          <text wx:if="{{quickRemarks.length > 0}}" class="quick-remark-toggle" bindtap="onToggleQuickRemarks">{{showQuickRemarks ? '收起' : '快捷备注'}}</text>
        </view>
        <input
          class="remark-input"
          placeholder="描述摸鱼时在做什么..."
          value="{{formData.remark}}"
          maxlength="20"
          bindinput="onRemarkInput"
        />

        <!-- 快捷备注列表 -->
        <view wx:if="{{showQuickRemarks && quickRemarks.length > 0}}" class="quick-remarks-list">
          <view
            wx:for="{{quickRemarks}}"
            wx:key="remark"
            class="quick-remark-item"
            data-remark="{{item.remark}}"
            bindtap="onSelectQuickRemark"
          >
            <text class="quick-remark-text">{{item.remark}}</text>
            <text class="quick-remark-count">{{item.count}}次</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="editor-footer">
      <button wx:if="{{mode === 'edit'}}" class="editor-btn delete-btn" bindtap="onDelete">删除</button>
      <button class="editor-btn save-btn {{mode === 'add' ? 'save-btn-full' : ''}}" disabled="{{loading}}" bindtap="onSave">{{loading ? '保存中...' : '保存'}}</button>
    </view>

    <!-- 加载状态 -->
    <view class="loading-overlay" wx:if="{{loading}}">
      <view class="loading-spinner"></view>
    </view>
  </view>

  <!-- 时间范围选择器 -->
  <time-range-picker
    show="{{showTimeRangePicker}}"
    start-time="{{formData.startTime || '09:00'}}"
    end-time="{{formData.endTime || '18:00'}}"
    is-start-next-day="{{formData.startIsNextDay}}"
    is-end-next-day="{{formData.endIsNextDay}}"
    title="设置摸鱼时间"
    bind:confirm="onTimeRangePickerConfirm"
    bind:cancel="onTimeRangePickerCancel"
    bind:close="onTimeRangePickerClose">
  </time-range-picker>
</view>
