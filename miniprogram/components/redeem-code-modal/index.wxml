<view wx:if="{{show}}" class="redeem-modal-overlay {{visible ? 'show' : ''}}" bindtap="onClose">
  <view class="redeem-modal-content" catchtap="onStopPropagation">
    <!-- 模态框头部 -->
    <view class="modal-header">
      <view class="modal-title">
        <text class="title-icon">🎫</text>
        <text class="title-text">使用兑换码</text>
      </view>
      <view class="modal-close" bindtap="onClose">
        <text class="close-icon">✕</text>
      </view>
    </view>

    <!-- 兑换码输入 -->
    <view class="modal-body">
      <view class="input-section">
        <view class="input-label">请输入兑换码</view>
        <view class="input-wrapper">
          <input
            class="code-input"
            type="text"
            placeholder="请输入兑换码"
            value="{{inputCode}}"
            bindinput="onCodeInput"
            maxlength="20"
            auto-focus="{{false}}"
          />
        </view>
      </view>
    </view>

    <!-- 模态框底部 -->
    <view class="modal-footer">
      <button class="store-btn" bindtap="onGoToStore">
        <text class="btn-icon">🛒</text>
        <text class="btn-text">积分商店</text>
        <text class="btn-arrow">›</text>
      </button>
      <button
        class="redeem-btn {{(!inputCode || loading) ? 'disabled' : ''}}"
        bindtap="onRedeemCode"
        loading="{{loading}}"
      >{{loading ? '兑换中...' : '立即兑换'}}</button>
    </view>
  </view>
</view>
