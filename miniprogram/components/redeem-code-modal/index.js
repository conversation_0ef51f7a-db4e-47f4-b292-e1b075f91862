// 兑换码使用模态框组件
import { api } from '../../core/api/index.js'

Component({
  options: {
    addGlobalClass: true
  },

  properties: {
    // 是否显示模态框
    show: {
      type: Boolean,
      value: false
    },
    // 预填充的兑换码
    defaultCode: {
      type: String,
      value: ''
    }
  },

  data: {
    // 输入的兑换码
    inputCode: '',
    // 加载状态
    loading: false,
    // 控制动画显示
    visible: false
  },

  observers: {
    'show': function(show) {
      if (show) {
        // 模态框显示时，设置默认兑换码
        this.setData({
          inputCode: this.properties.defaultCode || ''
        })
        // 延迟显示动画，确保DOM已渲染
        setTimeout(() => {
          this.setData({ visible: true })
        }, 50)
      } else {
        this.setData({ visible: false })
      }
    },
    'defaultCode': function(code) {
      if (code) {
        this.setData({
          inputCode: code
        })
      }
    }
  },

  methods: {
    /**
     * 兑换码输入变化
     */
    onCodeInput(event) {
      const code = event.detail.value.trim().toUpperCase()
      this.setData({
        inputCode: code
      })
    },

    /**
     * 跳转到积分商店
     */
    onGoToStore() {
      wx.navigateTo({
        url: '/pages/store/index'
      })
    },

    /**
     * 使用兑换码
     */
    async onRedeemCode() {
      // 检查按钮是否应该被禁用
      if (!this.data.inputCode || this.data.loading) {
        return
      }

      const code = this.data.inputCode.trim()

      if (!code) {
        wx.showToast({
          title: '请输入兑换码',
          icon: 'none'
        })
        return
      }

      // 简单的兑换码格式验证
      if (code.length < 6) {
        wx.showToast({
          title: '兑换码格式不正确',
          icon: 'none'
        })
        return
      }

      try {
        this.setData({
          loading: true
        })

        console.log('[RedeemCodeModal] 开始兑换码:', code)

        const result = await api.general.redeemCode({
          code: code
        })

        if (result.success) {
          const data = result.data
          console.log('[RedeemCodeModal] 兑换成功:', data)

          // 显示成功信息
          wx.showModal({
            title: '兑换成功！',
            content: `恭喜您成功兑换${data.vipDays}天VIP会员！`,
            showCancel: false,
            confirmText: '太棒了',
            success: () => {
              // 触发成功事件
              this.triggerEvent('success', {
                code: code,
                data: data
              })

              // 关闭模态框
              this.onClose()
            }
          })
        } else {
          wx.showToast({
            title: result.message || '兑换失败',
            icon: 'none'
          })
        }
      } catch (error) {
        console.error('[RedeemCodeModal] 兑换码兑换失败:', error)
        wx.showToast({
          title: error.message || '网络异常，请重试',
          icon: 'none'
        })
      } finally {
        this.setData({
          loading: false
        })
      }
    },

    /**
     * 关闭模态框
     */
    onClose() {
      // 开始出场动画
      this.setData({ visible: false })

      // 等待动画完成后触发关闭事件
      setTimeout(() => {
        this.setData({
          inputCode: '',
          loading: false
        })
        this.triggerEvent('close')
      }, 300) // 与CSS动画时长一致
    },

    /**
     * 阻止事件冒泡
     */
    onStopPropagation() {
      // 阻止点击模态框内容时关闭
    }
  }
})
