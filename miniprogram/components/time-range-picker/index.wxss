/* 时间段选择器样式 */

.time-range-picker {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
}

.picker-content {
  background: #ffffff;
  border-radius: 24rpx;
  width: 100%;
  max-width: 680rpx;
  max-height: 85vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.15);
}

/* 模态框头部 */
.picker-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 32rpx 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
  background: #fafafa;
  border-radius: 24rpx 24rpx 0 0;
}

.header-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #1f2937;
}

.header-close {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: #f3f4f6;
  transition: all 0.2s ease;
}

.header-close:active {
  background: #e5e7eb;
  transform: scale(0.95);
}

.close-icon {
  font-size: 28rpx;
  color: #6b7280;
  font-weight: bold;
}

/* 时间选择区域 */
.time-selection {
  flex: 1;
  overflow-y: auto;
  padding: 32rpx;
}

/* 时间选择行 */
.time-row {
  display: flex;
  align-items: center;
  gap: 16rpx;
  margin-bottom: 32rpx;
}

/* 时间列 */
.time-column {
  flex: 1;
}

/* 范围分隔符 */
.range-separator {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 40rpx;
}

.range-dash {
  font-size: 48rpx;
  font-weight: bold;
  color: #6b7280;
  line-height: 1;
}

.time-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 16rpx;
  gap: 8rpx;
}

.time-label {
  font-size: 28rpx;
  font-weight: 600;
  color: #1f2937;
  text-align: center;
}

.next-day-option {
  display: flex;
  align-items: center;
  gap: 8rpx;
  cursor: pointer;
  padding: 8rpx;
  border-radius: 8rpx;
  transition: background-color 0.2s ease;
}

.next-day-option:active {
  background-color: #f3f4f6;
}

.next-day-text {
  font-size: 20rpx;
  color: #6b7280;
  font-weight: 500;
}

.next-day-disabled {
  opacity: 0.5;
  pointer-events: none;
  cursor: not-allowed;
}

.next-day-disabled .next-day-text {
  color: #9ca3af;
}

.next-day-disabled:active {
  background-color: transparent;
}

.time-pickers {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6rpx;
}

.time-picker {
  width: 80rpx;
  height: 180rpx;
  background: #f9fafb;
  border: 2rpx solid #e5e7eb;
  border-radius: 12rpx;
}

.time-separator {
  font-size: 32rpx;
  font-weight: bold;
  color: #374151;
  margin: 0 2rpx;
}

.picker-item {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 50rpx;
  font-size: 26rpx;
  color: #374151;
  font-weight: 500;
}

/* 时长显示 */
.duration-section {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
  margin-bottom: 24rpx;
  padding: 24rpx;
  background: #eff6ff;
  border: 2rpx solid #3b82f6;
  border-radius: 16rpx;
}

.duration-label {
  font-size: 28rpx;
  color: #1e40af;
  font-weight: 600;
}

.duration-value {
  font-size: 32rpx;
  color: #1e40af;
  font-weight: bold;
}



/* 操作按钮 */
.picker-actions {
  display: flex;
  padding: 24rpx 32rpx;
  gap: 16rpx;
  border-top: 1rpx solid #f0f0f0;
  background: #fafafa;
  border-radius: 0 0 24rpx 24rpx;
}

.action-btn {
  flex: 1;
  height: 48rpx;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  font-weight: 600;
  transition: all 0.2s ease;
}

.action-btn:active {
  transform: scale(0.98);
}

.cancel-btn {
  background: #f3f4f6;
  color: #6b7280;
  border: 2rpx solid #e5e7eb;
}

.cancel-btn:active {
  background: #e5e7eb;
}

.confirm-btn {
  background: #3b82f6;
  color: #ffffff;
  border: 2rpx solid #3b82f6;
}

.confirm-btn:active {
  background: #2563eb;
  border-color: #2563eb;
}
