/* 摸鱼社群模态框样式 */
@import "../../styles/modal-animations.wxss";

.community-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(8rpx);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  padding: 40rpx;

  /* 使用统一的transition动画 */
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.community-modal-overlay.show {
  opacity: 1;
  visibility: visible;
}

.community-modal-content {
  background: white;
  border-radius: 24rpx;
  width: 100%;
  max-width: 640rpx;
  max-height: 85vh;
  overflow: hidden;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.3);
  display: flex;
  flex-direction: column;

  /* 使用统一的transition动画 */
  transform: scale(0.9) translateY(50rpx);
  opacity: 0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.community-modal-overlay.show .community-modal-content {
  transform: scale(1) translateY(0);
  opacity: 1;
}

/* 模态框头部 */
.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 32rpx 24rpx;
  border-bottom: 2rpx solid #f0f0f0;
  background: linear-gradient(135deg, rgba(6, 182, 212, 0.05) 0%, rgba(16, 185, 129, 0.05) 100%);
}

.modal-title {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.title-icon {
  font-size: 32rpx;
}

.title-text {
  font-size: 32rpx;
  font-weight: 700;
  color: #333;
}

.modal-close {
  width: 48rpx;
  height: 48rpx;
  border-radius: 24rpx;
  background: rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.modal-close:active {
  background: rgba(0, 0, 0, 0.1);
  transform: scale(0.95);
}

.close-icon {
  font-size: 24rpx;
  color: #666;
  font-weight: 600;
}

/* 模态框内容 */
.modal-body {
  flex: 1;
  overflow-y: auto;
  padding: 32rpx;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 40rpx;
  gap: 16rpx;
}

.loading-icon {
  font-size: 48rpx;
  animation: spin 2s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

/* 错误状态 */
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 40rpx;
  gap: 16rpx;
}

.error-icon {
  font-size: 48rpx;
}

.error-text {
  font-size: 28rpx;
  color: #666;
  text-align: center;
}

.retry-btn {
  margin-top: 16rpx;
  padding: 16rpx 32rpx;
  background: #667eea;
  color: white;
  border: none;
  border-radius: 12rpx;
  font-size: 28rpx;
}

/* 社群内容 */
.community-content {
  min-height: 200rpx;
}

/* Markdown内容样式 */
.markdown-content {
  line-height: 1.6;
  font-size: 28rpx;
  color: #374151;
}

/* 标题样式 */
.md-header {
  font-weight: 600;
  margin: 24rpx 0 16rpx 0;
  color: #1f2937;
}

.md-header-1 {
  font-size: 36rpx;
  border-bottom: 2rpx solid #e5e7eb;
  padding-bottom: 12rpx;
}

.md-header-2 {
  font-size: 32rpx;
}

.md-header-3 {
  font-size: 30rpx;
}

/* 段落样式 */
.md-paragraph {
  margin: 16rpx 0;
  line-height: 1.6;
}

/* 文本样式 */
.md-text {
  color: #374151;
}

.md-bold {
  font-weight: 600;
  color: #1f2937;
}

.md-italic {
  font-style: italic;
  color: #6b7280;
}

.md-inline-code {
  background: #f3f4f6;
  color: #dc2626;
  padding: 4rpx 8rpx;
  border-radius: 6rpx;
  font-family: 'Courier New', monospace;
  font-size: 24rpx;
}

.md-link {
  color: #667eea;
  text-decoration: underline;
}

/* 图片样式 */
.md-image-container {
  margin: 24rpx 0;
  text-align: center;
}

.md-image {
  max-width: 100%;
  border-radius: 12rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.md-inline-image {
  max-width: 200rpx;
  max-height: 200rpx;
  border-radius: 8rpx;
  margin: 0 8rpx;
  vertical-align: middle;
}

.md-image-caption {
  margin-top: 8rpx;
  font-size: 24rpx;
  color: #6b7280;
  font-style: italic;
}

/* 列表样式 */
.md-list {
  margin: 16rpx 0;
}

.md-list-item {
  display: flex;
  margin: 8rpx 0;
  align-items: flex-start;
}

.md-list-bullet {
  flex-shrink: 0;
  width: 32rpx;
  color: #6b7280;
  font-weight: 600;
}

.md-list-content {
  flex: 1;
  line-height: 1.5;
}

/* 引用样式 */
.md-quote {
  display: flex;
  margin: 16rpx 0;
  padding: 16rpx 0;
}

.md-quote-border {
  width: 6rpx;
  background: #d1d5db;
  border-radius: 3rpx;
  margin-right: 16rpx;
  flex-shrink: 0;
}

.md-quote-content {
  flex: 1;
  color: #6b7280;
  font-style: italic;
  line-height: 1.5;
}

/* 代码块样式 */
.md-code-block {
  margin: 16rpx 0;
  background: #f8fafc;
  border: 1rpx solid #e2e8f0;
  border-radius: 12rpx;
  overflow: hidden;
}

.md-code-language {
  background: #e2e8f0;
  color: #64748b;
  padding: 8rpx 16rpx;
  font-size: 24rpx;
  font-weight: 500;
}

.md-code-content {
  padding: 16rpx;
  font-family: 'Courier New', monospace;
  font-size: 24rpx;
  color: #1e293b;
  line-height: 1.4;
  white-space: pre-wrap;
  word-break: break-all;
}

/* 分割线样式 */
.md-divider {
  height: 2rpx;
  background: #e5e7eb;
  margin: 32rpx 0;
  border-radius: 1rpx;
}

/* 模态框底部 */
.modal-footer {
  padding: 24rpx 32rpx;
  border-top: 2rpx solid #f0f0f0;
  background: #f9fafb;
}

.footer-tip {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
}

.tip-icon {
  font-size: 24rpx;
}

.tip-text {
  font-size: 24rpx;
  color: #6b7280;
}
