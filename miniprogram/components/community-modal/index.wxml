<!-- 摸鱼社群模态框组件 -->
<view wx:if="{{show}}" class="community-modal-overlay {{visible ? 'show' : ''}}" bindtap="onClose">
  <view class="community-modal-content" catchtap="onStopPropagation">
    <!-- 模态框头部 -->
    <view class="modal-header">
      <view class="modal-title">
        <text class="title-icon">🐟</text>
        <text class="title-text">加入摸鱼社群</text>
      </view>
      <view class="modal-close" bindtap="onClose">
        <text class="close-icon">✕</text>
      </view>
    </view>

    <!-- 模态框内容 -->
    <view class="modal-body">
      <!-- 加载状态 -->
      <view wx:if="{{loading}}" class="loading-container">
        <view class="loading-icon">⏳</view>
        <text class="loading-text">加载中...</text>
      </view>

      <!-- 错误状态 -->
      <view wx:elif="{{errorMessage}}" class="error-container">
        <view class="error-icon">❌</view>
        <text class="error-text">{{errorMessage}}</text>
        <button class="retry-btn" bindtap="onRetry">重试</button>
      </view>

      <!-- 社群信息内容 -->
      <view wx:else class="community-content">
        <view class="markdown-content">
          <block wx:for="{{parsedContent}}" wx:key="index" wx:for-item="block">

            <!-- 标题 -->
            <view wx:if="{{block.type === 'header'}}" class="md-header md-header-{{block.level}}">
              {{block.text}}
            </view>

            <!-- 段落 -->
            <view wx:elif="{{block.type === 'paragraph'}}" class="md-paragraph">
              <block wx:for="{{block.text}}" wx:key="index" wx:for-item="span">
                <text wx:if="{{span.type === 'text'}}" class="md-text">{{span.text}}</text>
                <text wx:elif="{{span.type === 'bold'}}" class="md-bold">{{span.text}}</text>
                <text wx:elif="{{span.type === 'italic'}}" class="md-italic">{{span.text}}</text>
                <text wx:elif="{{span.type === 'inline-code'}}" class="md-inline-code">{{span.text}}</text>
                <text wx:elif="{{span.type === 'link'}}" class="md-link" data-url="{{span.url}}" bindtap="onLinkTap">{{span.text}}</text>
                <image wx:elif="{{span.type === 'image'}}" class="md-inline-image" src="{{span.url}}" mode="widthFix" data-url="{{span.url}}" show-menu-by-longpress="{{true}}" lazy-load="{{true}}" />
              </block>
            </view>

            <!-- 图片 -->
            <view wx:elif="{{block.type === 'image'}}" class="md-image-container">
              <image class="md-image" src="{{block.url}}" mode="widthFix" data-url="{{block.url}}" show-menu-by-longpress="{{true}}" lazy-load="{{true}}" />
              <view wx:if="{{block.alt}}" class="md-image-caption">{{block.alt}}</view>
            </view>

            <!-- 列表 -->
            <view wx:elif="{{block.type === 'list'}}" class="md-list">
              <view wx:for="{{block.items}}" wx:key="index" wx:for-item="item" class="md-list-item">
                <view class="md-list-bullet">
                  <text wx:if="{{block.listType === 'unordered'}}">•</text>
                  <text wx:else>{{index + 1}}.</text>
                </view>
                <view class="md-list-content">
                  <block wx:for="{{item.text}}" wx:key="index" wx:for-item="span">
                    <text wx:if="{{span.type === 'text'}}" class="md-text">{{span.text}}</text>
                    <text wx:elif="{{span.type === 'bold'}}" class="md-bold">{{span.text}}</text>
                    <text wx:elif="{{span.type === 'italic'}}" class="md-italic">{{span.text}}</text>
                    <text wx:elif="{{span.type === 'inline-code'}}" class="md-inline-code">{{span.text}}</text>
                    <text wx:elif="{{span.type === 'link'}}" class="md-link" data-url="{{span.url}}" bindtap="onLinkTap">{{span.text}}</text>
                    <image wx:elif="{{span.type === 'image'}}" class="md-inline-image" src="{{span.url}}" mode="widthFix" data-url="{{span.url}}" show-menu-by-longpress="{{true}}" lazy-load="{{true}}" />
                  </block>
                </view>
              </view>
            </view>

            <!-- 引用 -->
            <view wx:elif="{{block.type === 'quote'}}" class="md-quote">
              <view class="md-quote-border"></view>
              <view class="md-quote-content">
                <block wx:for="{{block.text}}" wx:key="index" wx:for-item="span">
                  <text wx:if="{{span.type === 'text'}}" class="md-text">{{span.text}}</text>
                  <text wx:elif="{{span.type === 'bold'}}" class="md-bold">{{span.text}}</text>
                  <text wx:elif="{{span.type === 'italic'}}" class="md-italic">{{span.text}}</text>
                  <text wx:elif="{{span.type === 'inline-code'}}" class="md-inline-code">{{span.text}}</text>
                  <text wx:elif="{{span.type === 'link'}}" class="md-link" data-url="{{span.url}}" bindtap="onLinkTap">{{span.text}}</text>
                  <image wx:elif="{{span.type === 'image'}}" class="md-inline-image" src="{{span.url}}" mode="widthFix" data-url="{{span.url}}" show-menu-by-longpress="{{true}}" lazy-load="{{true}}" />
                </block>
              </view>
            </view>

            <!-- 代码块 -->
            <view wx:elif="{{block.type === 'code'}}" class="md-code-block">
              <view wx:if="{{block.language}}" class="md-code-language">{{block.language}}</view>
              <view class="md-code-content">{{block.text}}</view>
            </view>

            <!-- 分割线 -->
            <view wx:elif="{{block.type === 'divider'}}" class="md-divider"></view>

          </block>
        </view>
      </view>
    </view>

    <!-- 模态框底部 -->
    <view class="modal-footer">
      <view class="footer-tip">
        <text class="tip-icon">💡</text>
        <text class="tip-text">长按二维码图片可保存或识别</text>
      </view>
    </view>
  </view>
</view>
