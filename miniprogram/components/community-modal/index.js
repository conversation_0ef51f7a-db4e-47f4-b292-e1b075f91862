// 摸鱼社群模态框组件
import { getConfigManager } from '../../core/managers/config-manager.js'
import { parseMarkdown } from '../../utils/markdown-parser.js'

Component({
  options: {
    addGlobalClass: true
  },

  properties: {
    // 是否显示模态框
    show: {
      type: Boolean,
      value: false
    }
  },

  data: {
    // 控制动画显示
    visible: false,
    // 社群信息内容
    communityInfo: null,
    // 解析后的markdown内容
    parsedContent: [],
    // 加载状态
    loading: true,
    // 错误信息
    errorMessage: ''
  },

  observers: {
    'show': function(show) {
      if (show) {
        this.loadCommunityInfo()
        // 延迟显示动画，确保DOM已渲染
        setTimeout(() => {
          this.setData({ visible: true })
        }, 100)
      } else {
        this.setData({ visible: false })
      }
    }
  },

  methods: {
    /**
     * 加载社群信息
     */
    async loadCommunityInfo() {
      try {
        this.setData({ 
          loading: true, 
          errorMessage: '' 
        })

        const configManager = getConfigManager()
        
        // 确保配置管理器已初始化
        if (!configManager.isLoaded()) {
          await configManager.initialize()
        }

        // 获取社群信息配置
        const communityInfo = configManager.get('community_group_info', null)
        
        if (!communityInfo) {
          throw new Error('未找到社群信息配置')
        }

        // 解析markdown内容
        const parsedContent = parseMarkdown(communityInfo)

        this.setData({
          communityInfo,
          parsedContent,
          loading: false
        })

        console.log('[CommunityModal] 社群信息加载成功:', { communityInfo, parsedContent })

      } catch (error) {
        console.error('[CommunityModal] 加载社群信息失败:', error)
        this.setData({
          loading: false,
          errorMessage: error.message || '加载失败，请稍后重试'
        })
      }
    },

    /**
     * 关闭模态框
     */
    onClose() {
      this.setData({ visible: false })
      
      // 等待动画完成后触发关闭事件
      setTimeout(() => {
        this.triggerEvent('close')
      }, 300)
    },

    /**
     * 阻止事件冒泡
     */
    onStopPropagation() {
      // 阻止点击模态框内容时关闭模态框
    },

    /**
     * 处理链接点击
     */
    onLinkTap(e) {
      const { url } = e.currentTarget.dataset
      if (url) {
        // 如果是小程序内部页面，使用navigateTo
        if (url.startsWith('/pages/')) {
          wx.navigateTo({
            url: url,
            fail: (err) => {
              console.warn('[CommunityModal] 页面跳转失败:', err)
              wx.showToast({
                title: '页面跳转失败',
                icon: 'none'
              })
            }
          })
        } else {
          // 外部链接，复制到剪贴板
          wx.setClipboardData({
            data: url,
            success: () => {
              wx.showToast({
                title: '链接已复制',
                icon: 'success'
              })
            }
          })
        }
      }
    },

    /**
     * 重新加载
     */
    onRetry() {
      this.loadCommunityInfo()
    }
  }
})
