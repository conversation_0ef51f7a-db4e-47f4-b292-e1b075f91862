/* 智能填写收入模态框组件样式 */

/* 模态框基础样式 */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.modal.show {
  opacity: 1;
}

.modal-content {
  background-color: #fff;
  border-radius: 12px;
  width: 90%;
  max-width: 500px;
  max-height: 85%;
  overflow: hidden;
  transform: scale(0.9);
  transition: transform 0.3s ease;
}

.modal.show .modal-content {
  transform: scale(1);
}

.smart-income-modal {
  max-height: 90vh;
}

/* 模态框头部 */
.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px;
  border-bottom: 1px solid #f0f0f0;
}

.modal-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.modal-close {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: #999;
  cursor: pointer;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.modal-close:hover {
  background-color: #f5f5f5;
  color: #666;
}

/* 模态框主体 */
.modal-body {
  padding: 20px;
  max-height: 50vh;
  overflow-y: auto;
}

/* 输入组 */
.input-groups {
  display: flex;
  gap: 12px;
}

.input-group {
  margin-bottom: 20px;
}

.input-label {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 8px;
}

.input-label-with-calculator {
  margin-bottom: 0;
  align-self: flex-end;
}

.input-with-unit {
  display: flex;
  align-items: center;
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 0 12px;
  transition: border-color 0.2s ease;
}

.input-with-unit:focus-within {
  border-color: #06b6d4;
  background-color: #fff;
}

.number-input {
  flex: 1;
  height: 44px;
  font-size: 16px;
  color: #333;
  background: transparent;
  border: none;
  outline: none;
}

.input-unit {
  font-size: 14px;
  color: #666;
  margin-left: 8px;
}

/* 计算模式选择 */
.calculation-modes {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.mode-option {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px;
  border: 2px solid #e9ecef;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  background-color: #fff;
}

.mode-option:hover {
  border-color: #06b6d4;
  background-color: #f0fdff;
}

.mode-option.active {
  border-color: #06b6d4;
  background-color: #e0fbff;
  box-shadow: 0 2px 8px rgba(6, 182, 212, 0.15);
}

.mode-icon {
  font-size: 24px;
}

.mode-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.mode-title {
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.mode-desc {
  font-size: 12px;
  color: #666;
  line-height: 1.4;
}

/* 计算内容区域 */
.calculation-content {
  margin-top: 20px;
}

/* 计算方式选择器 */
.calculation-method-selector {
  display: flex;
  gap: 12px;
  margin-bottom: 20px;
}

.method-option {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  background-color: #fff;
}

.method-option:hover {
  border-color: #06b6d4;
  background-color: #f0fdff;
}

.method-option.active {
  border-color: #06b6d4;
  background-color: #e0fbff;
}

.method-icon {
  font-size: 20px;
  margin-bottom: 6px;
}

.method-title {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 2px;
}

.method-desc {
  font-size: 12px;
  color: #666;
  text-align: center;
  line-height: 1.3;
}

/* 日收入计算器按钮 */
.input-with-calculator {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.daily-income-calculator-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  background: #00a381;
  color: white;
  border-radius: 6px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.daily-income-calculator-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(6, 182, 212, 0.3);
}

.calculator-icon {
  font-size: 12px;
}

.calculator-text {
  font-weight: 500;
}

/* 模态框底部 */
.modal-footer {
  display: flex;
  gap: 12px;
  padding: 20px;
  border-top: 1px solid #f0f0f0;
  background-color: #fafafa;
}

.modal-btn {
  flex: 1;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  transition: all 0.2s ease;
  cursor: pointer;
}

.btn-secondary {
  background-color: #f8f9fa;
  color: #666;
  border: 1px solid #e9ecef;
}

.btn-secondary:hover {
  background-color: #e9ecef;
  color: #333;
}

.btn-primary {
  background: linear-gradient(135deg, #06b6d4 0%, #10b981 100%);
  color: white;
  border: 1px solid #06b6d4;
}

.btn-primary:hover {
  background-color: #0ea5b3;
  border-color: #0ea5b3;
}
