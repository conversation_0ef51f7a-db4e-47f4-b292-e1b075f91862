// 智能填写收入模态框组件
const { CurrencyUtils } = require('../../utils/currency-utils.js')

Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 是否显示
    visible: {
      type: Boolean,
      value: false
    },
    // 时间段数据
    timeInputs: {
      type: Array,
      value: []
    },
    // 当前工作ID
    currentWorkId: {
      type: String,
      value: ''
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    // 计算模式：total, overtime, hourly
    smartIncomeMode: 'total',

    // 总收入分配模式
    smartIncomeTotalAmount: '',
    smartIncomeTotalAmountText: '',

    // 加班倍率模式
    smartIncomeBaseHourly: '',
    smartIncomeBaseHourlyText: '',
    smartIncomeOvertimeRate: '1.5',
    smartIncomeOvertimeRateText: '1.5',
    smartIncomeOvertimeCalculationMethod: 'hourly', // hourly: 基础时薪方式, total: 总收入方式
    smartIncomeOvertimeTotalAmount: '',
    smartIncomeOvertimeTotalAmountText: '',

    // 分类时薪模式
    smartIncomeWorkHourly: '',
    smartIncomeWorkHourlyText: '',
    smartIncomeOvertimeHourly: '',
    smartIncomeOvertimeHourlyText: '',

    // 模态框动画状态
    modalVisible: false,

    // 日收入计算器相关
    showDailyIncomeCalculator: false,
    dailyIncomeCalculatorMonthlyIncome: 0,
    dailyIncomeCalculatorTargetMode: 'hourly',

    // 货币设置
    currencyUnit: '元',
    currencyHourlyUnit: '元/小时'
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 监听属性变化
     */
    _onPropertiesChange() {
      console.log('智能收入模态框 - 属性变化', {
        visible: this.data.visible,
        timeInputs: this.data.timeInputs?.length
      })

      if (this.data.visible) {
        this._initializeModal()
      } else {
        this.setData({ modalVisible: false })
      }
    },

    /**
     * 初始化模态框
     */
    _initializeModal() {
      console.log('智能收入模态框 - 初始化', {
        timeInputs: this.data.timeInputs,
        timeInputsLength: this.data.timeInputs?.length
      })

      // 加载货币设置
      this._loadCurrencySettings()

      // 重置所有输入值
      this.setData({
        smartIncomeMode: 'total',
        smartIncomeTotalAmount: '',
        smartIncomeTotalAmountText: '',
        smartIncomeBaseHourly: '',
        smartIncomeBaseHourlyText: '',
        smartIncomeOvertimeRate: '1.5',
        smartIncomeOvertimeRateText: '1.5',
        smartIncomeOvertimeCalculationMethod: 'hourly',
        smartIncomeOvertimeTotalAmount: '',
        smartIncomeOvertimeTotalAmountText: '',
        smartIncomeWorkHourly: '',
        smartIncomeWorkHourlyText: '',
        smartIncomeOvertimeHourly: '',
        smartIncomeOvertimeHourlyText: ''
      }, () => {
        // 延迟显示动画
        setTimeout(() => {
          this.setData({ modalVisible: true })
        }, 50)
      })
    },

    /**
     * 加载货币设置
     */
    _loadCurrencySettings() {
      try {
        CurrencyUtils.loadCurrencySettingsForContext(this, {
          includeUnit: true,
          includeHourlyUnit: true
        })
      } catch (error) {
        console.error('加载货币设置失败:', error)
        // 使用默认值
        this.setData({
          currencyUnit: '元',
          currencyHourlyUnit: '元/小时'
        })
      }
    },

    /**
     * 选择收入计算模式
     */
    onSelectIncomeMode(e) {
      const { mode } = e.currentTarget.dataset
      this.setData({
        smartIncomeMode: mode
      })
    },

    /**
     * 选择加班倍率计算方式
     */
    onSelectOvertimeCalculationMethod(e) {
      const { method } = e.currentTarget.dataset
      this.setData({
        smartIncomeOvertimeCalculationMethod: method
      })
    },

    /**
     * 总收入输入变化
     */
    onTotalAmountChange(e) {
      let value = this._limitDecimalPlaces(e.detail.value, 2)
      const numValue = this._fixFloatPrecision(parseFloat(value) || 0)

      if (numValue > 1000000000) {
        wx.showToast({
          title: '总收入不能超过 1000,000,000',
          icon: 'none'
        })
        return
      }

      this.setData({
        smartIncomeTotalAmount: value,
        smartIncomeTotalAmountText: value
      })
    },

    /**
     * 基础时薪输入变化
     */
    onBaseHourlyChange(e) {
      let value = this._limitDecimalPlaces(e.detail.value, 2)
      const numValue = this._fixFloatPrecision(parseFloat(value) || 0)

      if (numValue > 10000) {
        wx.showToast({
          title: '基础时薪不能超过 10,000',
          icon: 'none'
        })
        return
      }

      this.setData({
        smartIncomeBaseHourly: value,
        smartIncomeBaseHourlyText: value
      })
    },

    /**
     * 加班倍率输入变化
     */
    onOvertimeRateChange(e) {
      let value = this._limitDecimalPlaces(e.detail.value, 2)
      const numValue = this._fixFloatPrecision(parseFloat(value) || 0)

      if (numValue > 10) {
        wx.showToast({
          title: '加班倍率不能超过10倍',
          icon: 'none'
        })
        return
      }

      this.setData({
        smartIncomeOvertimeRate: value,
        smartIncomeOvertimeRateText: value
      })
    },

    /**
     * 加班总收入输入变化
     */
    onOvertimeTotalAmountChange(e) {
      let value = this._limitDecimalPlaces(e.detail.value, 2)
      const numValue = this._fixFloatPrecision(parseFloat(value) || 0)

      if (numValue > 1000000000) {
        wx.showToast({
          title: '总收入不能超过 1000,000,000',
          icon: 'none'
        })
        return
      }

      this.setData({
        smartIncomeOvertimeTotalAmount: value,
        smartIncomeOvertimeTotalAmountText: value
      })
    },

    /**
     * 工作时薪输入变化
     */
    onWorkHourlyChange(e) {
      let value = this._limitDecimalPlaces(e.detail.value, 2)
      const numValue = this._fixFloatPrecision(parseFloat(value) || 0)

      if (numValue > 10000) {
        wx.showToast({
          title: '工作时薪不能超过 10,000',
          icon: 'none'
        })
        return
      }

      this.setData({
        smartIncomeWorkHourly: value,
        smartIncomeWorkHourlyText: value
      })
    },

    /**
     * 加班时薪输入变化
     */
    onOvertimeHourlyChange(e) {
      let value = this._limitDecimalPlaces(e.detail.value, 2)
      const numValue = this._fixFloatPrecision(parseFloat(value) || 0)

      if (numValue > 10000) {
        wx.showToast({
          title: '加班时薪不能超过 10,000',
          icon: 'none'
        })
        return
      }

      this.setData({
        smartIncomeOvertimeHourly: value,
        smartIncomeOvertimeHourlyText: value
      })
    },

    /**
     * 打开日收入计算器（总收入分配模式）
     */
    onOpenDailyIncomeCalculator() {
      console.log('智能收入组件：打开日收入计算器（总收入分配模式）')

      // 获取当前工作的月薪作为默认值
      const currentMonthlySalary = this._getCurrentWorkMonthlySalary()

      this.setData({
        dailyIncomeCalculatorMonthlyIncome: currentMonthlySalary,
        dailyIncomeCalculatorTargetMode: 'total',
        showDailyIncomeCalculator: true
      })
    },

    /**
     * 打开日收入计算器（加班倍率模式）
     */
    onOpenDailyIncomeCalculatorForOvertime() {
      console.log('智能收入组件：打开日收入计算器（加班倍率模式）')

      // 获取当前工作的月薪作为默认值
      const currentMonthlySalary = this._getCurrentWorkMonthlySalary()

      this.setData({
        dailyIncomeCalculatorMonthlyIncome: currentMonthlySalary,
        dailyIncomeCalculatorTargetMode: 'overtime',
        showDailyIncomeCalculator: true
      })
    },

    /**
     * 获取当前工作的月薪
     */
    _getCurrentWorkMonthlySalary() {
      // 通过页面实例获取当前工作信息
      const pages = getCurrentPages()
      if (pages.length > 0) {
        const currentPage = pages[pages.length - 1]
        if (currentPage.getCurrentWorkMonthlySalary) {
          return currentPage.getCurrentWorkMonthlySalary()
        }
      }
      return 5000 // 默认值
    },

    /**
     * 日收入计算器确认
     */
    onDailyIncomeCalculatorConfirm(e) {
      const { result } = e.detail
      console.log('智能收入组件：日收入计算器确认', { result })

      // 根据目标模式更新对应的输入值
      const targetMode = this.data.dailyIncomeCalculatorTargetMode

      this.fillDailyIncomeResult(result, targetMode)

      this.setData({
        showDailyIncomeCalculator: false
      })
    },

    /**
     * 日收入计算器取消
     */
    onDailyIncomeCalculatorCancel() {
      this.setData({
        showDailyIncomeCalculator: false
      })
    },

    /**
     * 日收入计算器结果回填
     */
    fillDailyIncomeResult(result, targetMode) {
      const fixedResult = this._fixFloatPrecision(result)
      const resultString = fixedResult.toString()

      if (targetMode === 'overtime') {
        this.setData({
          smartIncomeOvertimeTotalAmount: resultString,
          smartIncomeOvertimeTotalAmountText: resultString
        })
      } else {
        this.setData({
          smartIncomeTotalAmount: resultString,
          smartIncomeTotalAmountText: resultString
        })
      }
    },

    /**
     * 确认应用计算
     */
    onConfirm() {
      const timeInputs = JSON.parse(JSON.stringify(this.properties.timeInputs)) // 深拷贝

      try {
        switch (this.data.smartIncomeMode) {
          case 'total':
            this._calculateTotalIncomeDistribution(timeInputs)
            break
          case 'overtime':
            this._calculateOvertimeRateIncome(timeInputs)
            break
          case 'hourly':
            this._calculateHourlyRateIncome(timeInputs)
            break
        }

        this.triggerEvent('confirm', {
          timeInputs,
          calculationInfo: {
            mode: this.data.smartIncomeMode,
            parameters: this._getCalculationParameters()
          }
        })

        this.onClose()

        wx.showToast({
          title: '收入计算完成',
          icon: 'success'
        })
      } catch (error) {
        console.error('收入计算失败:', error)
        wx.showToast({
          title: error.message || '计算失败',
          icon: 'none'
        })
      }
    },

    /**
     * 取消
     */
    onCancel() {
      this.triggerEvent('cancel')
      this.onClose()
    },

    /**
     * 关闭模态框
     */
    onClose() {
      // 开始出场动画
      this.setData({ modalVisible: false })

      // 等待动画完成后触发关闭事件
      setTimeout(() => {
        this.triggerEvent('close')
      }, 300) // 与CSS动画时长一致
    },

    /**
     * 阻止事件冒泡
     */
    onStopPropagation() {
      // 阻止点击模态框内容时关闭模态框
    },

    /**
     * 获取计算参数
     */
    _getCalculationParameters() {
      switch (this.data.smartIncomeMode) {
        case 'total':
          return {
            totalAmount: parseFloat(this.data.smartIncomeTotalAmount) || 0
          }
        case 'overtime':
          return {
            calculationMethod: this.data.smartIncomeOvertimeCalculationMethod,
            baseHourly: parseFloat(this.data.smartIncomeBaseHourly) || 0,
            overtimeRate: parseFloat(this.data.smartIncomeOvertimeRate) || 1.5,
            totalAmount: parseFloat(this.data.smartIncomeOvertimeTotalAmount) || 0
          }
        case 'hourly':
          return {
            workHourly: parseFloat(this.data.smartIncomeWorkHourly) || 0,
            overtimeHourly: parseFloat(this.data.smartIncomeOvertimeHourly) || 0
          }
        default:
          return {}
      }
    },

    /**
     * 计算时间段持续时间（分钟）
     */
    _calculateInputDuration(input) {
      const startTime = input.startTime
      const endTime = input.endTime

      if (!startTime || !endTime) return 0

      const [startHour, startMinute] = startTime.split(':').map(Number)
      const [endHour, endMinute] = endTime.split(':').map(Number)

      let startMinutes = startHour * 60 + startMinute
      let endMinutes = endHour * 60 + endMinute

      // 处理跨日情况
      if (input.isStartNextDay) {
        startMinutes += 24 * 60
      }
      if (input.isEndNextDay) {
        endMinutes += 24 * 60
      }

      // 如果结束时间小于开始时间，说明跨日了
      if (endMinutes <= startMinutes && !input.isEndNextDay) {
        endMinutes += 24 * 60
      }

      return Math.max(0, endMinutes - startMinutes)
    },

    /**
     * 按总收入分配计算
     */
    _calculateTotalIncomeDistribution(timeInputs) {
      const totalAmount = this._fixFloatPrecision(parseFloat(this.data.smartIncomeTotalAmount) || 0)
      if (totalAmount <= 0) {
        throw new Error('请输入有效的总收入')
      }

      // 计算总工作时间（排除休息）
      let totalWorkMinutes = 0
      const workInputs = []
      timeInputs.forEach(input => {
        if (input.type !== 'rest') {
          const duration = this._calculateInputDuration(input)
          totalWorkMinutes += duration
          workInputs.push({ input, duration })
        }
      })

      if (totalWorkMinutes <= 0) {
        throw new Error('没有工作时间段')
      }

      // 按时间比例分配收入，使用累积误差修正算法
      let allocatedIncome = 0
      workInputs.forEach((workInput, index) => {
        const { input, duration } = workInput

        if (index === workInputs.length - 1) {
          // 最后一个工作时间段：用总收入减去已分配的收入，避免累积误差
          input.income = this._fixFloatPrecision(totalAmount - allocatedIncome)
        } else {
          // 前面的时间段：按比例分配
          const ratio = duration / totalWorkMinutes
          input.income = this._fixFloatPrecision(totalAmount * ratio)
          allocatedIncome = this._fixFloatPrecision(allocatedIncome + input.income)
        }

        input.incomeText = input.income.toString()
        input.hourlyRate = this._fixFloatPrecision(input.income / (duration / 60))
        input.hourlyRateText = input.hourlyRate.toFixed(2)
        input._lastUpdatedBy = 'income' // 标记为基于收入计算
      })

      // 处理休息时间段
      timeInputs.forEach(input => {
        if (input.type === 'rest') {
          input.income = 0
          input.incomeText = ''
          input.hourlyRate = 0
          input.hourlyRateText = ''
        }
      })
    },

    /**
     * 按加班倍率计算
     */
    _calculateOvertimeRateIncome(timeInputs) {
      const overtimeRate = parseFloat(this.data.smartIncomeOvertimeRate) || 1.5
      const calculationMethod = this.data.smartIncomeOvertimeCalculationMethod

      if (calculationMethod === 'hourly') {
        // 基础时薪方式
        this._calculateOvertimeRateByHourly(timeInputs, overtimeRate)
      } else {
        // 总收入方式
        this._calculateOvertimeRateByTotal(timeInputs, overtimeRate)
      }
    },

    /**
     * 基础时薪方式计算加班倍率
     */
    _calculateOvertimeRateByHourly(timeInputs, overtimeRate) {
      const baseHourly = this._fixFloatPrecision(parseFloat(this.data.smartIncomeBaseHourly) || 0)

      if (baseHourly <= 0) {
        throw new Error('请输入有效的基础时薪')
      }

      timeInputs.forEach(input => {
        if (input.type === 'rest') {
          input.income = 0
          input.hourlyRate = 0
          input.hourlyRateText = ''
        } else {
          const duration = this._calculateInputDuration(input)
          const hours = duration / 60

          if (input.type === 'overtime') {
            input.hourlyRate = this._fixFloatPrecision(baseHourly * overtimeRate)
          } else {
            input.hourlyRate = this._fixFloatPrecision(baseHourly)
          }

          input.income = this._fixFloatPrecision(hours * input.hourlyRate)
          input.incomeText = input.income.toString()
          input.hourlyRateText = input.hourlyRate.toFixed(2)
          input._lastUpdatedBy = 'income' // 标记为基于收入计算
        }
      })
    },

    /**
     * 修复浮点数精度问题
     * @param {number} num - 数字
     * @param {number} decimals - 小数位数，默认2位
     * @returns {number} 修复精度后的数字
     */
    _fixFloatPrecision(num, decimals = 2) {
      return Math.round(num * Math.pow(10, decimals)) / Math.pow(10, decimals)
    },

    /**
     * 限制小数位数
     */
    _limitDecimalPlaces(value, maxDecimalPlaces) {
      if (!value || value === '') return ''

      // 移除非数字和小数点的字符
      value = value.replace(/[^\d.]/g, '')

      // 处理以小数点开头的情况：.123 -> 0.123
      if (value.startsWith('.')) {
        value = '0' + value
      }

      // 确保只有一个小数点
      const parts = value.split('.')
      if (parts.length > 2) {
        value = parts[0] + '.' + parts.slice(1).join('').replace(/\./g, '')
      }

      // 重新分割以获取正确的部分
      const finalParts = value.split('.')

      // 处理前导零的情况
      if (finalParts[0].length > 1 && finalParts[0].startsWith('0')) {
        finalParts[0] = finalParts[0].replace(/^0+/, '') || '0'
        value = finalParts.join('.')
      }

      // 限制小数位数
      if (finalParts.length === 2 && finalParts[1].length > maxDecimalPlaces) {
        const integerPart = finalParts[0]
        const decimalPart = finalParts[1].substring(0, maxDecimalPlaces)
        value = integerPart + '.' + decimalPart
      }

      return value
    },

    /**
     * 总收入方式计算加班倍率
     */
    _calculateOvertimeRateByTotal(timeInputs, overtimeRate) {
      const totalAmount = this._fixFloatPrecision(parseFloat(this.data.smartIncomeOvertimeTotalAmount) || 0)

      if (totalAmount <= 0) {
        throw new Error('请输入有效的总收入')
      }

      // 计算总工作时间和加班时间
      let totalWorkMinutes = 0
      let totalOvertimeMinutes = 0
      let totalNormalMinutes = 0
      const workInputs = []

      timeInputs.forEach(input => {
        if (input.type !== 'rest') {
          const duration = this._calculateInputDuration(input)
          totalWorkMinutes += duration
          workInputs.push({ input, duration })

          if (input.type === 'overtime') {
            totalOvertimeMinutes += duration
          } else {
            totalNormalMinutes += duration
          }
        }
      })

      if (totalWorkMinutes === 0) {
        throw new Error('没有工作时间段')
      }

      // 计算加权总小时数：正常工作时间 + 加班时间 * 倍率
      const normalHours = totalNormalMinutes / 60
      const overtimeHours = totalOvertimeMinutes / 60
      const weightedTotalHours = normalHours + (overtimeHours * overtimeRate)

      if (weightedTotalHours === 0) {
        throw new Error('没有有效的工作时间')
      }

      const baseHourlyRate = this._fixFloatPrecision(totalAmount / weightedTotalHours)

      // 为每个时间段分配收入，使用累积误差修正算法
      let allocatedIncome = 0
      workInputs.forEach((workInput, index) => {
        const { input, duration } = workInput
        const hours = duration / 60

        if (input.type === 'overtime') {
          input.hourlyRate = this._fixFloatPrecision(baseHourlyRate * overtimeRate)
        } else {
          input.hourlyRate = this._fixFloatPrecision(baseHourlyRate)
        }

        if (index === workInputs.length - 1) {
          // 最后一个工作时间段：用总收入减去已分配的收入，避免累积误差
          input.income = this._fixFloatPrecision(totalAmount - allocatedIncome)
        } else {
          input.income = this._fixFloatPrecision(hours * input.hourlyRate)
          allocatedIncome = this._fixFloatPrecision(allocatedIncome + input.income)
        }

        input.incomeText = input.income.toString()
        input.hourlyRateText = input.hourlyRate.toFixed(2)
        input._lastUpdatedBy = 'income' // 标记为基于收入计算
      })

      // 处理休息时间段
      timeInputs.forEach(input => {
        if (input.type === 'rest') {
          input.income = 0
          input.hourlyRate = 0
          input.hourlyRateText = ''
        }
      })
    },

    /**
     * 按分类时薪计算
     */
    _calculateHourlyRateIncome(timeInputs) {
      const workHourly = this._fixFloatPrecision(parseFloat(this.data.smartIncomeWorkHourly) || 0)
      const overtimeHourly = this._fixFloatPrecision(parseFloat(this.data.smartIncomeOvertimeHourly) || 0)

      if (workHourly <= 0 && overtimeHourly <= 0) {
        throw new Error('请至少输入一种时薪')
      }

      timeInputs.forEach(input => {
        if (input.type === 'rest') {
          input.income = 0
          input.hourlyRate = 0
          input.hourlyRateText = ''
        } else {
          const duration = this._calculateInputDuration(input)
          const hours = duration / 60

          if (input.type === 'overtime' && overtimeHourly > 0) {
            input.hourlyRate = overtimeHourly
          } else if (input.type === 'work' && workHourly > 0) {
            input.hourlyRate = workHourly
          } else {
            // 如果没有对应的时薪设置，使用另一种时薪或0
            input.hourlyRate = workHourly > 0 ? workHourly : (overtimeHourly > 0 ? overtimeHourly : 0)
          }

          input.income = this._fixFloatPrecision(hours * input.hourlyRate)
          input.incomeText = input.income.toString()
          input.hourlyRateText = input.hourlyRate.toFixed(2)
          input._lastUpdatedBy = 'income' // 标记为基于收入计算
        }
      })
    }
  },

  /**
   * 组件生命周期
   */
  observers: {
    'visible': function(visible) {
      this._onPropertiesChange()
    }
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached() {
      // 组件实例进入页面节点树时，注册全局回填方法
      if (typeof getApp === 'function') {
        const app = getApp()
        if (!app.globalData) app.globalData = {}
        app.globalData.smartIncomeComponent = this
      }
    },

    detached() {
      // 组件实例被从页面节点树移除时，清除全局引用
      if (typeof getApp === 'function') {
        const app = getApp()
        if (app.globalData) {
          app.globalData.smartIncomeComponent = null
        }
      }
    }
  }
})
