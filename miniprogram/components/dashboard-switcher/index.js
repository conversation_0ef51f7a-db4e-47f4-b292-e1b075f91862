// 主题切换器组件
const { DashboardService } = require('../../core/services/dashboard-service.js')
const { checkThemeSwitchBenefit } = require('../../utils/pro-benefits.js')

Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 是否显示
    show: {
      type: Boolean,
      value: false
    },
    // 当前选择的主题
    currentDashboard: {
      type: String,
      value: ''
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    // 可用的主题列表
    availableDashboards: [],
    // 控制动画显示
    visible: false,
    // 用户信息
    userInfo: null
  },

  observers: {
    'show': function(show) {
      if (show) {
        // 延迟显示动画，确保DOM已渲染
        setTimeout(() => {
          this.setData({ visible: true })
        }, 50)
      } else {
        this.setData({ visible: false })
      }
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 初始化组件
     */
    init: function() {
      this.dashboardService = new DashboardService()
      this.loadUserInfo()
      this.loadAvailableDashboards()
    },

    /**
     * 加载用户信息
     */
    loadUserInfo: function() {
      try {
        const app = getApp()
        const userManager = app.getUserManager()
        if (userManager) {
          const userInfo = userManager.getUserInfo()
          this.setData({ userInfo })
        }
      } catch (error) {
        console.error('加载用户信息失败:', error)
      }
    },

    /**
     * 加载可用的主题列表
     */
    loadAvailableDashboards: function() {
      try {
        const allDashboards = this.dashboardService.getAllDashboards()
        const themeAccess = checkThemeSwitchBenefit()

        // 为每个主题添加权益信息
        const availableDashboards = allDashboards.map(dashboard => ({
          ...dashboard,
          isLocked: !themeAccess.hasAccess && dashboard.id !== 'dashboard1', // 默认主题不锁定
          needsPro: !themeAccess.hasAccess && dashboard.id !== 'dashboard1'
        }))

        this.setData({
          availableDashboards: availableDashboards
        })
      } catch (error) {
        console.error('加载主题列表失败:', error)
      }
    },

    /**
     * 隐藏切换器
     */
    onHide: function() {
      this.triggerEvent('hide')
    },

    /**
     * 模态框内容点击（阻止冒泡）
     */
    onModalContentTap: function() {
      // 阻止事件冒泡，防止关闭模态框
    },

    /**
     * 选择主题
     */
    onSelectDashboard: function(event) {
      const dashboardId = event.currentTarget.dataset.dashboard
      const isLocked = event.currentTarget.dataset.locked

      console.log('选择主题:', dashboardId, '是否锁定:', isLocked)

      // 检查权益
      if (isLocked) {
        wx.showModal({
          title: '主题权益',
          content: '此主题需要 Pro 会员才能使用\n\n升级 Pro 会员即可解锁所有主题！',
          confirmText: '升级 Pro',
          cancelText: '取消',
          success: (res) => {
            if (res.confirm) {
              wx.navigateTo({
                url: '/pages/membership/index'
              })
            }
          }
        })
        return
      }

      try {
        this.dashboardService.setCurrentDashboard(dashboardId)

        // 隐藏模态框
        this.setData({
          show: false
        })

        // 触发选择事件
        this.triggerEvent('dashboardChange', {
          dashboardId: dashboardId
        })
      } catch (error) {
        console.error('切换主题失败:', error)
        wx.showToast({
          title: '切换失败',
          icon: 'error'
        })
      }
    }
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached: function() {
      this.init()
    }
  },

  /**
   * 监听属性变化
   */
  observers: {
    'currentDashboard': function(newVal) {
      console.log('当前主题变化:', newVal)
    },
    'show': function(newVal) {
      if (newVal) {
        console.log('主题切换器显示，加载数据')
        this.loadAvailableDashboards()
      }
    }
  }
})
