// 工作履历模态框组件
const { formatDateToYYYYMMDD } = require('../../utils/helpers/time-utils.js')

Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 是否显示模态框
    visible: {
      type: Boolean,
      value: false,
      observer: 'onVisibleChange'
    },
    // 编辑的工作履历ID（空字符串表示新增）
    editingWorkId: {
      type: String,
      value: '',
      observer: 'onEditingWorkIdChange'
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    modalVisible: false, // 控制动画显示
    
    // 表单数据
    formData: {
      company: '',
      position: '',
      startDate: '',
      probationSalary: '',
      probationEndDate: '',
      formalSalary: '',
      endDate: '',
      notes: '',
      payDays: []
    },
    
    // 表单验证错误
    errors: {}
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached() {
      // 获取工作履历服务
      this.workHistoryService = getApp().getWorkHistoryService()
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 监听显示状态变化
     */
    onVisibleChange(newVal) {
      if (newVal) {
        // 显示模态框时启动入场动画
        setTimeout(() => {
          this.setData({ modalVisible: true })
        }, 50)

        // 如果是新增模式（editingWorkId为空），确保表单被正确初始化
        if (!this.properties.editingWorkId || !this.properties.editingWorkId.trim()) {
          this.resetForm()
        }
      } else {
        // 隐藏时重置数据
        this.resetForm()
      }
    },

    /**
     * 监听编辑工作履历ID变化
     */
    onEditingWorkIdChange(newVal) {

      if (newVal && newVal.trim()) {
        // 编辑模式，通过ID获取工作履历数据
        const work = this.workHistoryService.getWorkHistory(newVal)

        if (work) {
          const formattedStartDate = work.startDate ? this.formatDateForPicker(work.startDate) : ''
          const formattedProbationEndDate = work.probationEndDate ? this.formatDateForPicker(work.probationEndDate) : ''
          const formattedEndDate = work.endDate ? this.formatDateForPicker(work.endDate) : ''

          this.setData({
            formData: {
              company: work.company || '',
              position: work.position || '',
              startDate: formattedStartDate,
              probationSalary: work.probationSalary ? work.probationSalary.toString() : '',
              probationEndDate: formattedProbationEndDate,
              formalSalary: work.formalSalary ? work.formalSalary.toString() : '',
              endDate: formattedEndDate,
              notes: work.notes || '',
              payDays: work.payDays ? this.processPayDaysForDisplay(work.payDays) : []
            },
            errors: {}
          })
        } else {
          this.resetForm()
        }
      } else {
        // 新增模式，重置表单
        this.resetForm()
      }
    },

    /**
     * 格式化日期为选择器格式
     */
    formatDateForPicker(date) {

      if (!date) {
        return ''
      }

      // 如果已经是字符串格式且符合 YYYY-MM-DD，直接返回
      if (typeof date === 'string' && /^\d{4}-\d{2}-\d{2}$/.test(date)) {
        return date
      }

      // 如果是 Date 对象或其他格式，转换为 Date 对象再格式化
      const dateObj = date instanceof Date ? date : new Date(date)

      if (isNaN(dateObj.getTime())) {
        return ''
      }

      return formatDateToYYYYMMDD(dateObj)
    },

    /**
     * 处理发薪日数据，添加显示用的计算字段
     */
    processPayDaysForDisplay(payDays) {
      return payDays.map(payDay => ({
        ...payDay,
        // 添加显示用的计算字段
        isPositive: payDay.day > 0,
        absDay: Math.abs(payDay.day),
        pickerValue: Math.abs(payDay.day) - 1,  // picker的索引值（0-30）
        displayText: payDay.day > 0 ? payDay.day.toString() : Math.abs(payDay.day).toString()
      }))
    },

    /**
     * 清理发薪日数据，只保留核心字段用于保存
     */
    cleanPayDaysForSave(payDays) {
      return payDays.map(payDay => ({
        day: payDay.day,
        name: payDay.name
      }))
    },

    /**
     * 重置表单数据
     */
    resetForm() {
      // 获取一个月前的日期作为默认入职日期
      const oneMonthAgo = new Date()
      oneMonthAgo.setMonth(oneMonthAgo.getMonth() - 1)
      const defaultStartDate = formatDateToYYYYMMDD(oneMonthAgo)

      this.setData({
        formData: {
          company: '神秘单位',
          position: '核心岗位',
          startDate: defaultStartDate,
          probationSalary: '',
          probationEndDate: '',
          formalSalary: '',
          endDate: '',
          notes: '',
          payDays: []
        },
        errors: {}
      })
    },

    /**
     * 隐藏模态框
     */
    hideModal() {
      // 开始出场动画
      this.setData({ modalVisible: false })

      // 等待动画完成后触发关闭事件
      setTimeout(() => {
        this.triggerEvent('close')
      }, 300)
    },

    /**
     * 阻止模态框关闭
     */
    preventModalClose() {
      // 空方法，阻止事件冒泡
    },

    /**
     * 输入框内容变化
     */
    onInputChange(e) {
      const field = e.currentTarget.dataset.field
      const value = e.detail.value
      
      const formData = { ...this.data.formData }
      formData[field] = value
      
      // 清除该字段的错误信息
      const errors = { ...this.data.errors }
      if (errors[field]) {
        delete errors[field]
      }
      
      this.setData({
        formData,
        errors
      })
    },

    /**
     * 工资输入框变化（限制小数位数）
     */
    onSalaryInputChange(e) {
      const field = e.currentTarget.dataset.field
      let value = e.detail.value

      // 限制小数位数最多2位
      value = this.limitDecimalPlaces(value, 2)

      const formData = { ...this.data.formData }
      formData[field] = value

      // 清除该字段的错误信息
      const errors = { ...this.data.errors }
      if (errors[field]) {
        delete errors[field]
      }

      this.setData({
        formData,
        errors
      })
    },

    /**
     * 限制小数位数
     */
    limitDecimalPlaces(value, decimalPlaces) {
      if (!value) return value
      
      // 移除非数字和小数点的字符
      value = value.replace(/[^\d.]/g, '')
      
      // 确保只有一个小数点
      const parts = value.split('.')
      if (parts.length > 2) {
        value = parts[0] + '.' + parts.slice(1).join('')
      }
      
      // 限制小数位数
      if (parts.length === 2 && parts[1].length > decimalPlaces) {
        value = parts[0] + '.' + parts[1].substring(0, decimalPlaces)
      }
      
      return value
    },

    /**
     * 日期选择器变化
     */
    onDateChange(e) {
      const field = e.currentTarget.dataset.field
      const value = e.detail.value
      
      const formData = { ...this.data.formData }
      formData[field] = value
      
      // 清除该字段的错误信息
      const errors = { ...this.data.errors }
      if (errors[field]) {
        delete errors[field]
      }
      
      this.setData({
        formData,
        errors
      })
    },

    /**
     * 清除日期
     */
    clearDate(e) {
      const field = e.currentTarget.dataset.field

      const formData = { ...this.data.formData }
      formData[field] = ''

      // 清除该字段的错误信息
      const errors = { ...this.data.errors }
      if (errors[field]) {
        delete errors[field]
      }

      this.setData({
        formData,
        errors
      })
    },

    /**
     * 添加发薪日
     */
    addPayDay() {
      const formData = { ...this.data.formData }

      // 找一个未使用的日期（优先使用正数天数）
      let newDay = 15
      const existingDays = formData.payDays.map(p => Math.abs(p.day))

      for (let day = 1; day <= 31; day++) {
        if (!existingDays.includes(day)) {
          newDay = day
          break
        }
      }

      const newPayDay = {
        day: newDay,  // 默认为正数（第几号）
        name: '发薪日'
      }

      // 处理新添加的发薪日，添加显示字段
      formData.payDays.push(this.processPayDaysForDisplay([newPayDay])[0])

      this.setData({ formData })
    },

    /**
     * 删除发薪日
     */
    removePayDay(e) {
      const index = e.currentTarget.dataset.index
      const formData = { ...this.data.formData }
      formData.payDays.splice(index, 1)

      this.setData({ formData })
    },

    /**
     * 更新发薪日字段
     */
    onPayDayChange(e) {
      const { index, field } = e.currentTarget.dataset
      const value = e.detail.value
      const formData = { ...this.data.formData }

      if (field === 'type') {
        // 类型选择器：0=第几号，1=倒数第几天
        const currentDay = Math.abs(formData.payDays[index].day)
        if (parseInt(value) === 0) {
          // 选择"第几号"，转为正数
          formData.payDays[index].day = currentDay
        } else {
          // 选择"倒数第几天"，转为负数
          formData.payDays[index].day = -currentDay
        }
      } else if (field === 'day') {
        // 天数选择器：picker返回的是索引，需要转换为实际日期（1-31）
        const dayValue = parseInt(value) + 1
        const currentDay = formData.payDays[index].day
        if (currentDay > 0) {
          // 当前是正数（第几号），保持正数
          formData.payDays[index].day = dayValue
        } else {
          // 当前是负数（倒数第几天），保持负数
          formData.payDays[index].day = -dayValue
        }
      } else if (field === 'name') {
        formData.payDays[index].name = value
      }

      // 重新处理发薪日数据，更新显示字段
      formData.payDays = this.processPayDaysForDisplay(formData.payDays)

      this.setData({ formData })
    },

    /**
     * 表单验证
     */
    validateForm() {
      const { formData } = this.data
      const errors = {}
      
      // 必填字段验证
      if (!formData.company.trim()) {
        errors.company = '请输入工作单位'
      }
      
      if (!formData.position.trim()) {
        errors.position = '请输入工作职位'
      }
      
      if (!formData.startDate) {
        errors.startDate = '请选择入职日期'
      }
      
      // 日期逻辑验证
      if (formData.startDate && formData.probationEndDate) {
        const startDate = new Date(formData.startDate)
        const probationEndDate = new Date(formData.probationEndDate)
        
        if (probationEndDate <= startDate) {
          errors.probationEndDate = '转正日期必须在入职日期之后'
        }
      }
      
      if (formData.startDate && formData.endDate) {
        const startDate = new Date(formData.startDate)
        const endDate = new Date(formData.endDate)
        
        if (endDate <= startDate) {
          errors.endDate = '离职日期必须在入职日期之后'
        }
      }
      
      // 工资验证
      if (formData.probationSalary) {
        const probationSalaryNum = Number(formData.probationSalary)
        if (isNaN(probationSalaryNum)) {
          errors.probationSalary = '请输入有效的试用工资'
        } else if (probationSalaryNum < 0) {
          errors.probationSalary = '试用工资不能为负数'
        }
      }

      if (formData.formalSalary) {
        const formalSalaryNum = Number(formData.formalSalary)
        if (isNaN(formalSalaryNum)) {
          errors.formalSalary = '请输入有效的正式工资'
        } else if (formalSalaryNum < 0) {
          errors.formalSalary = '正式工资不能为负数'
        }
      }

      // 发薪日验证
      if (formData.payDays && formData.payDays.length > 0) {
        const validation = this.workHistoryService.validatePayDays(formData.payDays)
        if (!validation.isValid) {
          errors.payDays = validation.errors.join(', ')
        }
      }

      this.setData({ errors })
      return Object.keys(errors).length === 0
    },

    /**
     * 保存工作履历
     */
    saveWorkHistory() {
      if (!this.validateForm()) {
        return
      }

      const { formData } = this.data
      const editingWorkId = this.properties.editingWorkId

      try {
        // 构建工作履历数据
        const workData = {
          company: formData.company.trim(),
          position: formData.position.trim(),
          startDate: new Date(formData.startDate),
          probationSalary: formData.probationSalary ? Number(formData.probationSalary) : 0,
          probationEndDate: formData.probationEndDate ? new Date(formData.probationEndDate) : null,
          formalSalary: formData.formalSalary ? Number(formData.formalSalary) : 0,
          endDate: formData.endDate ? new Date(formData.endDate) : null,
          notes: formData.notes.trim(),
          payDays: this.cleanPayDaysForSave(formData.payDays || [])
        }

        if (editingWorkId && editingWorkId.trim()) {
          // 更新现有工作履历
          this.workHistoryService.updateWorkHistory(editingWorkId, workData)
          wx.showToast({
            title: '更新成功',
            icon: 'success'
          })
        } else {
          // 添加新工作履历
          const workId = this.workHistoryService.addWorkHistory(workData)

          // 如果是第一个工作履历，自动设置为当前工作
          const workHistoryList = this.workHistoryService.getAllWorkHistory()
          if (workHistoryList.length === 1) {
            this.workHistoryService.setCurrentWork(workId)
          }

          wx.showToast({
            title: '添加成功',
            icon: 'success'
          })
        }

        // 触发刷新事件，通知父组件更新数据
        this.triggerEvent('refresh')

        // 隐藏模态框
        this.hideModal()
      } catch (error) {
        console.error('保存工作履历失败:', error)
        wx.showToast({
          title: '保存失败',
          icon: 'error'
        })
      }
    }
  }
})
