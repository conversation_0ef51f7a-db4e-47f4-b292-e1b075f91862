/* 工作履历模态框组件样式 */
@import "../../styles/modal-animations.wxss";

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(8rpx);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.modal-overlay.show {
  opacity: 1;
  visibility: visible;
}

.modal-content {
  background: white;
  border-radius: 32rpx;
  width: 100%;
  max-width: 640rpx;
  max-height: 85vh;
  display: flex;
  flex-direction: column;
  box-shadow: 0 32rpx 80rpx rgba(0, 0, 0, 0.2);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(20rpx);

  /* 使用统一的transition动画 */
  transform: scale(0.9) translateY(50rpx);
  opacity: 0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.modal-overlay.show .modal-content {
  transform: scale(1) translateY(0);
  opacity: 1;
}

/* 模态框头部样式更新 */
.modal-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  padding: 32rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.06);
  background: linear-gradient(135deg, rgba(6, 182, 212, 0.05) 0%, rgba(16, 185, 129, 0.05) 100%);
}

.modal-title-section {
  flex: 1;
  margin-right: 32rpx;
}

.modal-title {
  display: block;
  font-size: 40rpx;
  font-weight: 700;
  color: #1a1a1a;
  letter-spacing: 0.5rpx;
}

.modal-subtitle {
  display: block;
  font-size: 26rpx;
  color: #666;
  font-weight: 400;
  line-height: 1.4;
}

.modal-close {
  width: 56rpx;
  height: 56rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.modal-close::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(0, 0, 0, 0.08);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.modal-close:active::before {
  width: 100%;
  height: 100%;
}

.modal-close:active {
  transform: scale(0.9);
}

.close-icon {
  font-size: 32rpx;
  color: #666;
  font-weight: 300;
}

.modal-body {
  flex: 1;
  /* max-height: 60vh; */
  overflow: auto; /* 添加溢出处理，确保不会超出父元素的容器宽度 */
  box-sizing: border-box; /* 确保内边距不会影响宽度计算 */
}

/* 表单章节头部样式 */
.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
  padding-bottom: 16rpx;
  border-bottom: 2rpx solid rgba(6, 182, 212, 0.1);
}

.section-icon {
  font-size: 32rpx;
  margin-right: 16rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 700;
  color: #1a1a1a;
  letter-spacing: 0.5rpx;
}

.section-subtitle {
  display: block;
  font-size: 24rpx;
  color: #666;
  margin-bottom: 24rpx;
  padding: 16rpx 20rpx;
  background: rgba(6, 182, 212, 0.05);
  border-radius: 12rpx;
  border-left: 4rpx solid #06b6d4;
}

/* 模态框底部样式优化 */
.modal-footer {
  display: flex;
  gap: 24rpx;
  padding: 36rpx;
  border-top: 1rpx solid rgba(0, 0, 0, 0.06);
  background: linear-gradient(135deg, rgba(6, 182, 212, 0.03) 0%, rgba(16, 185, 129, 0.03) 100%);
}

.footer-btn {
  flex: 1;
  height: 88rpx;
  border-radius: 24rpx;
  border: none;
  font-size: 32rpx;
  font-weight: 600;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.footer-btn::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.footer-btn:active::before {
  width: 120%;
  height: 120%;
}

.footer-btn:active {
  transform: scale(0.96);
}

.cancel-btn {
  background: rgba(0, 0, 0, 0.04);
  color: #666;
}

.cancel-btn::before {
  background: rgba(0, 0, 0, 0.08);
}

.save-btn {
  background: linear-gradient(135deg, #06b6d4 0%, #10b981 100%);
  color: white;
  box-shadow: 0 8rpx 24rpx rgba(6, 182, 212, 0.3);
}

.save-btn::before {
  background: rgba(255, 255, 255, 0.2);
}

.btn-text {
  position: relative;
  z-index: 1;
  font-size: 32rpx;
  font-weight: 700;
  letter-spacing: 0.5rpx;
}

/* 表单样式 */
.form-section {
  margin: 32rpx;
}

.form-item {
  margin-bottom: 28rpx;
}

/* 日期行样式 */
.date-row {
  display: flex;
  gap: 20rpx;
}

.date-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

/* 工资行样式 */
.salary-row {
  display: flex;
  gap: 20rpx;
}

.salary-item {
  flex: 1;
}

/* 表单标签行样式 */
.form-label-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16rpx;
}

/* 表单标签样式更新 */
.form-label {
  display: block;
  font-size: 28rpx;
  color: #1a1a1a;
  font-weight: 600;
  margin-bottom: 12rpx;
}

.required {
  color: #ef4444;
  font-weight: 700;
}

.clear-date-btn {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 8rpx 16rpx;
  background: rgba(239, 68, 68, 0.1);
  border: 1rpx solid rgba(239, 68, 68, 0.2);
  border-radius: 12rpx;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.clear-date-btn:hover {
  background: rgba(239, 68, 68, 0.15);
  border-color: rgba(239, 68, 68, 0.3);
  transform: scale(1.05);
}

.clear-date-btn:active {
  transform: scale(0.95);
  background: rgba(239, 68, 68, 0.2);
}

.clear-icon {
  font-size: 20rpx;
  color: #ef4444;
  font-weight: 600;
}

.clear-text {
  font-size: 24rpx;
  color: #ef4444;
  font-weight: 500;
}

/* 输入框样式优化 */
.form-input {
  width: auto;
  padding: 24rpx;
  border: 2rpx solid rgba(0, 0, 0, 0.06);
  border-radius: 20rpx;
  font-size: 30rpx;
  color: #1a1a1a;
  background: rgba(6, 182, 212, 0.02);
  font-weight: 500;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
  font-weight: 500;
}

.form-input:focus {
  border-color: #06b6d4;
  background: rgba(6, 182, 212, 0.06);
  box-shadow: 0 4rpx 20rpx rgba(6, 182, 212, 0.15);
  transform: translateY(-2rpx);
}

.form-input.error {
  border-color: #ef4444;
  background: rgba(239, 68, 68, 0.05);
  box-shadow: 0 4rpx 20rpx rgba(239, 68, 68, 0.15);
}

/* 日期选择器样式优化 */
.form-picker {
  width: 100%;
  border: 2rpx solid rgba(0, 0, 0, 0.06);
  border-radius: 20rpx;
  background: rgba(6, 182, 212, 0.02);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
  position: relative;
  overflow: hidden;
}

.form-picker:active {
  border-color: #06b6d4;
  background: rgba(6, 182, 212, 0.08);
  box-shadow: 0 4rpx 20rpx rgba(6, 182, 212, 0.15);
  transform: translateY(-2rpx);
}

.form-picker.error {
  border-color: #ef4444;
  background: rgba(239, 68, 68, 0.05);
  box-shadow: 0 4rpx 20rpx rgba(239, 68, 68, 0.15);
}

.picker-display {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 24rpx;
  position: relative;
}

.picker-text {
  font-size: 30rpx;
  color: #1a1a1a;
  font-weight: 500;
  flex: 1;
  margin-right: 12rpx;
}

.picker-text.placeholder {
  color: #999;
  font-weight: 400;
}

.picker-arrow {
  font-size: 20rpx;
  color: #06b6d4;
  font-weight: 600;
  transform: rotate(0deg);
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.form-picker:active .picker-arrow {
  transform: rotate(180deg);
}

/* 文本域样式优化 */
.form-textarea {
  width: auto;
  padding: 24rpx;
  border: 2rpx solid rgba(0, 0, 0, 0.06);
  border-radius: 20rpx;
  font-size: 30rpx;
  color: #1a1a1a;
  background: rgba(102, 126, 234, 0.02);
  min-height: 120rpx;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
  font-weight: 500;
  resize: none;
}

.form-textarea:focus {
  border-color: #06b6d4;
  background: rgba(6, 182, 212, 0.06);
  box-shadow: 0 4rpx 20rpx rgba(6, 182, 212, 0.15);
  transform: translateY(-2rpx);
}

.textarea-counter {
  display: flex;
  justify-content: flex-end;
  margin-top: 12rpx;
}

.counter-text {
  font-size: 24rpx;
  color: #999;
  font-weight: 400;
}

.error-text {
  display: block;
  font-size: 24rpx;
  color: #ef4444;
  margin-top: 12rpx;
  font-weight: 500;
}

/* 发薪日设置样式 */
.payday-item {
  background: rgba(102, 126, 234, 0.05);
  border: 1rpx solid rgba(102, 126, 234, 0.1);
  border-radius: 16rpx;
  padding: 20rpx;
  margin-bottom: 16rpx;
}

.payday-header {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.payday-info {
  flex: 1;
  margin-right: 15rpx;
}

.payday-name-input {
  width: auto;
  padding: 12rpx 16rpx;
  background: rgba(255, 255, 255, 0.9);
  border: 1rpx solid rgba(102, 126, 234, 0.2);
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #1a1a1a;
}

.payday-actions {
  display: flex;
  align-items: center;
}

.remove-btn {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
  border-radius: 50%;
  font-size: 24rpx;
  font-weight: 600;
  cursor: pointer;
}

.payday-day-setting {
  display: flex;
  align-items: center;
  justify-content: center;
}

.payday-label {
  font-size: 28rpx;
  color: #666;
  margin: 0 10rpx;
}

.payday-day-picker {
  margin: 0 10rpx;
}

.payday-day-picker .picker-display {
  display: flex;
  align-items: center;
  padding: 12rpx 16rpx;
  background: rgba(6, 182, 212, 0.1);
  border-radius: 8rpx;
  min-width: 80rpx;
  justify-content: center;
}

.payday-day-picker .picker-text {
  font-size: 28rpx;
  color: #06b6d4;
  font-weight: 600;
  margin-right: 24rpx;
}

.payday-day-picker .picker-arrow {
  font-size: 20rpx;
  color: #06b6d4;
}

.add-payday-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  padding: 20rpx;
  background: rgba(6, 182, 212, 0.08);
  border: 2rpx dashed rgba(6, 182, 212, 0.3);
  border-radius: 16rpx;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  margin-top: 16rpx;
}

.add-payday-btn:active {
  background: rgba(6, 182, 212, 0.15);
  border-color: rgba(6, 182, 212, 0.5);
  transform: scale(0.98);
}

.add-icon {
  font-size: 32rpx;
  color: #06b6d4;
  font-weight: 600;
}

.add-text {
  font-size: 28rpx;
  color: #06b6d4;
  font-weight: 600;
}
