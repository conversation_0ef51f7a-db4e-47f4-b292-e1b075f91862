// 关于我们页面
const { AppConfig } = require('../../core/config/app.js')
const { getVersionInfo } = require('../../core/config/version.js')

Page({
  data: {
    // 应用信息
    appInfo: {
      name: '',
      description: ''
    },

    // 版本信息
    versionInfo: {
      version: '',
      date: '',
      description: ''
    },

    // 摸鱼社群模态框显示状态
    showCommunityModal: false
  },

  /**
   * 页面加载
   */
  onLoad() {
    console.log('关于我们页面加载')
    this.loadAppInfo()
  },

  /**
   * 加载应用信息
   */
  loadAppInfo() {
    try {
      const versionInfo = getVersionInfo()
      const appInfo = AppConfig.getAppInfo()
      
      this.setData({
        versionInfo: versionInfo,
        appInfo: appInfo
      })
      
      console.log('应用信息加载成功:', { versionInfo, appInfo })
    } catch (error) {
      console.error('获取应用信息失败:', error)
      wx.showToast({
        title: '加载失败',
        icon: 'error'
      })
    }
  },

  /**
   * 前往意见反馈页面
   */
  onGoToFeedback() {
    wx.navigateTo({
      url: '/pages/feedback/index'
    })
  },

  /**
   * 加入摸鱼社群
   */
  onJoinCommunity() {
    this.setData({
      showCommunityModal: true
    })
  },

  /**
   * 关闭摸鱼社群模态框
   */
  onCloseCommunityModal() {
    this.setData({
      showCommunityModal: false
    })
  },

  /**
   * 查看公告中心
   */
  onViewAnnouncements() {
    wx.navigateTo({
      url: '/pages/announcements/index'
    })
  },

  /**
   * 复制客服邮箱
   */
  onCopyEmail() {
    const email = '<EMAIL>'
    
    wx.setClipboardData({
      data: email,
      success: () => {
        wx.showToast({
          title: '邮箱已复制',
          icon: 'success'
        })
      },
      fail: () => {
        wx.showToast({
          title: '复制失败',
          icon: 'error'
        })
      }
    })
  },

  /**
   * 页面显示
   */
  onShow() {
    console.log('关于我们页面显示')
  },

  /**
   * 页面隐藏
   */
  onHide() {
    console.log('关于我们页面隐藏')
  },

  /**
   * 页面卸载
   */
  onUnload() {
    console.log('关于我们页面卸载')
  },

  /**
   * 页面分享
   */
  onShareAppMessage() {
    return {
      title: '时间跟踪器 - 跟踪你的每一分钟价值',
      path: '/pages/index/index',
      imageUrl: ''
    }
  }
})
