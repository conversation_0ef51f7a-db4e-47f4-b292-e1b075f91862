page {
  /* 禁用点击高亮 */
  -webkit-tap-highlight-color: rgba(0,0,0,0);
}

/* 关于我们页面样式 */
.about-container {
  min-height: 100vh;
  background-color: #F5F7FB;
  padding: 36rpx;
}

/* 通用区域样式 */
.section {
  margin-bottom: 32rpx;
}

/* 应用介绍卡片 */
.app-intro-card {
  background: white;
  border-radius: 32rpx;
  padding: 40rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
}

/* 应用头部信息 */
.app-header {
  display: flex;
  align-items: center;
  margin-bottom: 32rpx;
}

.app-logo {
  width: 120rpx;
  height: 120rpx;
  border-radius: 24rpx;
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 32rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
}

.logo-icon {
  font-size: 64rpx;
}

.app-basic-info {
  flex: 1;
}

.app-name {
  font-size: 48rpx;
  font-weight: 700;
  color: #2d3748;
  margin-bottom: 8rpx;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.app-description {
  font-size: 28rpx;
  color: #718096;
  font-weight: 500;
}

/* 版本信息 */
.version-info {
  background: #f8fafc;
  border-radius: 20rpx;
  padding: 24rpx;
  margin-bottom: 32rpx;
  border: 1rpx solid #e2e8f0;
}

.version-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12rpx 0;
}

.version-item:not(:last-child) {
  border-bottom: 1rpx solid #e2e8f0;
}

.version-label {
  font-size: 26rpx;
  color: #4a5568;
  font-weight: 500;
}

.version-value {
  font-size: 26rpx;
  color: #2d3748;
  font-weight: 600;
}

/* 应用简介 */
.app-intro {
  background: linear-gradient(135deg, rgba(168, 237, 234, 0.1) 0%, rgba(254, 214, 227, 0.1) 100%);
  border-radius: 20rpx;
  padding: 24rpx;
  border: 1rpx solid rgba(168, 237, 234, 0.2);
}

.intro-text {
  font-size: 28rpx;
  color: #4a5568;
  line-height: 1.6;
  font-weight: 400;
}

/* 联系方式卡片 */
.contact-card {
  background: white;
  border-radius: 32rpx;
  padding: 40rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.06);
}

.card-title {
  display: flex;
  align-items: center;
  margin-bottom: 32rpx;
}

.title-icon {
  font-size: 32rpx;
  margin-right: 16rpx;
}

.title-text {
  font-size: 36rpx;
  font-weight: 700;
  color: #2d3748;
}

/* 联系方式列表 */
.contact-list {
  display: flex;
  flex-direction: column;
}

.contact-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 0;
  cursor: pointer;
  transition: all 0.3s ease;
  border-bottom: 1rpx solid #f1f5f9;
}

.contact-item:last-child {
  border-bottom: none;
}

.contact-item:active {
  background: #f8fafc;
  transform: scale(0.98);
}

.contact-left {
  display: flex;
  align-items: center;
  flex: 1;
}

.contact-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 20rpx;
  background: #f8fafc;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
  font-size: 32rpx;
}

.contact-info {
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.contact-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #2d3748;
}

.contact-subtitle {
  font-size: 24rpx;
  color: #718096;
  font-weight: 400;
}

.contact-arrow {
  font-size: 32rpx;
  color: #cbd5e0;
  font-weight: 300;
}

/* 页面底部信息 */
.footer-info {
  text-align: center;
  padding: 40rpx 0;
  margin-top: 40rpx;
}

.footer-text {
  font-size: 26rpx;
  color: #718096;
  margin-bottom: 8rpx;
  font-weight: 400;
}

.copyright {
  font-size: 22rpx;
  color: #a0aec0;
  margin-top: 16rpx;
  font-weight: 400;
}
