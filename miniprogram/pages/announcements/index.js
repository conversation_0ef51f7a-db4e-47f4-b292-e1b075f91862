// 公告中心页面
import { api } from '../../core/api/index.js'
import { parseMarkdown } from '../../utils/markdown-parser.js'

Page({
  data: {
    // 公告列表
    announcements: [],
    // 加载状态
    loading: true,
    // 错误信息
    errorMessage: '',
    // 是否有更多数据
    hasMore: true,
    // 当前页码
    currentPage: 1,
    // 每页数量
    pageSize: 20,
    // 当前类型筛选
    currentType: 'all',
    // 是否显示类型筛选
    showTypeFilter: true
  },

  /**
   * 页面加载时
   */
  onLoad() {
    // 设置页面标题
    wx.setNavigationBarTitle({
      title: '公告中心'
    })
    
    // 加载公告数据
    this.loadAnnouncementData()
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh() {
    this.refreshData()
  },

  /**
   * 上拉加载更多
   */
  onReachBottom() {
    if (this.data.hasMore && !this.data.loading) {
      this.loadMoreData()
    }
  },

  /**
   * 加载公告数据
   */
  async loadAnnouncementData() {
    try {
      this.setData({
        loading: true,
        errorMessage: ''
      })

      console.log('[Announcements] 开始加载公告数据')

      // 使用新的公告API
      const result = await api.general.getAnnouncementList({
        type: this.data.currentType,
        page: this.data.currentPage,
        pageSize: this.data.pageSize,
      })

      if (result.success) {
        const { list, hasMore } = result.data

        // 处理Markdown内容
        const processedList = this.processMarkdownContent(list)

        this.setData({
          announcements: processedList,
          hasMore: hasMore || false,
          loading: false
        })

        console.log(`[Announcements] 公告加载成功: ${processedList.length}条记录`)
      } else {
        throw new Error(result.message || '获取公告失败')
      }
    } catch (error) {
      console.error('[Announcements] 加载公告失败:', error)
      this.setData({
        loading: false,
        errorMessage: error.message || '加载失败，请重试'
      })
    }
  },

  /**
   * 刷新数据
   */
  async refreshData() {
    try {
      this.setData({
        currentPage: 1,
        announcements: [],
        hasMore: true,
        errorMessage: ''
      })

      await this.loadAnnouncementData()
    } catch (error) {
      console.error('[Announcements] 刷新数据失败:', error)
    } finally {
      wx.stopPullDownRefresh()
    }
  },

  /**
   * 加载更多数据
   */
  async loadMoreData() {
    try {
      const nextPage = this.data.currentPage + 1

      this.setData({
        loading: true
      })

      console.log(`[Announcements] 加载更多数据: 第${nextPage}页`)

      const result = await api.general.getAnnouncementList({
        type: this.data.currentType,
        page: nextPage,
        pageSize: this.data.pageSize
      })

      if (result.success) {
        const { list, hasMore } = result.data

        // 处理Markdown内容
        const processedList = this.processMarkdownContent(list)

        this.setData({
          announcements: [...this.data.announcements, ...processedList],
          hasMore: hasMore || false,
          currentPage: nextPage,
          loading: false
        })

        console.log(`[Announcements] 加载更多成功: 第${nextPage}页，${list.length}条记录`)
      } else {
        throw new Error(result.message || '加载更多数据失败')
      }
    } catch (error) {
      console.error('[Announcements] 加载更多数据失败:', error)
      this.setData({
        loading: false
      })

      wx.showToast({
        title: error.message || '加载失败',
        icon: 'error'
      })
    }
  },

  /**
   * 切换类型筛选
   */
  async switchType(e) {
    const type = e.currentTarget.dataset.type
    
    if (type === this.data.currentType) {
      return
    }

    console.log(`[Announcements] 切换类型筛选: ${this.data.currentType} -> ${type}`)

    this.setData({
      currentType: type,
      currentPage: 1,
      announcements: [],
      hasMore: true,
      errorMessage: ''
    })

    await this.loadAnnouncementData()
  },

  /**
   * 重试加载
   */
  onRetry() {
    this.loadAnnouncementData()
  },

  /**
   * 处理链接点击
   */
  onLinkTap(e) {
    const url = e.currentTarget.dataset.url
    if (!url) return

    // 如果是小程序内部页面，使用navigateTo
    if (url.startsWith('/pages/')) {
      wx.navigateTo({
        url: url,
        fail: (err) => {
          console.error('页面跳转失败:', err)
          wx.showToast({
            title: '页面跳转失败',
            icon: 'error'
          })
        }
      })
    } else {
      // 外部链接，复制到剪贴板
      wx.setClipboardData({
        data: url,
        success: () => {
          wx.showToast({
            title: '链接已复制',
            icon: 'success'
          })
        },
        fail: () => {
          wx.showToast({
            title: '复制失败',
            icon: 'error'
          })
        }
      })
    }
  },

  /**
   * 页面显示时
   */
  onShow() {
    // 如果页面已经加载过数据，可以选择性刷新
    if (this.data.announcements.length === 0 && !this.data.loading) {
      this.loadAnnouncementData()
    }
  },

  /**
   * 页面隐藏时
   */
  onHide() {
    // 页面隐藏时的处理
  },

  /**
   * 页面卸载时
   */
  onUnload() {
    // 页面卸载时的清理工作
  },

  /**
   * 处理Markdown内容
   * @param {Array} list - 公告列表
   * @returns {Array} 处理后的列表
   */
  processMarkdownContent(list) {
    return list.map(item => {
      if (item.content && typeof item.content === 'string') {
        // 解析Markdown内容为渲染数据
        const parsedContent = parseMarkdown(item.content)

        return {
          ...item,
          parsedContent: parsedContent,
          // 保留原始内容用于调试
          originalContent: item.content
        }
      }
      return item
    })
  }
})
