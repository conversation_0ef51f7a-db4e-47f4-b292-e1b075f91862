# 公告系统测试指南

## 测试步骤

### 1. 初始化公告系统
在微信开发者工具的云函数控制台中，调用以下云函数来初始化公告系统：

```javascript
// 在云函数控制台执行
wx.cloud.callFunction({
  name: 'cloud-functions',
  data: {
    type: 'initAnnouncementSystem',
    data: {}
  }
}).then(res => {
  console.log('初始化结果:', res.result)
})
```

### 2. 创建测试公告
```javascript
// 创建一个测试公告
wx.cloud.callFunction({
  name: 'cloud-functions',
  data: {
    type: 'createAnnouncement',
    data: {
      title: '测试公告',
      content: '<h2>这是一个测试公告</h2><p>用于验证公告系统功能。</p>',
      type: 'announcement',
      isSticky: false,
      status: 'published'
    }
  }
}).then(res => {
  console.log('创建公告结果:', res.result)
})
```

### 3. 获取公告列表
```javascript
// 获取公告列表
wx.cloud.callFunction({
  name: 'cloud-functions',
  data: {
    type: 'getAnnouncementList',
    data: {
      type: 'all',
      page: 1,
      pageSize: 10
    }
  }
}).then(res => {
  console.log('公告列表:', res.result)
})
```

### 4. 测试前端页面
1. 在微信开发者工具中打开小程序
2. 导航到 `pages/announcements/index` 页面
3. 检查是否能正常显示公告列表
4. 测试类型筛选功能
5. 测试下拉刷新和上拉加载更多

### 5. 测试置顶功能
```javascript
// 设置公告置顶
wx.cloud.callFunction({
  name: 'cloud-functions',
  data: {
    type: 'toggleAnnouncementSticky',
    data: {
      id: '公告ID',
      isSticky: true
    }
  }
}).then(res => {
  console.log('置顶结果:', res.result)
})
```

## 预期结果

### 数据库
- `announcements` 集合应该被创建
- 应该有默认的测试公告数据
- 置顶公告应该排在列表前面

### 前端页面
- 页面标题显示为"公告中心"
- 公告列表正常显示
- 置顶公告有特殊标识
- 富文本内容正确渲染
- 类型筛选功能正常
- 分页加载功能正常

### API功能
- 所有CRUD操作正常
- 权限验证正确
- 错误处理完善
- 响应格式统一

## 常见问题

### 1. 权限错误
如果遇到权限错误，确保：
- 云函数已正确部署
- 管理员权限配置正确
- 用户身份验证正常

### 2. 数据不显示
检查：
- 云函数调用是否成功
- 数据库连接是否正常
- 前端API调用是否正确

### 3. 富文本不显示
确认：
- rich-text 组件使用正确
- HTML内容格式正确
- 样式文件加载正常

## 性能测试

### 1. 大量数据测试
创建100+条公告，测试：
- 列表加载性能
- 分页功能
- 搜索筛选性能

### 2. 并发测试
模拟多用户同时访问：
- API响应时间
- 数据库查询性能
- 缓存效果

### 3. 内存使用
监控：
- 页面内存占用
- 图片加载优化
- 长列表性能
