/* 公告中心页面样式 */

page {
  /* 禁用点击高亮 */
  -webkit-tap-highlight-color: rgba(0,0,0,0);
}

/* 容器 */
.announcements-container {
  min-height: 100vh;
  padding: 20rpx;
  background: #f5f7fa;
}

/* 页面头部 */
.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 40rpx 32rpx;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-radius: 24rpx;
  box-shadow: 0 12rpx 48rpx rgba(0, 0, 0, 0.08);
  margin-bottom: 32rpx;
}

.header-content {
  flex: 1;
}

.header-title {
  display: block;
  font-size: 48rpx;
  font-weight: 700;
  color: #1a1a1a;
  margin-bottom: 8rpx;
  letter-spacing: 0.5rpx;
}

.header-subtitle {
  display: block;
  font-size: 28rpx;
  color: #666;
  font-weight: 400;
}

.header-icon {
  font-size: 64rpx;
  margin-left: 20rpx;
}

/* 类型筛选 */
.type-filter {
  white-space: nowrap;
  margin-bottom: 24rpx;
  padding: 0 20rpx;
}

.filter-tab {
  display: inline-block;
  padding: 16rpx 24rpx;
  margin-right: 16rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 20rpx;
  font-size: 28rpx;
  color: #666;
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
}

.filter-tab.active {
  background: #667eea;
  color: white;
  border-color: #667eea;
  box-shadow: 0 4rpx 16rpx rgba(102, 126, 234, 0.3);
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 40rpx;
  text-align: center;
}

.loading-icon {
  font-size: 80rpx;
  margin-bottom: 24rpx;
  animation: spin 2s linear infinite;
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}

/* 错误状态 */
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 40rpx;
  text-align: center;
}

.error-icon {
  font-size: 80rpx;
  margin-bottom: 24rpx;
}

.error-text {
  font-size: 28rpx;
  color: #ef4444;
  margin-bottom: 32rpx;
  text-align: center;
}

.retry-button {
  background: #667eea;
  color: white;
  border: none;
  border-radius: 16rpx;
  padding: 16rpx 32rpx;
  font-size: 28rpx;
  font-weight: 600;
}

.retry-button:active {
  background: #5a6fd8;
}

/* 公告列表 */
.announcements-list {
  padding: 0 20rpx;
}

/* 公告卡片 */
.announcement-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-radius: 20rpx;
  border: 1rpx solid #e2e8f0;
  overflow: hidden;
  margin-bottom: 24rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.06);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

/* 置顶公告特殊样式 */
.announcement-card.sticky {
  border: 2rpx solid #ff6b6b;
  background: linear-gradient(135deg, #fff5f5 0%, #ffffff 100%);
  box-shadow: 0 12rpx 48rpx rgba(255, 107, 107, 0.15);
}

.sticky-badge {
  position: absolute;
  top: 16rpx;
  right: 16rpx;
  background: #ff6b6b;
  color: white;
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  font-size: 24rpx;
  font-weight: 600;
  z-index: 2;
}

/* 公告头部 */
.card-header {
  padding: 32rpx 32rpx 16rpx 32rpx;
  border-bottom: 1rpx solid #f1f5f9;
}

.title-section {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 16rpx;
}

.title {
  flex: 1;
  font-size: 32rpx;
  font-weight: 600;
  color: #1a1a1a;
  line-height: 1.4;
  margin-right: 16rpx;
}

.type-badge {
  flex-shrink: 0;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  font-size: 22rpx;
  font-weight: 500;
}

.type-badge.announcement {
  background: #e0f2fe;
  color: #0277bd;
}

.type-badge.update {
  background: #e8f5e8;
  color: #2e7d32;
}

.type-badge.notice {
  background: #fff3e0;
  color: #f57c00;
}

.type-badge.maintenance {
  background: #fce4ec;
  color: #c2185b;
}

.badge-text {
  font-size: 22rpx;
}

.publish-time {
  font-size: 24rpx;
  color: #94a3b8;
  font-weight: 400;
}

/* 公告内容 */
.card-content {
  padding: 16rpx 32rpx 32rpx 32rpx;
}

.rich-content {
  line-height: 1.6;
  font-size: 28rpx;
  color: #374151;
}

/* Markdown内容样式 */
.markdown-content {
  line-height: 1.6;
  font-size: 28rpx;
  color: #374151;
}

/* 标题样式 */
.md-header {
  font-weight: 600;
  margin: 24rpx 0 16rpx 0;
  color: #1f2937;
}

.md-header-1 {
  font-size: 36rpx;
  border-bottom: 2rpx solid #e5e7eb;
  padding-bottom: 12rpx;
}

.md-header-2 {
  font-size: 32rpx;
}

.md-header-3 {
  font-size: 30rpx;
}

.md-header-4 {
  font-size: 28rpx;
}

.md-header-5 {
  font-size: 26rpx;
}

.md-header-6 {
  font-size: 24rpx;
}

/* 段落样式 */
.md-paragraph {
  margin: 16rpx 0;
  line-height: 1.6;
}

/* 文本样式 */
.md-text {
  color: #374151;
}

.md-bold {
  font-weight: 600;
  color: #1f2937;
}

.md-italic {
  font-style: italic;
  color: #6b7280;
}

.md-inline-code {
  background: #f3f4f6;
  color: #dc2626;
  padding: 4rpx 8rpx;
  border-radius: 6rpx;
  font-family: 'Courier New', monospace;
  font-size: 24rpx;
}

.md-link {
  color: #667eea;
  text-decoration: underline;
}

/* 列表样式 */
.md-list {
  margin: 16rpx 0;
}

.md-list-item {
  display: flex;
  margin: 8rpx 0;
  align-items: flex-start;
}

.md-list-bullet {
  flex-shrink: 0;
  width: 32rpx;
  color: #6b7280;
  font-weight: 600;
}

.md-list-content {
  flex: 1;
  line-height: 1.5;
}

/* 引用样式 */
.md-quote {
  display: flex;
  margin: 16rpx 0;
  padding: 16rpx 0;
}

.md-quote-border {
  width: 6rpx;
  background: #d1d5db;
  border-radius: 3rpx;
  margin-right: 16rpx;
  flex-shrink: 0;
}

.md-quote-content {
  flex: 1;
  color: #6b7280;
  font-style: italic;
  line-height: 1.5;
}

/* 代码块样式 */
.md-code-block {
  margin: 16rpx 0;
  background: #f8fafc;
  border: 1rpx solid #e2e8f0;
  border-radius: 12rpx;
  overflow: hidden;
}

.md-code-language {
  background: #e2e8f0;
  color: #64748b;
  padding: 8rpx 16rpx;
  font-size: 24rpx;
  font-weight: 500;
}

.md-code-content {
  padding: 16rpx;
  font-family: 'Courier New', monospace;
  font-size: 24rpx;
  color: #1e293b;
  line-height: 1.4;
  white-space: pre-wrap;
  word-break: break-all;
}

/* 分割线样式 */
.md-divider {
  height: 2rpx;
  background: #e5e7eb;
  margin: 32rpx 0;
  border-radius: 1rpx;
}

/* 图片样式 */
.md-image-container {
  margin: 24rpx 0;
  text-align: center;
}

.md-image {
  max-width: 100%;
  border-radius: 12rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.md-inline-image {
  max-width: 200rpx;
  max-height: 200rpx;
  border-radius: 8rpx;
  margin: 0 8rpx;
  vertical-align: middle;
}

.md-image-caption {
  margin-top: 8rpx;
  font-size: 24rpx;
  color: #6b7280;
  font-style: italic;
}

/* 空状态 */
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 40rpx;
  text-align: center;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 32rpx;
  opacity: 0.6;
}

.empty-text {
  font-size: 32rpx;
  font-weight: 600;
  color: #64748b;
  margin-bottom: 12rpx;
}

.empty-desc {
  font-size: 26rpx;
  color: #94a3b8;
}

/* 加载更多状态 */
.load-more-container {
  padding: 40rpx 20rpx;
  text-align: center;
}

.loading-more {
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-more-icon {
  font-size: 32rpx;
  margin-right: 12rpx;
  animation: spin 2s linear infinite;
}

.loading-more-text {
  font-size: 26rpx;
  color: #94a3b8;
}

.no-more {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #94a3b8;
  font-size: 24rpx;
}

.no-more-line {
  flex: 1;
  height: 1rpx;
  background: #e2e8f0;
}

.no-more-text {
  margin: 0 24rpx;
  font-size: 24rpx;
  color: #94a3b8;
}

/* 动画 */
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .announcements-container {
    padding: 16rpx;
  }
  
  .page-header {
    padding: 32rpx 24rpx;
  }
  
  .header-title {
    font-size: 40rpx;
  }
  
  .announcement-card {
    margin-bottom: 20rpx;
  }
  
  .card-header {
    padding: 24rpx;
  }
  
  .title {
    font-size: 28rpx;
  }
}
