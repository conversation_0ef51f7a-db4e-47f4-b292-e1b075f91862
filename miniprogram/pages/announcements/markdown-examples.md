# Markdown公告示例

以下是一些可以在公告系统中使用的Markdown格式示例：

## 示例1：欢迎公告

```markdown
# 🎉 欢迎使用时间跟踪器！

感谢您选择我们的时间管理工具。

## 主要功能

- **时间追踪**：精确记录工作时间
- **收入统计**：自动计算收入
- **数据同步**：云端安全存储

### 快速开始

1. 在"履历"页面添加工作信息
2. 在主页记录时间段
3. 查看"统计"了解收入情况

> 💡 **提示**：建议每天定时记录，养成良好习惯。

如有问题，请访问 [帮助页面](/pages/help/index) 或联系客服。

---

祝您使用愉快！
```

## 示例2：功能更新公告

```markdown
# 📱 新功能上线

## 本次更新内容

### 新增功能
- 摸鱼状态记录
- 实时在线人数显示
- 数据导出功能

### 优化改进
- 提升页面加载速度
- 优化用户界面设计
- 修复已知问题

### 技术细节

使用了新的缓存策略：

```javascript
const cacheConfig = {
  ttl: 30 * 60 * 1000, // 30分钟
  maxSize: 100
}
```

## 使用说明

1. 点击主页的 **摸鱼** 按钮开始记录
2. 在状态页面查看当前摸鱼人数
3. 统计页面可查看摸鱼时长

*更多功能正在开发中，敬请期待！*
```

## 示例3：维护通知

```markdown
# 🔧 系统维护通知

## 维护时间
**2024年1月20日 02:00 - 04:00**

## 维护内容

### 数据库优化
- 索引重建
- 性能调优
- 数据清理

### 功能升级
- 新增备份机制
- 增强安全性
- 修复bug

## 注意事项

> ⚠️ **重要提醒**：维护期间无法使用以下功能：
> - 数据同步
> - 在线统计
> - 实时更新

### 建议操作

1. 提前保存重要数据
2. 避免在维护期间进行重要操作
3. 维护完成后重新登录

如有紧急问题，请联系技术支持。

---

感谢您的理解与配合！
```

## 支持的Markdown语法

### 标题
```markdown
# 一级标题
## 二级标题
### 三级标题
#### 四级标题
##### 五级标题
###### 六级标题
```

### 文本格式
```markdown
**粗体文本**
*斜体文本*
`行内代码`
[链接文本](链接地址)
```

### 列表
```markdown
无序列表：
- 项目1
- 项目2
- 项目3

有序列表：
1. 第一项
2. 第二项
3. 第三项
```

### 引用
```markdown
> 这是一个引用块
> 可以包含多行内容
```

### 代码块
```markdown
```javascript
function hello() {
  console.log('Hello World!')
}
```
```

### 分割线
```markdown
---
或者
***
```

## 最佳实践

1. **标题层次**：合理使用标题层次，便于阅读
2. **段落分隔**：使用空行分隔段落
3. **重点突出**：使用粗体和引用突出重要信息
4. **列表清晰**：使用列表组织信息
5. **代码规范**：代码块指定语言类型
6. **链接有效**：确保链接地址正确

## 注意事项

- 不支持HTML标签
- 图片需要使用外部链接
- 表格暂不支持
- 复杂嵌套结构可能显示异常
