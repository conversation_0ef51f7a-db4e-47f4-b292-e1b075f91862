<!-- 工作履历页面 -->
<view class="work-history-page">
  <!-- 页面头部 -->
  <view class="page-header">
    <view class="header-content">
      <text class="header-title">工作履历管理</text>
      <text class="header-subtitle">管理您的工作经历，设置当前工作</text>
    </view>
    <view class="header-actions">
      <button class="add-btn" bindtap="showAddModal">
        <text class="add-icon">+</text>
        <text>添加履历</text>
        <image wx:if="{{!canAddWork}}" class="crown-icon" src="/images/icons/crown.svg"></image>
      </button>
    </view>
  </view>

  <!-- 工作履历列表 -->
  <view class="work-list">
    <view wx:if="{{workHistoryList.length === 0}}" class="empty-state">
      <view class="empty-icon">💼</view>
      <text class="empty-text">暂无工作履历</text>
      <text class="empty-subtitle">点击上方按钮添加您的第一份工作履历</text>
    </view>
    
    <view wx:else>
      <view
        wx:for="{{workHistoryList}}"
        wx:key="id"
        class="modern-work-card {{item.isCurrent ? 'current-work' : ''}}"
      >
        <!-- 卡片头部 -->
        <view class="card-header" data-work-id="{{item.id}}" bindtap="toggleExpanded">
          <view class="header-left">
            <!-- 公司信息 -->
            <view class="company-section">
              <view class="company-name-row">
                <text class="company-name">{{item.company}}</text>
                <view wx:if="{{item.isCurrent}}" class="current-indicator">
                  <view class="current-dot"></view>
                  <text class="current-label">当前</text>
                </view>
              </view>
              <text class="position-title">{{item.position}}</text>
            </view>

            <!-- 快速信息 -->
            <view class="quick-info">
              <view class="info-chip status-chip status-{{item.status}}">
                <view class="status-indicator"></view>
                <text class="chip-text">{{item.status === 'active' ? '在职' : '离职'}}</text>
              </view>
              <view class="info-chip">
                <view class="chip-icon">📅</view>
                <text class="chip-text">{{item.timeRangeText}}</text>
              </view>
            </view>
          </view>

          <!-- 展开按钮 -->
          <view class="expand-button {{expandedItems[item.id] ? 'expanded' : ''}}">
            <text class="expand-icon-text">▶</text>
          </view>
        </view>

        <!-- 展开详情内容 -->
        <view class="details-panel {{expandedItems[item.id] ? 'show' : 'hide'}}">
          <view class="panel-content">

            <!-- 任职时间 -->
            <view class="detail-row">
              <view class="row-icon">📅</view>
              <text class="row-text">{{item.timeRangeText}}</text>
            </view>

            <!-- 转正时间 -->
            <view wx:if="{{item.probationEndDate}}" class="detail-row">
              <view class="row-icon">⭕</view>
              <text class="row-text">转正: {{item.probationEndDateText}}</text>
            </view>

            <!-- 薪资信息 -->
            <view wx:if="{{item.probationSalary > 0}}" class="detail-row">
              <view class="row-icon">💰</view>
              <text class="row-text">{{item.probationSalary}}k{{currencyUnit}}/月</text>
            </view>

            <view wx:if="{{item.formalSalary > 0 && item.formalSalary !== item.probationSalary}}" class="detail-row">
              <view class="row-icon">💰</view>
              <text class="row-text">{{item.formalSalary}}k{{currencyUnit}}/月 (转正后)</text>
            </view>

            <!-- 发薪日 -->
            <view wx:if="{{item.payDays && item.payDays.length > 0}}" class="detail-row">
              <view class="row-icon">📅</view>
              <text class="row-text">发薪日: {{item.payDaysText}}</text>
            </view>

            <!-- 备注 -->
            <view wx:if="{{item.notes}}" class="notes-section">
              <view class="notes-title">备注</view>
              <text class="notes-content">{{item.notes}}</text>
            </view>

            <!-- 操作按钮 -->
            <view class="action-buttons">
              <button
                wx:if="{{!item.isCurrent}}"
                class="action-btn primary-action"
                data-work-id="{{item.id}}"
                bindtap="setCurrentWork"
              >
                <text class="btn-icon">⭐</text>
                <text class="btn-text">设为当前</text>
              </button>

              <button
                class="action-btn secondary-action"
                data-work-id="{{item.id}}"
                bindtap="showEditWorkModal"
              >
                <text class="btn-icon">✏️</text>
                <text class="btn-text">编辑</text>
              </button>

              <button
                class="action-btn danger-action"
                data-work-id="{{item.id}}"
                bindtap="deleteWorkHistory"
              >
                <text class="btn-icon">🗑️</text>
                <text class="btn-text">删除</text>
              </button>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</view>

<!-- 工作履历模态框组件 -->
<work-history-modal
  visible="{{showModal}}"
  editingWorkId="{{editingWorkId}}"
  bind:close="onModalClose"
/>