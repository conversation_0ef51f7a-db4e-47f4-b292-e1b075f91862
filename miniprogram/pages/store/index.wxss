page {
  /* 禁用点击高亮 */
  -webkit-tap-highlight-color: rgba(0,0,0,0);
}

/* 积分商店页面样式 */
.store-page {
  padding: 32rpx;
  background: #f5f5f5;
  min-height: 100vh;
}

/* 积分头部 */
.points-header {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: white;
  box-shadow: 0 8rpx 32rpx rgba(40, 167, 69, 0.3);
}

.points-info {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.points-icon {
  font-size: 48rpx;
}

.points-content {
  display: flex;
  flex-direction: column;
}

.points-label {
  font-size: 24rpx;
  opacity: 0.8;
  margin-bottom: 4rpx;
}

.points-value {
  font-size: 36rpx;
  font-weight: 700;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
}

.points-action {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 20rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 20rpx;
  cursor: pointer;
  transition: all 0.3s ease;
}

.points-action:active {
  background: rgba(255, 255, 255, 0.3);
}

.action-text {
  font-size: 26rpx;
  font-weight: 600;
}

.action-arrow {
  font-size: 20rpx;
}

/* 标签页 */
.tab-bar {
  display: flex;
  background: white;
  border-radius: 24rpx;
  padding: 8rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 20rpx 0;
  border-radius: 16rpx;
  cursor: pointer;
  transition: all 0.3s ease;
}

.tab-item.active {
  background: linear-gradient(135deg, #06b6d4 0%, #10b981 100%);
  color: white;
}

.tab-text {
  font-size: 28rpx;
  font-weight: 600;
}

/* 商店内容 */
.store-content {
  margin-bottom: 32rpx;
}

.loading-state {
  text-align: center;
  padding: 80rpx 0;
}

.loading-icon {
  font-size: 64rpx;
  margin-bottom: 16rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}

.store-items {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.store-item {
  background: white;
  border-radius: 24rpx;
  padding: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.06);
  position: relative;
}

/* 新的商品项布局 */
.item-content {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.item-icon {
  font-size: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.item-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.item-name-row {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.item-name {
  font-size: 32rpx;
  font-weight: 700;
  color: #333;
}

.popular-badge {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
  color: white;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-size: 20rpx;
  font-weight: 700;
  box-shadow: 0 2rpx 8rpx rgba(255, 107, 107, 0.3);
}

.test-only-badge {
  background: linear-gradient(135deg, #ffc107 0%, #ff9800 100%);
  color: white;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-size: 20rpx;
  font-weight: 700;
  box-shadow: 0 2rpx 8rpx rgba(255, 193, 7, 0.3);
}

.item-price {
  display: flex;
  align-items: baseline;
  gap: 4rpx;
}

.price-number {
  font-size: 28rpx;
  font-weight: 700;
  color: #28a745;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
}

.price-unit {
  font-size: 22rpx;
  color: #666;
}

.purchase-btn {
  height: 64rpx;
  padding: 0 24rpx;
  border-radius: 32rpx;
  font-size: 26rpx;
  font-weight: 600;
  border: none;
  background: linear-gradient(135deg, #06b6d4 0%, #10b981 100%);
  color: white;
  box-shadow: 0 4rpx 16rpx rgba(6, 182, 212, 0.3);
  transition: all 0.3s ease;
  flex-shrink: 0;
  min-width: 140rpx;
}

.purchase-btn:active {
  transform: scale(0.95);
}

.purchase-btn.disabled {
  background: #e9ecef;
  color: #6c757d;
  box-shadow: none;
}

/* 兑换码内容 */
.codes-content {
  margin-bottom: 32rpx;
}

.empty-state {
  text-align: center;
  padding: 80rpx 0;
}

.empty-icon {
  font-size: 64rpx;
  margin-bottom: 16rpx;
  opacity: 0.5;
}

.empty-text {
  font-size: 32rpx;
  color: #333;
  font-weight: 600;
  margin-bottom: 8rpx;
}

.empty-desc {
  font-size: 26rpx;
  color: #999;
}

.codes-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.code-item {
  background: white;
  border-radius: 24rpx;
  padding: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.06);
}

.code-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.code-type {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.type-icon {
  font-size: 32rpx;
}

.type-text {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}

.code-status {
  padding: 6rpx 16rpx;
  border-radius: 12rpx;
  font-size: 22rpx;
  font-weight: 600;
}

.status-active {
  background: rgba(40, 167, 69, 0.1);
  color: #28a745;
}

.status-used {
  background: rgba(108, 117, 125, 0.1);
  color: #6c757d;
}

.status-expired {
  background: rgba(220, 53, 69, 0.1);
  color: #dc3545;
}

.code-body {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.code-value {
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  background: #f8f9fa;
  padding: 12rpx 16rpx;
  border-radius: 12rpx;
  letter-spacing: 2rpx;
}

.code-actions {
  display: flex;
  gap: 12rpx;
}

.action-btn {
  height: 60rpx;
  padding: 0 24rpx;
  border-radius: 30rpx;
  font-size: 24rpx;
  font-weight: 600;
  border: none;
}

.copy-btn {
  background: #f8f9fa;
  color: #6c757d;
  border: 2rpx solid #dee2e6;
}

.use-btn {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  color: white;
}

.code-footer {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.code-info {
  display: flex;
  align-items: center;
}

.info-label {
  font-size: 24rpx;
  color: #999;
  margin-right: 8rpx;
}

.info-value {
  font-size: 24rpx;
  color: #666;
}

/* 使用说明 */
.usage-tips {
  background: white;
  border-radius: 24rpx;
  padding: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.06);
}

.tips-title {
  font-size: 28rpx;
  font-weight: 700;
  color: #333;
  margin-bottom: 20rpx;
}

.tips-content {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.tip-item {
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
}

/* 测试用户标识 */
.test-user-badge {
  display: flex;
  align-items: center;
  gap: 12rpx;
  background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
  color: white;
  padding: 16rpx 24rpx;
  border-radius: 20rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(23, 162, 184, 0.3);
}

.badge-icon {
  font-size: 28rpx;
}

.badge-text {
  font-size: 24rpx;
  font-weight: 600;
}

/* 测试商品标识 */
.test-only-badge {
  position: absolute;
  top: -16rpx;
  left: -16rpx;
  background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
  color: white;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  font-size: 18rpx;
  font-weight: 700;
  box-shadow: 0 2rpx 8rpx rgba(23, 162, 184, 0.3);
}
