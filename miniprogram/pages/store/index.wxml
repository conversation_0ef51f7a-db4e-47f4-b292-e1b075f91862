<wxs module="dateUtils" src="/utils/date.wxs"></wxs>

<view class="store-page">
  <!-- 头部积分显示 -->
  <view class="points-header">
    <view class="points-info">
      <view class="points-icon">💰</view>
      <view class="points-content">
        <view class="points-label">当前积分</view>
        <view class="points-value">{{currentPoints}}</view>
      </view>
    </view>
    <view class="points-action" bindtap="onViewPointsRecords">
      <text class="action-text">积分记录</text>
      <text class="action-arrow">›</text>
    </view>
  </view>

  <!-- 测试用户标识 -->
  <view wx:if="{{isTestUser}}" class="test-user-badge">
    <view class="badge-icon">🧪</view>
    <view class="badge-text">测试用户 - 可查看未发布商品</view>
  </view>

  <!-- 标签页切换 -->
  <view class="tab-bar">
    <view class="tab-item {{activeTab === 'store' ? 'active' : ''}}" 
          bindtap="onTabChange" data-tab="store">
      <text class="tab-text">积分商店</text>
    </view>
    <view class="tab-item {{activeTab === 'codes' ? 'active' : ''}}" 
          bindtap="onTabChange" data-tab="codes">
      <text class="tab-text">兑换记录</text>
    </view>
  </view>

  <!-- 积分商店内容 -->
  <view wx:if="{{activeTab === 'store'}}" class="store-content">
    <view wx:if="{{loading.items}}" class="loading-state">
      <view class="loading-icon">⏳</view>
      <view class="loading-text">加载中...</view>
    </view>
    
    <view wx:else class="store-items">
      <view wx:for="{{storeItems}}" wx:key="id" class="store-item">
        <view class="item-content">
          <view class="item-icon">{{item.icon}}</view>
          <view class="item-info">
            <view class="item-name-row">
              <text class="item-name">{{item.name}}</text>
              <view wx:if="{{item.popular}}" class="popular-badge">热门</view>
              <view wx:if="{{!item.published && isTestUser}}" class="test-only-badge">测试</view>
            </view>
            <view class="item-price">
              <text class="price-number">{{item.pointsCost}}</text>
              <text class="price-unit">积分</text>
            </view>
          </view>
          <button
            class="purchase-btn {{item.affordable ? '' : 'disabled'}}"
            bindtap="onPurchaseItem"
            data-item-id="{{item.id}}"
            disabled="{{!item.affordable || loading.purchase}}"
            loading="{{loading.purchase}}"
          >
            {{item.affordable ? '立即兑换' : '积分不足'}}
          </button>
        </view>
      </view>
    </view>
  </view>

  <!-- 兑换记录内容 -->
  <view wx:if="{{activeTab === 'codes'}}" class="codes-content">
    <view wx:if="{{myCodes.length === 0}}" class="empty-state">
      <view class="empty-icon">🎫</view>
      <view class="empty-text">暂无兑换码</view>
      <view class="empty-desc">购买商品后兑换码将显示在这里</view>
    </view>
    
    <view wx:else class="codes-list">
      <view wx:for="{{myCodes}}" wx:key="_id" class="code-item">
        <view class="code-header">
          <view class="code-type">
            <text class="type-icon">{{item.type === 'vip_days' ? '👑' : '🎁'}}</text>
            <text class="type-text">{{item.value}}天VIP会员兑换码</text>
          </view>
          <view class="code-status {{getStatusClass(item.status)}}">
            {{getStatusText(item.status)}}
          </view>
        </view>
        
        <view class="code-body">
          <view class="code-value">{{item.code}}</view>
          <view class="code-actions">
            <button
              wx:if="{{item.status === 'active'}}"
              class="action-btn copy-btn"
              bindtap="onCopyCode"
              data-code="{{item.code}}"
            >
              复制
            </button>
            <button
              wx:if="{{item.status === 'active'}}"
              class="action-btn use-btn"
              bindtap="onUseCode"
              data-code="{{item.code}}"
            >
              使用
            </button>
          </view>
        </view>
        
        <view class="code-footer">
          <view class="code-info">
            <text class="info-label">兑换时间：</text>
            <text class="info-value">{{item.createdAtText}}</text>
          </view>
          <view wx:if="{{item.status === 'used'}}" class="code-info">
            <text class="info-label">使用时间：</text>
            <text class="info-value">{{item.usedAtText}}</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 使用说明 -->
  <view class="usage-tips">
    <view class="tips-title">💡 使用说明</view>
    <view class="tips-content">
      <view class="tip-item">• 兑换码永久有效，可随时使用</view>
      <view class="tip-item">• 每个兑换码只能使用一次</view>
      <view class="tip-item">• VIP时长可累加，不会覆盖现有时长</view>
      <view class="tip-item">• 点击"使用"按钮可快速兑换</view>
      <view class="tip-item">• 兑换码可复制分享给他人使用</view>
    </view>
  </view>

  <!-- 兑换码使用模态框 -->
  <redeem-code-modal
    show="{{showRedeemModal}}"
    default-code="{{defaultRedeemCode}}"
    bind:success="onRedeemSuccess"
    bind:close="onCloseRedeemModal"
  />
</view>
