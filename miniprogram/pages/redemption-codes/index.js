// 兑换码页面
import { api } from '../../core/api/index.js'
import { formatDateTime } from '../../utils/time-utils.js'
import { formatRefreshTime } from '../../utils/time-utils.js'

Page({
  data: {
    // 统计数据
    stats: {
      totalCodes: 0,
      usedCodes: 0,
      activeCodes: 0
    },

    // 兑换码列表
    codes: [],
    filteredCodes: [],
    
    // 筛选状态
    currentFilter: 'all',
    
    // 分页参数
    page: 1,
    limit: 20,
    hasMore: true,
    loading: true
  },

  /**
   * 页面加载时
   */
  onLoad() {
    console.log('兑换码页面加载')
    this.loadRedemptionCodes()
  },

  /**
   * 页面显示时
   */
  onShow() {
    console.log('兑换码页面显示')
    // 刷新数据
    this.loadRedemptionCodes()
  },

  /**
   * 加载兑换码数据
   */
  async loadRedemptionCodes() {
    try {
      this.setData({ loading: true })

      console.log('[RedemptionCodes] 开始加载兑换码数据')

      const result = await api.user.getUserRedemptionCodes({
        page: this.data.page,
        limit: this.data.limit
      })

      if (result.success) {
        const { codes, stats, hasMore } = result.data

        // 格式化时间显示和来源信息
        const formattedCodes = codes.map(code => ({
          ...code,
          createTimeText: formatDateTime(code.createdAt),
          usedAtText: code.usedAt ? formatDateTime(code.usedAt) : '',
          usedByText: code.usedByNickname || '未知用户',
          sourceText: this.getSourceText(code),
          valueText: this.getValueText(code)
        }))

        this.setData({
          codes: this.data.page === 1 ? formattedCodes : [...this.data.codes, ...formattedCodes],
          stats,
          hasMore,
          loading: false
        })

        console.log(`[RedemptionCodes] 兑换码加载成功: ${codes.length}条记录`)

        // 应用当前筛选
        this.applyFilter()
      } else {
        wx.showToast({
          title: result.message || '加载失败',
          icon: 'none'
        })
        this.setData({ loading: false })
      }
    } catch (error) {
      console.error('[RedemptionCodes] 加载兑换码失败:', error)
      wx.showToast({
        title: error.message || '网络异常，请重试',
        icon: 'none'
      })
      this.setData({ loading: false })
    }
  },

  /**
   * 筛选切换
   */
  onFilterChange(e) {
    const filter = e.currentTarget.dataset.filter
    this.setData({ currentFilter: filter })
    this.applyFilter()
  },

  /**
   * 应用筛选
   */
  applyFilter() {
    const { codes, currentFilter } = this.data
    let filteredCodes = codes

    if (currentFilter !== 'all') {
      filteredCodes = codes.filter(code => code.status === currentFilter)
    }

    this.setData({ filteredCodes })
  },

  /**
   * 复制兑换码
   */
  onCopyCode(e) {
    const code = e.currentTarget.dataset.code
    wx.setClipboardData({
      data: code,
      success: () => {
        wx.showToast({
          title: '兑换码已复制',
          icon: 'success'
        })
      }
    })
  },

  /**
   * 使用兑换码
   */
  async onUseCode(e) {
    const code = e.currentTarget.dataset.code
    
    wx.showModal({
      title: '确认使用',
      content: `确定要使用兑换码 ${code} 吗？`,
      success: async (res) => {
        if (res.confirm) {
          try {
            wx.showLoading({ title: '使用中...' })

            console.log('[RedemptionCodes] 开始使用兑换码:', code)

            const result = await api.user.useRedemptionCode({ code })

            wx.hideLoading()

            if (result.success) {
              console.log('[RedemptionCodes] 兑换码使用成功')
              wx.showToast({
                title: '使用成功',
                icon: 'success'
              })
              // 刷新数据
              this.setData({ page: 1 })
              this.loadRedemptionCodes()
            } else {
              wx.showToast({
                title: result.message || '使用失败',
                icon: 'none'
              })
            }
          } catch (error) {
            wx.hideLoading()
            console.error('[RedemptionCodes] 使用兑换码失败:', error)
            wx.showToast({
              title: error.message || '网络异常，请重试',
              icon: 'none'
            })
          }
        }
      }
    })
  },

  /**
   * 加载更多
   */
  onLoadMore() {
    if (!this.data.hasMore || this.data.loading) {
      return
    }

    this.setData({ 
      page: this.data.page + 1 
    })
    this.loadRedemptionCodes()
  },

  /**
   * 格式化日期时间
   */
  formatDateTime(dateStr) {
    if (!dateStr) return ''
    
    const date = new Date(dateStr)
    const now = new Date()
    const diff = now.getTime() - date.getTime()
    const days = Math.floor(diff / (1000 * 60 * 60 * 24))

    if (days === 0) {
      return '今天 ' + date.toTimeString().slice(0, 5)
    } else if (days === 1) {
      return '昨天 ' + date.toTimeString().slice(0, 5)
    } else if (days < 7) {
      return `${days}天前`
    } else {
      return `${date.getMonth() + 1}月${date.getDate()}日`
    }
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh() {
    this.setData({ page: 1 })
    this.loadRedemptionCodes().then(() => {
      wx.stopPullDownRefresh()
    })
  },

  /**
   * 获取兑换码来源文本
   */
  getSourceText(code) {
    if (code.pointsCost && code.pointsCost > 0) {
      return `积分商店兑换（${code.pointsCost}积分）`
    }
    return '其他来源'
  },

  /**
   * 获取兑换码价值文本
   */
  getValueText(code) {
    if (code.type === 'vip_days' && code.value) {
      return `${code.value}天VIP`
    }
    return code.value || '未知'
  }
})
