<!-- 数据管理页面 -->
<view class="data-management-container">
  <!-- 页面头部 -->
  <view class="page-header">
    <view class="page-title">数据管理</view>
    <view class="page-subtitle">管理您的本地和云端数据</view>
  </view>

  <!-- 使用提示 -->
  <view class="usage-tip">
    <view class="tip-icon">💡</view>
    <view class="tip-content">
      <view class="tip-title">遇到数据异常？</view>
      <view class="tip-text">可以先尝试清理缓存并重启小程序，如果问题仍然存在，请<text class="tip-link" bindtap="onGoToFeedback">向我们反馈</text>。</view>
    </view>
  </view>

  <!-- 数据概览卡片 -->
  <view class="section">
    <view class="section-title">数据概览</view>
    <view class="data-overview-card">
      <view class="data-comparison">
        <view class="comparison-header">
          <view class="data-label">数据类型</view>
          <view class="local-header">本地数据</view>
          <view class="cloud-header">云端数据</view>
        </view>

        <view class="comparison-row">
          <view class="data-label">履历</view>
          <view class="local-value">{{localStats.workHistoryCount}}</view>
          <view class="cloud-value">{{cloudStats.workHistoryCount !== undefined ? cloudStats.workHistoryCount : '-'}}</view>
        </view>

        <view class="comparison-row">
          <view class="data-label">时间段</view>
          <view class="local-value">{{localStats.timeSegmentCount}}</view>
          <view class="cloud-value">{{cloudStats.timeSegmentCount !== undefined ? cloudStats.timeSegmentCount : '-'}}</view>
        </view>

        <view class="comparison-row">
          <view class="data-label">摸鱼记录</view>
          <view class="local-value">{{localStats.fishingRecordCount}}</view>
          <view class="cloud-value">{{cloudStats.fishingRecordCount !== undefined ? cloudStats.fishingRecordCount : '-'}}</view>
        </view>

        <view class="comparison-row">
          <view class="data-label">收入扣款</view>
          <view class="local-value">{{localStats.incomeAdjustmentCount}}</view>
          <view class="cloud-value">{{cloudStats.incomeAdjustmentCount !== undefined ? cloudStats.incomeAdjustmentCount : '-'}}</view>
        </view>
      </view>
      
      <view class="last-modified-info">
        <view class="cloud-time">
          <text class="time-label">云端数据时间：</text>
          <text class="time-value">{{cloudStats.lastModifiedText || '未知'}}</text>
        </view>
        <view class="local-time">
          <text class="time-label">本地数据时间：</text>
          <text class="time-value">{{localStats.lastModifiedText}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 数据操作卡片 -->
  <view class="section">
    <view class="section-title">数据操作</view>
    <view class="data-operations-card">
      <button
        class="operation-button upload-button {{canUpload ? 'enabled' : 'disabled'}}"
        bindtap="onUploadData"
        disabled="{{!canUpload}}"
      >
        <view class="button-icon">☁️</view>
        <view class="button-text">
          <view class="button-title">上传到云端</view>
          <view class="button-desc">{{uploadButtonText}}</view>
        </view>
      </button>

      <button class="operation-button history-button" bindtap="onLoadHistoryData">
        <view class="button-icon">📅</view>
        <view class="button-text">
          <view class="button-title">加载云端历史数据</view>
          <view class="button-desc">恢复过去7天的数据备份</view>
        </view>
        <image wx:if="{{!canLoadHistory}}" class="crown-icon" src="/images/icons/crown.svg"></image>
      </button>
    </view>
  </view>

  <!-- 维护操作卡片 -->
  <view class="section">
    <view class="section-title">维护操作</view>
    <view class="maintenance-operations-card">
      <button class="maintenance-button" bindtap="onClearCache">
        <view class="button-icon">🧹</view>
        <view class="button-text">
          <view class="button-title">清空缓存</view>
          <view class="button-desc">清除临时数据，保留用户数据</view>
        </view>
      </button>
      
      <button class="maintenance-button danger-dark" bindtap="onClearAllData">
        <view class="button-icon">⚠️</view>
        <view class="button-text">
          <view class="button-title">清空所有数据</view>
          <view class="button-desc">删除本地和云端所有数据（危险操作）</view>
        </view>
      </button>
    </view>
  </view>

  <!-- 加载状态 -->
  <view class="loading-overlay {{isLoading ? 'show' : ''}}" wx:if="{{isLoading}}">
    <view class="loading-content">
      <view class="loading-spinner"></view>
      <view class="loading-text">{{loadingText}}</view>
    </view>
  </view>
</view>

<!-- 历史数据选择模态框 -->
<view class="modal-overlay {{showHistoryModal ? 'show' : ''}}" bindtap="onCloseHistoryModal" wx:if="{{showHistoryModal}}">
  <view class="modal-content" catchtap="onModalContentTap">
    <view class="modal-header">
      <text class="modal-title">选择历史数据</text>
      <view class="modal-close" bindtap="onCloseHistoryModal">×</view>
    </view>
    <view class="modal-body">
      <view class="history-list">
        <view 
          class="history-item {{item.selected ? 'selected' : ''}}" 
          wx:for="{{historyDataList}}" 
          wx:key="timestamp"
          bindtap="onSelectHistoryData"
          data-index="{{index}}"
        >
          <view class="history-date">{{item.dateText}}</view>
          <view class="history-time">{{item.timeText}}</view>
          <view class="history-stats">{{item.statsText}}</view>
        </view>
      </view>
    </view>
    <view class="modal-footer">
      <button class="modal-button cancel" bindtap="onCloseHistoryModal">取消</button>
      <button class="modal-button confirm" bindtap="onConfirmLoadHistory" disabled="{{!selectedHistoryIndex}}">确认加载</button>
    </view>
  </view>
</view>

<!-- 清空所有数据确认模态框 -->
<view class="modal-overlay {{showClearAllModal ? 'show' : ''}}" bindtap="onCloseClearAllModal" wx:if="{{showClearAllModal}}">
  <view class="modal-content danger-modal" catchtap="onModalContentTap">
    <view class="modal-header">
      <text class="modal-title">⚠️ 危险操作确认</text>
      <view class="modal-close" bindtap="onCloseClearAllModal">×</view>
    </view>
    <view class="modal-body">
      <view class="warning-text">
        此操作将永久清空您的云端和本地的所有数据，在清空完成后将重启小程序，数据将无法恢复。
      </view>
      <view class="confirm-input-section">
        <view class="confirm-label">请输入以下文字确认操作：</view>
        <view class="confirm-target-text">确认清空云端和本地数据</view>
        <input 
          class="confirm-input" 
          placeholder="请输入确认文字"
          value="{{confirmText}}"
          bindinput="onConfirmTextInput"
        />
      </view>
    </view>
    <view class="modal-footer">
      <button class="modal-button cancel" bindtap="onCloseClearAllModal">取消</button>
      <button 
        class="modal-button danger" 
        bindtap="onConfirmClearAll" 
        disabled="{{!canConfirmClearAll}}"
      >
        确认删除
      </button>
    </view>
  </view>
</view>
