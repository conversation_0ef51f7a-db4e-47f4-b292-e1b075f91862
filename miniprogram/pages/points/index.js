// 积分记录页面
import { api } from '../../core/api/index.js'

Page({
  data: {
    // 积分统计
    stats: {
      totalEarned: 0,
      totalSpent: 0,
      currentBalance: 0,
      totalRecords: 0
    },

    // 积分记录
    records: [],

    // 筛选条件
    filter: {
      type: '', // '' | 'earn' | 'spend'
    },

    // 加载状态
    loading: {
      records: false
    },

    // 任务数据
    tasks: [],
    checkInStatus: null
  },

  /**
   * 页面加载时
   */
  onLoad(options) {
    console.log('[Points] 积分记录页面加载')
    this.loadAllData()
  },

  /**
   * 页面显示时
   */
  onShow() {
    console.log('[Points] 积分记录页面显示')

    // 刷新缓存并加载数据
    this.refreshData()
  },

  /**
   * 加载所有数据
   */
  async loadAllData() {
    console.log('[Points] 开始加载所有数据')

    // 并行加载数据以提升性能
    await Promise.all([
      this.loadPointsRecords(),
      this.loadTasks()
    ])

    console.log('[Points] 所有数据加载完成')
  },

  /**
   * 刷新数据（清除缓存）
   */
  async refreshData() {
    try {
      console.log('[Points] 刷新数据，清除缓存')

      // 清除积分相关的所有缓存
      if (api.points.clearPointsCache) {
        api.points.clearPointsCache('all')
      }

      // 重置记录
      this.setData({
        records: []
      })

      // 重新加载所有数据
      await this.loadAllData()

      console.log('[Points] 数据刷新完成')
    } catch (error) {
      console.error('[Points] 刷新失败:', error)
      wx.showToast({
        title: '刷新失败，请重试',
        icon: 'none',
        duration: 2000
      })
    }
  },

  /**
   * 加载积分记录（使用新API系统）
   */
  async loadPointsRecords() {
    try {
      this.setData({
        'loading.records': true
      })

      console.log('[Points] 开始加载积分记录')

      // 使用新的API系统获取所有记录
      const result = await api.points.getPointsRecords(
        { type: this.data.filter.type || undefined },
        {
          showLoading: false,  // 使用页面自定义loading
          showError: false     // 使用页面自定义错误处理
        }
      )

      if (result.success) {
        const data = result.data
        this.setData({
          records: data.records,
          stats: data.stats || this.data.stats
        })

        console.log(`[Points] 积分记录加载成功: ${data.records.length}条记录`)
      } else {
        throw new Error(result.message || '加载失败')
      }
    } catch (error) {
      console.error('[Points] 加载积分记录失败:', error)
      wx.showToast({
        title: error.message || '网络异常，请重试',
        icon: 'none'
      })
    } finally {
      this.setData({
        'loading.records': false
      })
    }
  },

  /**
   * 筛选类型变化
   */
  onFilterChange(event) {
    const { type } = event.currentTarget.dataset
    const newType = type === this.data.filter.type ? '' : type

    console.log(`[Points] 筛选类型变化: ${this.data.filter.type} -> ${newType}`)

    this.setData({
      'filter.type': newType,
      records: []
    })

    // 重新加载记录
    this.loadPointsRecords()
  },

  /**
   * 跳转到积分商店
   */
  onGoToStore() {
    wx.navigateTo({
      url: '/pages/store/index'
    })
  },

  /**
   * 加载任务数据
   */
  async loadTasks() {
    try {
      // 获取签到状态
      await this.loadCheckInStatus()

      // 生成任务列表
      this.generateTasks()
    } catch (error) {
      console.error('加载任务失败:', error)
    }
  },

  /**
   * 获取签到状态（使用新API系统）
   */
  async loadCheckInStatus() {
    try {
      console.log('[Points] 开始获取签到状态')

      // 使用新的API系统，带缓存
      const result = await api.checkIn.getCheckInStatus({
        showLoading: false,  // 使用页面自定义loading
        showError: false     // 使用页面自定义错误处理
      })

      if (result.success) {
        this.setData({
          checkInStatus: result.data
        })
        console.log('[Points] 签到状态获取成功')
      } else {
        throw new Error(result.message || '获取签到状态失败')
      }
    } catch (error) {
      console.error('[Points] 获取签到状态失败:', error)
      // 任务模块的签到状态获取失败不影响主要功能，只记录错误
    }
  },

  /**
   * 生成任务列表
   */
  generateTasks() {
    const checkInStatus = this.data.checkInStatus
    const tasks = [
      {
        id: 'daily_checkin',
        name: '每日签到',
        description: '每日签到获得积分，连续签到奖励更多',
        icon: '📅',
        iconColor: 'linear-gradient(135deg, #06b6d4 0%, #10b981 100%)',
        reward: checkInStatus ? checkInStatus.nextReward : 1,
        status: checkInStatus && checkInStatus.hasCheckedInToday ? 'completed' : 'available',
        actionText: checkInStatus && checkInStatus.hasCheckedInToday ? '已签到' : '去签到',
        progress: checkInStatus ? `连续签到${checkInStatus.consecutiveDays}天` : null
      },
      {
        id: 'invite_friends',
        name: '邀请好友',
        description: '邀请好友注册使用，双方都可获得积分',
        icon: '👥',
        iconColor: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
        reward: 10,
        status: 'available',
        actionText: '邀请好友',
        progress: '今日已邀请0人'
      },
      {
        id: 'watch_ads',
        name: '观看广告',
        description: '观看完整广告视频获得积分奖励',
        icon: '📺',
        iconColor: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
        reward: 2,
        status: 'available',
        actionText: '观看广告',
        progress: '今日已观看0/5次'
      }
    ]

    this.setData({
      tasks
    })
  },

  /**
   * 任务操作处理
   */
  async onTaskAction(event) {
    const taskId = event.currentTarget.dataset.task
    console.log('执行任务:', taskId)

    switch (taskId) {
      case 'daily_checkin':
        this.handleCheckInTask()
        break
      case 'invite_friends':
        this.handleInviteTask()
        break
      case 'watch_ads':
        this.handleWatchAdsTask()
        break
      default:
        wx.showToast({
          title: '功能开发中',
          icon: 'none'
        })
    }
  },

  /**
   * 处理签到任务
   */
  handleCheckInTask() {
    wx.navigateTo({
      url: '/pages/check-in/index'
    })
  },

  /**
   * 处理邀请好友任务
   */
  handleInviteTask() {
    console.log('[INFO] 跳转到邀请好友页面')
    wx.navigateTo({
      url: '/pages/invite/index'
    })
  },

  /**
   * 处理观看广告任务
   */
  handleWatchAdsTask() {
    wx.showModal({
      title: '观看广告',
      content: '广告功能正在开发中，敬请期待！',
      showCancel: false,
      confirmText: '知道了'
    })
  },

  /**
   * 页面分享
   */
  onShareAppMessage() {
    return {
      title: '积分记录 - 查看我的积分收支明细',
      path: '/pages/points/index',
      imageUrl: ''
    }
  },

  /**
   * 分享到朋友圈
   */
  onShareTimeline() {
    return {
      title: '积分记录 - 查看我的积分收支明细',
      query: '',
      imageUrl: ''
    }
  },

  /**
   * 页面卸载时清理资源
   */
  onUnload() {
    console.log('[Points] 页面卸载')
    // 可以在这里做一些清理工作
  }
})
