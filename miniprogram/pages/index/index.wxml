<!-- 主入口页面 - 使用仪表盘组件 -->
<view class="main-container">
  <!-- 加载动画 -->
  <view wx:if="{{isLoading}}" class="loading-overlay" style="padding-top: {{navbarHeight}}px;">
    <!-- 加载页面导航栏 -->
    <view class="index-navbar loading-theme">
      <view class="status-bar" style="height: {{statusBarHeight}}px;"></view>
      <view class="navbar-content">
        <text class="navbar-title">时间跟踪器</text>
      </view>
    </view>

    <view class="loading-content">
      <view class="loading-spinner">
        <view class="spinner-ring"></view>
        <view class="spinner-ring"></view>
        <view class="spinner-ring"></view>
      </view>
      <view class="loading-text">正在加载仪表盘...</view>
    </view>
  </view>

  <!-- 新用户引导 -->
  <view wx:elif="{{showGuide}}" class="guide-overlay" style="padding-top: {{navbarHeight + 40}}px;">
    <!-- 引导页面导航栏 -->
    <view class="index-navbar guide-theme">
      <view class="status-bar" style="height: {{statusBarHeight}}px;"></view>
      <view class="navbar-content">
        <text class="navbar-title">时间跟踪器</text>
      </view>
    </view>

    <view class="guide-content">
      <view class="guide-icon">💼</view>
      <view class="guide-title">欢迎使用时间跟踪器</view>
      <view class="guide-text">
        <text>请先添加您的工作履历以开始使用</text>
      </view>
      <button class="guide-btn" bindtap="onAddFirstWork">
        <text class="btn-icon">✨</text>
        <text>添加工作履历</text>
      </button>
    </view>
  </view>

  <!-- 主题组件容器 -->
  <view wx:else class="dashboard-container {{isTransitioning ? 'transitioning' : ''}}">
    <!-- 主题切换器 -->
    <dashboard-switcher
      id="dashboard-switcher"
      show="{{showDashboardSwitcher}}"
      current-dashboard="{{currentDashboard}}"
      bind:dashboardChange="onDashboardChange"
      bind:hide="onHideDashboardSwitcher">
    </dashboard-switcher>

    <!-- 简约主题 -->
    <dashboard1
      wx:if="{{currentDashboard === 'dashboard1'}}"
      id="dashboard1"
      class="dashboard-component"
      bind:navigate="onComponentNavigate"
      bind:privacyMaskChange="onPrivacyMaskChange"
      bind:dashboardChange="onDashboardChange"
      bind:settingsChange="onSettingsChange"
      bind:showDashboardSwitcher="onShowDashboardSwitcher">
    </dashboard1>

    <!-- 卡片主题 -->
    <dashboard2
      wx:elif="{{currentDashboard === 'dashboard2'}}"
      id="dashboard2"
      class="dashboard-component"
      bind:navigate="onComponentNavigate"
      bind:privacyMaskChange="onPrivacyMaskChange"
      bind:dashboardChange="onDashboardChange"
      bind:settingsChange="onSettingsChange"
      bind:showDashboardSwitcher="onShowDashboardSwitcher">
    </dashboard2>

    <!-- 切换过渡遮罩 -->
    <view wx:if="{{isTransitioning}}" class="transition-overlay">
      <view class="transition-spinner">
        <view class="spinner-dot"></view>
        <view class="spinner-dot"></view>
        <view class="spinner-dot"></view>
      </view>
    </view>

  </view>
</view>