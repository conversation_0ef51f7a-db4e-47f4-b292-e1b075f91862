// 主入口页面逻辑 - 使用仪表盘组件

const { setNavbarHeightData } = require('../../utils/navbar-height')

Page({
  data: {
    // 当前选择的仪表盘
    currentDashboard: '',

    // 用户状态
    hasWorkHistory: false,

    // 是否显示引导
    showGuide: false,

    // 加载状态
    isLoading: true,

    // 切换动画状态
    isTransitioning: false,

    // 状态栏高度
    statusBarHeight: 0,

    // 导航栏总高度
    navbarHeight: 0,

    // 是否显示仪表盘切换器
    showDashboardSwitcher: false

  },

  /**
   * 页面加载时
   */
  onLoad: function() {
    console.log('主入口页面加载开始')

    // 使用统一的导航栏高度计算工具
    setNavbarHeightData(this, {
      enableLog: true,
      logPrefix: 'Index'
    })

    // 初始化服务
    const app = getApp()
    this.dashboardService = app.getDashboardService()
    this.workHistoryService = app.getWorkHistoryService()

    // 获取全局数据管理器
    this.dataManager = getApp().getDataManager()

    // 添加数据变化监听器
    this.dataChangeListener = this.onDataChange.bind(this)
    this.dashboardService.addChangeListener(this.dataChangeListener)
    this.dataManager.addChangeListener(this.dataChangeListener)

    // 确保用户信息已加载
    this.ensureUserInfo()

    // 初始化页面
    this.initializePage()
  },

  /**
   * 页面显示时
   */
  onShow: function() {
    console.log('主入口页面显示')

    // 确保用户信息已加载
    this.ensureUserInfo()

    // 每次显示时检查仪表盘设置是否有变化
    this.loadDashboardSettings()

    // 刷新当前显示的组件数据
    this.refreshCurrentDashboard()
  },

  /**
   * 页面卸载时
   */
  onUnload: function() {
    console.log('主入口页面卸载')

    // 移除数据变化监听器
    if (this.dataChangeListener) {
      this.dashboardService.removeChangeListener(this.dataChangeListener)
      this.dataManager.removeChangeListener(this.dataChangeListener)
      this.dataChangeListener = null
    }
  },

  /**
   * 确保用户信息已加载
   */
  async ensureUserInfo() {
    try {
      // 确保应用级别的用户信息已加载
      await getApp().ensureUserInfo()
    } catch (error) {
      console.error('确保用户信息失败:', error)
    }
  },

  /**
   * 初始化页面
   */
  initializePage: function() {
    console.log('初始化主入口页面')

    // 显示加载状态
    this.setData({
      isLoading: true
    })

    try {
      // 检查用户状态
      this.checkUserStatus()

      // 加载主题设置
      this.loadDashboardSettings()

      // 延迟隐藏加载状态，确保数据加载完成
      setTimeout(() => {
        this.setData({
          isLoading: false
        })
      }, 300)

    } catch (error) {
      console.error('页面初始化失败:', error)
      // 使用默认设置
      this.setData({
        currentDashboard: 'dashboard1',
        hasWorkHistory: false,
        showGuide: true,
        isLoading: false
      })
    }
  },

  /**
   * 检查用户状态
   */
  checkUserStatus: function() {
    // 检查是否有工作履历
    const hasWorkHistory = this.workHistoryService.hasWorkHistory()

    console.log('用户状态检查:', { hasWorkHistory })

    this.setData({
      hasWorkHistory: hasWorkHistory,
      showGuide: !hasWorkHistory
    })
  },

  /**
   * 加载仪表盘设置
   */
  loadDashboardSettings: function() {
    try {
      // 获取当前选择的仪表盘
      const currentDashboard = this.dashboardService.getCurrentDashboard()
      const previousDashboard = this.data.currentDashboard

      console.log('仪表盘设置:', { previousDashboard, currentDashboard })

      // 如果仪表盘发生变化，添加切换动画
      if (previousDashboard && previousDashboard !== currentDashboard) {
        this.switchDashboardWithAnimation(currentDashboard)
      } else {
        this.setData({
          currentDashboard: currentDashboard
        })
      }

    } catch (error) {
      console.error('加载仪表盘设置失败:', error)
      // 使用默认设置
      this.setData({
        currentDashboard: 'dashboard1'
      })
    }
  },

  /**
   * 带动画的仪表盘切换
   */
  switchDashboardWithAnimation: function(newDashboard) {
    console.log('切换仪表盘动画:', newDashboard)

    // 显示切换动画
    this.setData({
      isTransitioning: true
    })

    // 延迟切换，让淡出动画完成
    setTimeout(() => {
      this.setData({
        currentDashboard: newDashboard
      })

      // 再延迟一下让淡入动画开始
      setTimeout(() => {
        this.setData({
          isTransitioning: false
        })
      }, 50)
    }, 200)
  },

  /**
   * 刷新当前仪表盘
   */
  refreshCurrentDashboard: function() {
    console.log('刷新当前仪表盘数据:', this.data.currentDashboard)

    // 通知当前显示的仪表盘组件刷新数据
    const currentDashboard = this.data.currentDashboard

    if (currentDashboard === 'dashboard1' && this.selectComponent('#dashboard1')) {
      console.log('刷新 dashboard1 组件数据')
      this.selectComponent('#dashboard1').loadData()
    } else if (currentDashboard === 'dashboard2' && this.selectComponent('#dashboard2')) {
      console.log('刷新 dashboard2 组件数据')
      this.selectComponent('#dashboard2').loadData()
    }
  },

  /**
   * 处理组件导航事件
   */
  onComponentNavigate: function(event) {
    const { url, type } = event.detail

    console.log('组件请求导航:', { url, type })

    if (type === 'switchTab') {
      wx.switchTab({ url })
    } else {
      wx.navigateTo({ url })
    }
  },

  /**
   * 处理来自 dashboard1 组件的仪表盘切换事件
   */
  onDashboardChange: function(event) {
    const { dashboardId } = event.detail
    console.log('收到仪表盘切换事件:', dashboardId)

    // 这里可以添加额外的处理逻辑，比如记录日志等
    // 实际的切换逻辑已经在组件内部完成
  },

  /**
   * 处理来自 dashboard1 组件的设置变化事件
   */
  onSettingsChange: function(event) {
    const { dashboardId, config } = event.detail
    console.log('收到设置变化事件:', dashboardId, config)

    // 这里可以添加额外的处理逻辑，比如记录日志等
    // 实际的保存逻辑已经在组件内部完成
  },

  /**
   * 处理显示仪表盘切换器事件
   */
  onShowDashboardSwitcher: function(event) {
    this.setData({
      showDashboardSwitcher: true
    })

    // 延迟显示动画
    setTimeout(() => {
      const switcher = this.selectComponent('#dashboard-switcher')
      if (switcher) {
        switcher.setData({ visible: true })
      }
    }, 50)
  },

  /**
   * 处理隐藏仪表盘切换器事件
   */
  onHideDashboardSwitcher: function(event) {
    // 开始出场动画
    const switcher = this.selectComponent('#dashboard-switcher')
    if (switcher) {
      switcher.setData({ visible: false })
    }

    // 等待动画完成后关闭模态框
    setTimeout(() => {
      this.setData({
        showDashboardSwitcher: false
      })
    }, 300) // 与CSS动画时长一致
  },

  /**
   * 处理隐私脱敏变化事件
   */
  onPrivacyMaskChange: function(event) {
    const { field, value } = event.detail

    console.log('隐私脱敏设置变化:', { field, value })

    // 这里可以保存到用户设置中
    // 暂时只记录日志
  },

  /**
   * 数据变化监听器
   * @param {Object} userData 用户数据
   */
  onDataChange: function(userData) {
    console.log('检测到数据变化，更新仪表盘显示')

    // 如果没有用户数据，可能是来自 dashboardService 的通知
    if (!userData) {
      console.log('收到仪表盘设置变化通知')
      this.loadDashboardSettings()
      this.refreshCurrentDashboard()
      return
    }

    // 检查仪表盘设置是否变化
    const newDashboard = userData.settings.dashboard.currentDashboard
    const currentDashboard = this.data.currentDashboard

    if (newDashboard !== currentDashboard) {
      console.log('仪表盘设置发生变化:', currentDashboard, '->', newDashboard)
      this.switchDashboardWithAnimation(newDashboard)
    }



    // 检查工作履历状态变化
    const hasWorkHistory = Object.keys(userData.workHistory || {}).length > 0
    if (hasWorkHistory !== this.data.hasWorkHistory) {
      this.setData({
        hasWorkHistory: hasWorkHistory,
        showGuide: !hasWorkHistory
      })
    }

    // 刷新当前仪表盘组件的数据
    this.refreshCurrentDashboard()
  },



  /**
   * 阻止事件冒泡
   */
  onStopPropagation: function() {
    // 空方法，用于阻止事件冒泡
  },

  /**
   * 添加第一份工作履历
   */
  onAddFirstWork: function() {
    console.log('用户点击添加第一份工作履历')

    wx.switchTab({
      url: '/pages/work-history/index'
    })
  },



  /**
   * 刷新主题数据
   */
  refreshDashboardData: function() {
    try {
      // 获取当前主题组件并刷新数据
      const currentDashboard = this.data.currentDashboard
      if (currentDashboard === 'dashboard1') {
        const dashboard1 = this.selectComponent('#dashboard1')
        if (dashboard1 && typeof dashboard1.refreshData === 'function') {
          dashboard1.refreshData()
        }
      } else if (currentDashboard === 'dashboard2') {
        const dashboard2 = this.selectComponent('#dashboard2')
        if (dashboard2 && typeof dashboard2.refreshData === 'function') {
          dashboard2.refreshData()
        }
      }
    } catch (error) {
      console.error('刷新主题数据失败:', error)
    }
  }
})
