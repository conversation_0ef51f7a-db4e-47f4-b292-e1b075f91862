/**
 * 用户API模块
 */

import baseApiClient from '../base.js'
import enhancedApiClient from '../enhanced.js'

export class UserApi {
  constructor() {
    this.baseClient = baseApiClient
    this.enhancedClient = enhancedApiClient
  }

  /**
   * 获取用户信息
   * @param {Object} options - 调用选项
   * @returns {Promise<Object>} 用户信息
   */
  async getUserInfo(options = {}) {
    const defaultOptions = {
      cache: false,  // 禁用用户信息缓存
      loading: true,
      loadingKey: 'getUserInfo',
      showLoading: options.showLoading !== false
    }

    return this.enhancedClient.callWithOptions(
      'getUserInfo',
      {},
      { ...defaultOptions, ...options }
    )
  }

  /**
   * 更新用户信息
   * @param {Object} userInfo - 用户信息
   * @param {Object} options - 调用选项
   * @returns {Promise<Object>} 更新结果
   */
  async updateUserInfo(userInfo, options = {}) {
    const result = await this.enhancedClient.callWithOptions(
      'updateUserInfo', 
      userInfo, 
      {
        loading: true,
        loadingKey: 'updateUserInfo',
        showLoading: true,
        showError: true,
        ...options
      }
    )

    // 更新成功后清除用户信息缓存
    if (result.success) {
      this.enhancedClient.clearCache('getUserInfo')
    }

    return result
  }

  /**
   * 获取VIP记录
   * @param {Object} params - 查询参数
   * @param {Object} options - 调用选项
   * @returns {Promise<Object>} VIP记录
   */
  async getVipRecords(params = {}, options = {}) {
    return this.enhancedClient.callWithOptions(
      'getVipRecords', 
      params, 
      {
        cache: true,
        cacheTTL: 2 * 60 * 1000,  // 缓存2分钟
        loading: true,
        loadingKey: 'getVipRecords',
        ...options
      }
    )
  }

  /**
   * 获取VIP统计
   * @param {Object} options - 调用选项
   * @returns {Promise<Object>} VIP统计
   */
  async getVipRecordsStats(options = {}) {
    return this.enhancedClient.callWithOptions(
      'getVipRecordsStats', 
      {}, 
      {
        cache: true,
        cacheTTL: 5 * 60 * 1000,  // 缓存5分钟
        loading: true,
        loadingKey: 'getVipRecordsStats',
        ...options
      }
    )
  }

  /**
   * 获取用户兑换码
   * @param {Object} params - 查询参数
   * @param {Object} options - 调用选项
   * @returns {Promise<Object>} 兑换码列表
   */
  async getUserRedemptionCodes(params = {}, options = {}) {
    return this.enhancedClient.callWithOptions(
      'getUserRedemptionCodes', 
      params, 
      {
        cache: true,
        cacheTTL: 1 * 60 * 1000,  // 缓存1分钟
        loading: true,
        loadingKey: 'getUserRedemptionCodes',
        ...options
      }
    )
  }

  /**
   * 使用兑换码
   * @param {string} code - 兑换码
   * @param {Object} options - 调用选项
   * @returns {Promise<Object>} 使用结果
   */
  async useRedemptionCode(code, options = {}) {
    const result = await this.enhancedClient.callWithOptions(
      'useRedemptionCode', 
      { code }, 
      {
        loading: true,
        loadingKey: 'useRedemptionCode',
        showLoading: true,
        showError: true,
        retry: true,
        retryConfig: {
          maxRetries: 1,
          baseDelay: 1000
        },
        ...options
      }
    )

    // 使用成功后清除缓存
    if (result.success) {
      this.clearUserCache('all')
    }

    return result
  }

  /**
   * 清除用户相关缓存
   * @param {string} type - 缓存类型
   */
  clearUserCache(type = 'all') {
    console.log(`[UserAPI] 清除用户缓存: ${type}`)
    
    switch (type) {
      case 'all':
        this.enhancedClient.clearCache('getUserInfo')
        this.enhancedClient.clearCache('getVipRecords')
        this.enhancedClient.clearCache('getVipRecordsStats')
        this.enhancedClient.clearCache('getUserRedemptionCodes')
        break
      case 'info':
        this.enhancedClient.clearCache('getUserInfo')
        break
      case 'vip':
        this.enhancedClient.clearCache('getVipRecords')
        this.enhancedClient.clearCache('getVipRecordsStats')
        break
      case 'codes':
        this.enhancedClient.clearCache('getUserRedemptionCodes')
        break
    }
  }
}

// 创建用户API实例
const userApi = new UserApi()

export default userApi
export { userApi }
