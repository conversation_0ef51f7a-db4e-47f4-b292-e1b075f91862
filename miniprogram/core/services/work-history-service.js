/**
 * 工作履历服务类 - 重构版
 * 基于新的全局数据管理器实现工作履历管理
 * 
 * 重构说明：
 * - 移除了复杂的缓存机制
 * - 移除了事件通信系统
 * - 使用统一的数据管理器
 * - 简化了API设计
 */

const { formatDate } = require('../../utils/helpers/time-utils.js')

class WorkHistoryService {
  constructor() {
    // 获取数据管理器实例
    this.dataManager = getApp().getDataManager()
  }

  /**
   * 获取所有工作履历
   * @returns {Array} 工作履历数组（按入职日期倒序）
   */
  getAllWorkHistory() {
    try {
      const workHistoryMap = this.dataManager.getWorkHistory()

      // 将Hashmap转换为数组并按入职日期倒序排列（最新入职的在前面）
      const workHistoryList = Object.values(workHistoryMap).sort((a, b) => {
        const dateA = new Date(a.startDate)
        const dateB = new Date(b.startDate)
        return dateB.getTime() - dateA.getTime()
      })

      return workHistoryList
    } catch (error) {
      console.error('获取工作履历失败:', error)
      return []
    }
  }

  /**
   * 获取指定工作履历
   * @param {string} workId - 工作履历ID
   * @returns {Object|null} 工作履历对象或null
   */
  getWorkHistory(workId) {
    try {
      return this.dataManager.getWork(workId)
    } catch (error) {
      console.error('获取工作履历失败:', error)
      return null
    }
  }

  /**
   * 添加工作履历
   * @param {Object} workData - 工作履历数据
   * @returns {string} 新工作履历的ID
   */
  addWorkHistory(workData) {
    try {
      return this.dataManager.addWork(workData)
    } catch (error) {
      console.error('添加工作履历失败:', error)
      throw new Error('添加工作履历失败')
    }
  }

  /**
   * 更新工作履历
   * @param {string} workId - 工作履历ID
   * @param {Object} workData - 更新的工作履历数据
   */
  updateWorkHistory(workId, workData) {
    try {
      this.dataManager.updateWork(workId, workData)
    } catch (error) {
      console.error('更新工作履历失败:', error)
      throw new Error('更新工作履历失败')
    }
  }

  /**
   * 删除工作履历
   * @param {string} workId - 工作履历ID
   */
  deleteWorkHistory(workId) {
    try {
      this.dataManager.deleteWork(workId)
    } catch (error) {
      console.error('删除工作履历失败:', error)
      throw new Error('删除工作履历失败')
    }
  }

  /**
   * 获取当前工作履历
   * @returns {Object|null} 当前工作履历对象或null
   */
  getCurrentWork() {
    try {
      return this.dataManager.getCurrentWork()
    } catch (error) {
      console.error('获取当前工作履历失败:', error)
      return null
    }
  }

  /**
   * 获取当前工作履历ID
   * @returns {string|null} 当前工作履历ID或null
   */
  getCurrentWorkId() {
    try {
      const settings = this.dataManager.getSettings()
      return settings.currentWorkId
    } catch (error) {
      console.error('获取当前工作ID失败:', error)
      return null
    }
  }

  /**
   * 设置当前工作
   * @param {string} workId - 工作履历ID
   */
  setCurrentWork(workId) {
    try {
      this.dataManager.setCurrentWork(workId)
    } catch (error) {
      console.error('设置当前工作失败:', error)
      throw new Error('设置当前工作失败')
    }
  }

  /**
   * 检查是否有工作履历
   * @returns {boolean} 是否有工作履历
   */
  hasWorkHistory() {
    try {
      const workHistoryMap = this.dataManager.getWorkHistory()
      return Object.keys(workHistoryMap).length > 0
    } catch (error) {
      console.error('检查工作履历失败:', error)
      return false
    }
  }

  /**
   * 确保有当前工作（如果没有则创建默认工作）
   * @returns {Object|null} 当前工作履历对象
   */
  ensureCurrentWork() {
    try {
      let currentWork = this.getCurrentWork()
      
      if (!currentWork) {
        // 检查是否有其他工作履历
        const allWorks = this.getAllWorkHistory()
        
        if (allWorks.length > 0) {
          // 如果有其他工作履历，设置第一个为当前工作
          this.setCurrentWork(allWorks[0].id)
          currentWork = allWorks[0]
        } else {
          // 如果没有任何工作履历，创建默认工作履历
          const defaultWorkData = {
            company: '我的公司',
            position: '职位',
            startDate: new Date(),
            probationSalary: 0,
            probationEndDate: null,
            formalSalary: 0,
            endDate: null,
            notes: ''
          }
          
          const workId = this.addWorkHistory(defaultWorkData)
          currentWork = this.getWorkHistory(workId)
        }
      }
      
      return currentWork
    } catch (error) {
      console.error('确保当前工作失败:', error)
      return null
    }
  }

  /**
   * 获取工作履历的显示名称
   * @param {Object} work - 工作履历对象
   * @returns {string} 显示名称
   */
  getWorkDisplayName(work) {
    if (!work) {
      return '未知工作'
    }
    
    return `${work.company} - ${work.position}`
  }

  /**
   * 获取工作履历的状态
   * @param {Object} work - 工作履历对象
   * @returns {string} 状态：'active'(在职) 或 'inactive'(离职)
   */
  getWorkStatus(work) {
    if (!work) {
      return 'inactive'
    }
    
    return work.endDate ? 'inactive' : 'active'
  }

  /**
   * 获取工作时间范围文本
   * @param {Object} work - 工作履历对象
   * @returns {string} 时间范围文本
   */
  getWorkTimeRangeText(work) {
    if (!work || !work.startDate) {
      return '时间未知'
    }
    
    const startText = formatDate(work.startDate)
    const endText = work.endDate ? formatDate(work.endDate) : '至今'
    
    return `${startText} 至 ${endText}`
  }

  /**
   * 获取工作履历统计信息
   * @returns {Object} 统计信息
   */
  getStatistics() {
    try {
      const allWorks = this.getAllWorkHistory()
      const activeWorks = allWorks.filter(work => !work.endDate)
      const inactiveWorks = allWorks.filter(work => work.endDate)
      
      return {
        total: allWorks.length,
        active: activeWorks.length,
        inactive: inactiveWorks.length
      }
    } catch (error) {
      console.error('获取工作履历统计失败:', error)
      return {
        total: 0,
        active: 0,
        inactive: 0
      }
    }
  }

  /**
   * 导出工作履历数据
   * @returns {string} JSON格式的工作履历数据
   */
  exportWorkHistory() {
    try {
      const workHistoryMap = this.dataManager.getWorkHistory()
      const exportData = {
        version: '0.2.0',
        exportTime: new Date().toISOString(),
        workHistory: workHistoryMap
      }
      
      return JSON.stringify(exportData, null, 2)
    } catch (error) {
      console.error('导出工作履历失败:', error)
      throw new Error('导出工作履历失败')
    }
  }

  /**
   * 清除所有工作履历数据
   */
  clearAllData() {
    try {
      this.dataManager.clearAllData()
      console.log('所有工作履历数据已清除')
    } catch (error) {
      console.error('清除工作履历数据失败:', error)
      throw new Error('清除数据失败')
    }
  }

  /**
   * 添加数据变化监听器
   * @param {Function} listener - 监听器函数
   */
  addChangeListener(listener) {
    this.dataManager.addChangeListener(listener)
  }

  /**
   * 计算距离下一个发薪日的天数
   * @param {string} workId - 工作履历ID，如果不提供则使用当前工作
   * @returns {Object} 发薪日信息 {days: number, payDayName: string, nextPayDate: Date}
   */
  getNextPayDayInfo(workId = null) {
    try {
      const targetWorkId = workId || this.getCurrentWorkId()
      if (!targetWorkId) {
        return {
          days: 0,
          payDayName: '未设置',
          nextPayDate: null
        }
      }

      const work = this.getWorkHistory(targetWorkId)
      if (!work || !work.payDays || work.payDays.length === 0) {
        return {
          days: 0,
          payDayName: '未设置',
          nextPayDate: null
        }
      }

      const today = new Date()
      // 获取今天的日期部分（忽略时间）
      const todayDateOnly = new Date(today.getFullYear(), today.getMonth(), today.getDate())

      if (work.payDays.length === 0) {
        return {
          days: 0,
          payDayName: '未设置',
          nextPayDate: null
        }
      }

      let nearestPayDay = null
      let minDays = Infinity

      // 检查当前月和下个月的发薪日
      for (let monthOffset = 0; monthOffset <= 1; monthOffset++) {
        const targetMonth = new Date(today.getFullYear(), today.getMonth() + monthOffset, 1)

        for (const payDay of work.payDays) {
          const payDate = this.calculatePayDate(targetMonth, payDay.day)

          // 考虑今天和未来的日期（基于日期比较，忽略时间）
          if (payDate >= todayDateOnly) {
            const daysDiff = Math.ceil((payDate - todayDateOnly) / (1000 * 60 * 60 * 24))
            if (daysDiff < minDays) {
              minDays = daysDiff
              nearestPayDay = {
                days: daysDiff,
                payDayName: payDay.name,
                nextPayDate: payDate
              }
            }
          }
        }
      }

      return nearestPayDay || {
        days: 0,
        payDayName: '未设置',
        nextPayDate: null
      }
    } catch (error) {
      console.error('计算发薪日失败:', error)
      return {
        days: 0,
        payDayName: '未设置',
        nextPayDate: null
      }
    }
  }

  /**
   * 计算指定月份的发薪日期
   * @param {Date} monthDate - 月份日期
   * @param {number} payDay - 发薪日（正数表示第几号，负数表示倒数第几天）
   * @returns {Date} 实际发薪日期
   */
  calculatePayDate(monthDate, payDay) {
    const year = monthDate.getFullYear()
    const month = monthDate.getMonth()

    // 获取该月的最后一天
    const lastDayOfMonth = new Date(year, month + 1, 0).getDate()

    let actualDay
    if (payDay > 0) {
      // 正数：第几号
      actualDay = Math.min(payDay, lastDayOfMonth)
    } else {
      // 负数：倒数第几天
      // payDay是负数，例如-1表示倒数第1天（月末），-3表示倒数第3天
      // 计算公式：lastDayOfMonth + payDay + 1
      // 例如：31天的月份，-1 = 31 + (-1) + 1 = 31（月末）
      //      31天的月份，-3 = 31 + (-3) + 1 = 29（倒数第3天）
      actualDay = lastDayOfMonth + payDay + 1
      // 确保不会小于1
      actualDay = Math.max(1, actualDay)
    }

    return new Date(year, month, actualDay)
  }

  /**
   * 获取工作履历的发薪日列表
   * @param {string} workId - 工作履历ID
   * @returns {Array} 发薪日列表
   */
  getPayDays(workId) {
    try {
      const work = this.getWorkHistory(workId)
      return work && work.payDays ? work.payDays : []
    } catch (error) {
      console.error('获取发薪日列表失败:', error)
      return []
    }
  }

  /**
   * 验证发薪日列表
   * @param {Array} payDays - 发薪日列表
   * @returns {Object} 验证结果 {isValid: boolean, errors: string[]}
   */
  validatePayDays(payDays) {
    const errors = []

    if (!Array.isArray(payDays)) {
      errors.push('发薪日列表必须是数组')
      return { isValid: false, errors }
    }

    // 检查重复的发薪日
    const daySet = new Set()
    for (const payDay of payDays) {
      // 支持正数（1-31）和负数（-1到-31）
      if (!payDay.day || payDay.day === 0 || Math.abs(payDay.day) < 1 || Math.abs(payDay.day) > 31) {
        errors.push('发薪日期必须在1-31之间或倒数1-31天之间')
        continue
      }

      if (daySet.has(payDay.day)) {
        const dayText = payDay.day > 0 ? `${payDay.day}号` : `倒数第${Math.abs(payDay.day)}天`
        errors.push(`发薪日期${dayText}重复，请删除重复项`)
      } else {
        daySet.add(payDay.day)
      }

      if (!payDay.name || payDay.name.trim() === '') {
        const dayText = payDay.day > 0 ? `${payDay.day}号` : `倒数第${Math.abs(payDay.day)}天`
        errors.push(`发薪日期${dayText}的名称不能为空`)
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    }
  }

  /**
   * 更新工作履历的发薪日设置
   * @param {string} workId - 工作履历ID
   * @param {Array} payDays - 发薪日列表
   */
  updatePayDays(workId, payDays) {
    try {
      const work = this.getWorkHistory(workId)
      if (!work) {
        throw new Error('工作履历不存在')
      }

      // 验证发薪日列表
      const validation = this.validatePayDays(payDays)
      if (!validation.isValid) {
        throw new Error('发薪日验证失败: ' + validation.errors.join(', '))
      }

      this.updateWorkHistory(workId, { payDays })
    } catch (error) {
      console.error('更新发薪日设置失败:', error)
      throw new Error('更新发薪日设置失败')
    }
  }



  /**
   * 移除数据变化监听器
   * @param {Function} listener - 监听器函数
   */
  removeChangeListener(listener) {
    this.dataManager.removeChangeListener(listener)
  }
}

// 导出单例实例
let workHistoryServiceInstance = null

function getWorkHistoryService() {
  if (!workHistoryServiceInstance) {
    workHistoryServiceInstance = new WorkHistoryService()
  }
  return workHistoryServiceInstance
}

module.exports = {
  WorkHistoryService,
  getWorkHistoryService
}