/**
 * 主题配置服务
 * 基于全局数据管理器的主题配置和切换服务
 */

/**
 * 主题类型定义
 * 支持扩展更多主题类型
 */
const DASHBOARD_TYPES = {
  DASHBOARD1: {
    id: 'dashboard1',
    name: '简约主题',
    description: '简洁清爽的设计，专注核心信息',
    icon: '✨️',
    component: 'dashboard1',
    isDefault: true,
    // 默认配置
    defaultConfig: {
      showRealTimeIncome: true,
      showQuickActions: true,
      chartHeight: 120,
      showCurrentWork: true
    }
  },
  DASHBOARD2: {
    id: 'dashboard2',
    name: '卡片主题',
    description: '现代卡片式布局，信息层次分明',
    icon: '🎨',
    component: 'dashboard2',
    isDefault: false,
    // 默认配置
    defaultConfig: {
      showCountdown: true,
      showAllStats: true,
      circularProgressSize: 400,
      showCurrentWork: true
    }
  }
  // 未来可以在这里添加更多主题类型
  // DASHBOARD3: {
  //   id: 'dashboard3',
  //   name: '未来主题',
  //   description: '科技感十足的未来风格',
  //   icon: '🚀',
  //   component: 'dashboard3',
  //   isDefault: false,
  //   defaultConfig: {
  //     theme: 'dark',
  //     animations: true
  //   }
  // }
}

/**
 * 主题服务类
 * 独立的主题业务逻辑，但数据存储集成到 DataManager 中
 */
class DashboardService {
  constructor() {
    // 获取数据管理器实例
    this.dataManager = getApp().getDataManager()
  }

  /**
   * 获取所有可用的主题
   * @returns {Array} 主题数组
   */
  getAllDashboards() {
    return Object.values(DASHBOARD_TYPES)
  }

  /**
   * 获取主题信息
   * @param {string} dashboardId 主题ID
   * @returns {Object|null} 主题信息
   */
  getDashboardInfo(dashboardId) {
    const dashboardKey = Object.keys(DASHBOARD_TYPES).find(
      key => DASHBOARD_TYPES[key].id === dashboardId
    )
    return dashboardKey ? DASHBOARD_TYPES[dashboardKey] : null
  }

  /**
   * 获取当前主题ID
   * @returns {string} 主题ID
   */
  getCurrentDashboard() {
    return this.dataManager.getCurrentDashboard()
  }

  /**
   * 设置当前主题
   * @param {string} dashboardId 主题ID
   */
  setCurrentDashboard(dashboardId) {
    const dashboardInfo = this.getDashboardInfo(dashboardId)
    if (!dashboardInfo) {
      throw new Error('主题不存在: ' + dashboardId)
    }

    this.dataManager.setCurrentDashboard(dashboardId)
    console.log('当前主题已设置为:', dashboardId)
  }

  /**
   * 获取主题设置
   * @param {string} dashboardId 主题ID
   * @returns {Object} 主题设置
   */
  getDashboardSettings(dashboardId) {
    const config = this.dataManager.getDashboardConfig(dashboardId)
    const dashboardInfo = this.getDashboardInfo(dashboardId)

    // 合并默认配置和用户配置
    return {
      ...(dashboardInfo?.defaultConfig || {}),
      ...config
    }
  }

  /**
   * 更新主题设置（全局设置）
   * @param {Object} settings 设置对象
   */
  updateDashboardSettings(settings) {
    this.dataManager.updateDashboardSettings(settings)
    console.log('主题全局设置已更新:', settings)
  }

  /**
   * 更新特定主题的配置
   * @param {string} dashboardId 主题ID
   * @param {Object} config 配置对象
   */
  updateDashboardConfig(dashboardId, config) {
    const dashboardInfo = this.getDashboardInfo(dashboardId)
    if (!dashboardInfo) {
      throw new Error('主题不存在: ' + dashboardId)
    }

    this.dataManager.updateDashboardConfig(dashboardId, config)
    console.log('主题配置已更新:', dashboardId, config)
  }

  /**
   * 重置主题设置
   * @param {string} dashboardId 主题ID
   */
  resetDashboardSettings(dashboardId) {
    const dashboardInfo = this.getDashboardInfo(dashboardId)
    if (!dashboardInfo) {
      throw new Error('主题不存在: ' + dashboardId)
    }

    // 重置为默认配置
    this.dataManager.updateDashboardConfig(dashboardId, dashboardInfo.defaultConfig || {})
    console.log('主题设置已重置:', dashboardId)
  }

  /**
   * 检查是否首次使用
   * @returns {boolean} 是否首次使用
   */
  isFirstTime() {
    const settings = this.dataManager.getDashboardSettings()
    return settings.isFirstTime || false
  }

  /**
   * 标记首次使用完成
   */
  markFirstTimeComplete() {
    this.dataManager.updateDashboardSettings({
      isFirstTime: false
    })
    console.log('首次使用标记已完成')
  }

  /**
   * 添加新的主题类型（扩展性支持）
   * @param {Object} dashboardType 主题类型定义
   */
  addDashboardType(dashboardType) {
    if (!dashboardType.id || !dashboardType.name || !dashboardType.component) {
      throw new Error('主题类型定义不完整')
    }

    // 动态添加到类型定义中
    const key = dashboardType.id.toUpperCase()
    DASHBOARD_TYPES[key] = {
      isDefault: false,
      defaultConfig: {},
      ...dashboardType
    }

    console.log('新主题类型已添加:', dashboardType.id)
  }

  /**
   * 获取配置摘要
   * @returns {Object} 配置摘要
   */
  getConfigSummary() {
    const settings = this.dataManager.getDashboardSettings()
    return {
      currentDashboard: settings.currentDashboard,
      availableDashboards: this.getAllDashboards().map(d => d.id),
      isFirstTime: settings.isFirstTime,
      totalConfigs: Object.keys(settings.configs || {}).length
    }
  }

  /**
   * 添加数据变化监听器
   * @param {Function} listener 监听器函数
   */
  addChangeListener(listener) {
    this.dataManager.addChangeListener(listener)
  }

  /**
   * 移除数据变化监听器
   * @param {Function} listener 监听器函数
   */
  removeChangeListener(listener) {
    this.dataManager.removeChangeListener(listener)
  }
}

// 导出单例实例
let dashboardServiceInstance = null

function getDashboardService() {
  if (!dashboardServiceInstance) {
    dashboardServiceInstance = new DashboardService()
  }
  return dashboardServiceInstance
}

module.exports = {
  DashboardService,
  getDashboardService,
  DASHBOARD_TYPES
}
