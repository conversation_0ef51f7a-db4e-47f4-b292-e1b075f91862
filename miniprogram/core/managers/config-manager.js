/**
 * 配置管理器
 * 负责在应用启动时加载云端配置并提供全局访问接口
 */

import { api } from '../api/index.js'

class ConfigManager {
  constructor() {
    this.configs = {}
    this.isInitialized = false
    this.isInitializing = false
    
    // 默认配置（降级方案）
    this.defaultConfigs = {
      'statistics_page': {
        enabled: true,
        title: '即将上线',
        message: '我们正在努力完善这个功能'
      }
    }
  }

  /**
   * 初始化配置管理器
   */
  async initialize() {
    if (this.isInitialized) {
      console.log('[ConfigManager] 配置管理器已初始化')
      return
    }

    if (this.isInitializing) {
      console.log('[ConfigManager] 配置管理器正在初始化，等待完成...')
      return this.waitForInitialization()
    }

    this.isInitializing = true

    try {
      console.log('[ConfigManager] 开始初始化配置管理器...')
      
      await this.loadCloudConfigs()
      
      this.isInitialized = true
      console.log('[ConfigManager] 配置管理器初始化完成')
    } catch (error) {
      console.error('[ConfigManager] 配置管理器初始化失败:', error)
      // 使用默认配置
      this.configs = { ...this.defaultConfigs }
      this.isInitialized = true
    } finally {
      this.isInitializing = false
    }
  }

  /**
   * 等待初始化完成
   */
  async waitForInitialization() {
    return new Promise((resolve) => {
      const checkInitialized = () => {
        if (this.isInitialized) {
          resolve()
        } else {
          setTimeout(checkInitialized, 100)
        }
      }
      checkInitialized()
    })
  }

  /**
   * 加载云端配置
   */
  async loadCloudConfigs() {
    try {
      console.log('[ConfigManager] 开始加载云端配置')

      const result = await api.config.getAllConfigs()

      if (result && result.success) {
        this.configs = result.data || {}
        console.log('[ConfigManager] 云端配置加载成功:', this.configs)
      } else {
        console.warn('[ConfigManager] 云端配置加载失败，使用默认配置:', result?.message)
        this.configs = { ...this.defaultConfigs }
      }
    } catch (error) {
      console.error('[ConfigManager] 云端配置加载异常，使用默认配置:', error)
      this.configs = { ...this.defaultConfigs }
    }
  }

  /**
   * 获取配置值
   * @param {string} key - 配置键
   * @param {any} defaultValue - 默认值
   * @returns {any} 配置值
   */
  get(key, defaultValue = null) {
    // 如果配置管理器还未初始化，返回默认值
    if (!this.isInitialized) {
      console.warn(`[ConfigManager] 配置管理器未初始化，使用默认值: ${key}`)
      return this.getDefaultValue(key, defaultValue)
    }

    // 返回配置值或默认值
    if (this.configs.hasOwnProperty(key)) {
      return this.configs[key]
    } else {
      console.warn(`[ConfigManager] 配置不存在，使用默认值: ${key}`)
      return this.getDefaultValue(key, defaultValue)
    }
  }

  /**
   * 获取默认值
   */
  getDefaultValue(key, fallback = null) {
    return this.defaultConfigs.hasOwnProperty(key) 
      ? this.defaultConfigs[key] 
      : fallback
  }

  /**
   * 检查页面是否可访问
   * @param {string} pageName - 页面名称
   * @returns {Object} { enabled, title, message }
   */
  checkPageAccess(pageName) {
    const configKey = `${pageName}_page`
    const pageConfig = this.get(configKey, { enabled: true, title: '即将上线', message: '我们正在努力完善这个功能' })

    // 确保返回的配置格式正确
    if (typeof pageConfig === 'object' && pageConfig !== null) {
      return {
        enabled: pageConfig.enabled !== false, // 默认允许访问
        title: pageConfig.title || '即将上线',
        message: pageConfig.message || '我们正在努力完善这个功能'
      }
    } else {
      // 兼容旧格式或异常情况
      return {
        enabled: true,
        title: '即将上线',
        message: '我们正在努力完善这个功能'
      }
    }
  }

  /**
   * 重新加载配置
   */
  async reload() {
    console.log('[ConfigManager] 重新加载配置')
    this.isInitialized = false
    this.isInitializing = false
    await this.initialize()
  }

  /**
   * 获取所有配置
   */
  getAll() {
    return { ...this.configs }
  }

  /**
   * 检查配置是否已加载
   */
  isLoaded() {
    return this.isInitialized
  }

  /**
   * 设置默认配置（用于测试或特殊情况）
   */
  setDefaultConfigs(configs) {
    this.defaultConfigs = { ...this.defaultConfigs, ...configs }
  }

  /**
   * 清理资源
   */
  cleanup() {
    console.log('[ConfigManager] 清理配置管理器资源')
    this.configs = {}
    this.isInitialized = false
    this.isInitializing = false
  }
}

// 创建单例实例
let configManagerInstance = null

/**
 * 获取配置管理器实例
 * @returns {ConfigManager} 配置管理器实例
 */
function getConfigManager() {
  if (!configManagerInstance) {
    configManagerInstance = new ConfigManager()
  }
  return configManagerInstance
}

module.exports = {
  ConfigManager,
  getConfigManager
}
