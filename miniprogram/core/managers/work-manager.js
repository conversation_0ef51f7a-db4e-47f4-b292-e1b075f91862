/**
 * 工作履历管理器
 * 负责工作履历的CRUD操作和相关业务逻辑
 * 
 * 功能特性：
 * - 工作履历的增删改查
 * - 当前工作的管理
 * - 工作履历数据验证
 * - 业务逻辑封装
 */

/**
 * 工作履历管理器类
 */
class WorkManager {
  constructor() {
    // 数据变化监听器数组
    this.changeListeners = []
  }

  /**
   * 初始化工作履历数据结构
   * @returns {Object} 初始化的工作履历对象
   */
  initializeWorkHistory() {
    return {}
  }

  /**
   * 验证工作履历数据
   * @param {Object} workData - 工作履历数据
   * @returns {Object} 验证结果 {isValid: boolean, errors: string[]}
   */
  validateWorkData(workData) {
    const errors = []

    if (!workData.company || workData.company.trim() === '') {
      errors.push('公司名称不能为空')
    }

    if (!workData.position || workData.position.trim() === '') {
      errors.push('职位不能为空')
    }

    if (!workData.startDate) {
      errors.push('入职日期不能为空')
    }

    if (workData.probationSalary && workData.probationSalary < 0) {
      errors.push('试用期薪资不能为负数')
    }

    if (workData.formalSalary && workData.formalSalary < 0) {
      errors.push('正式薪资不能为负数')
    }

    if (workData.endDate && workData.startDate && workData.endDate <= workData.startDate) {
      errors.push('离职日期必须晚于入职日期')
    }

    return {
      isValid: errors.length === 0,
      errors: errors
    }
  }

  /**
   * 创建新的工作履历
   * @param {Object} workData - 工作履历数据
   * @returns {Object} 创建的工作履历对象
   */
  createWork(workData) {
    try {
      // 验证输入参数
      if (!workData || typeof workData !== 'object') {
        throw new Error('工作履历数据无效')
      }

      // 验证数据
      const validation = this.validateWorkData(workData)
      if (!validation.isValid) {
        console.error('工作履历数据验证失败:', validation.errors)
        throw new Error('工作履历数据验证失败: ' + validation.errors.join(', '))
      }

      const workId = Date.now().toString()

      // 安全地处理日期
      let startDate, probationEndDate, endDate
      try {
        startDate = new Date(workData.startDate)
        if (isNaN(startDate.getTime())) {
          throw new Error('入职日期格式无效')
        }

        probationEndDate = workData.probationEndDate ? new Date(workData.probationEndDate) : null
        if (probationEndDate && isNaN(probationEndDate.getTime())) {
          throw new Error('试用期结束日期格式无效')
        }

        endDate = workData.endDate ? new Date(workData.endDate) : null
        if (endDate && isNaN(endDate.getTime())) {
          throw new Error('离职日期格式无效')
        }
      } catch (dateError) {
        console.error('日期处理失败:', dateError.message)
        throw new Error('日期格式错误: ' + dateError.message)
      }

      const newWork = {
        id: workId,
        company: workData.company.trim(),
        position: workData.position.trim(),
        startDate: startDate,
        probationEndDate: probationEndDate,
        formalSalary: Number(workData.formalSalary) || 0,
        probationSalary: Number(workData.probationSalary) || 0,
        endDate: endDate,
        notes: workData.notes ? workData.notes.trim() : '',
        payDays: workData.payDays || [],
        timeTracking: {},
        createTime: new Date(),
        updateTime: new Date()
      }

      console.log('工作履历创建成功:', workId)
      return newWork

    } catch (error) {
      console.error('创建工作履历失败:', error.message)
      throw error
    }
  }

  /**
   * 更新工作履历
   * @param {Object} existingWork - 现有的工作履历
   * @param {Object} updateData - 更新的数据
   * @returns {Object} 更新后的工作履历对象
   */
  updateWork(existingWork, updateData) {
    // 验证更新数据
    const mergedData = Object.assign({}, existingWork, updateData)
    const validation = this.validateWorkData(mergedData)
    if (!validation.isValid) {
      throw new Error('工作履历数据验证失败: ' + validation.errors.join(', '))
    }

    const updatedWork = Object.assign({}, existingWork, {
      company: updateData.company ? updateData.company.trim() : existingWork.company,
      position: updateData.position ? updateData.position.trim() : existingWork.position,
      startDate: updateData.startDate ? new Date(updateData.startDate) : existingWork.startDate,
      probationEndDate: updateData.probationEndDate !== undefined ?
        (updateData.probationEndDate ? new Date(updateData.probationEndDate) : null) :
        existingWork.probationEndDate,
      formalSalary: updateData.formalSalary !== undefined ? updateData.formalSalary : existingWork.formalSalary,
      probationSalary: updateData.probationSalary !== undefined ? updateData.probationSalary : existingWork.probationSalary,
      endDate: updateData.endDate !== undefined ?
        (updateData.endDate ? new Date(updateData.endDate) : null) :
        existingWork.endDate,
      notes: updateData.notes !== undefined ? updateData.notes : existingWork.notes,
      payDays: updateData.payDays !== undefined ? updateData.payDays : existingWork.payDays,
      updateTime: new Date()
    })

    return updatedWork
  }

  /**
   * 获取工作履历列表（按入职日期倒序排序）
   * @param {Object} workHistory - 工作履历对象
   * @returns {Array} 排序后的工作履历数组
   */
  getWorkList(workHistory) {
    return Object.values(workHistory).sort((a, b) => {
      const dateA = new Date(a.startDate)
      const dateB = new Date(b.startDate)
      return dateB.getTime() - dateA.getTime()
    })
  }

  /**
   * 获取当前在职的工作履历
   * @param {Object} workHistory - 工作履历对象
   * @returns {Array} 当前在职的工作履历数组
   */
  getCurrentWorks(workHistory) {
    return Object.values(workHistory).filter(work => !work.endDate)
  }

  /**
   * 获取已离职的工作履历
   * @param {Object} workHistory - 工作履历对象
   * @returns {Array} 已离职的工作履历数组
   */
  getPastWorks(workHistory) {
    return Object.values(workHistory).filter(work => work.endDate)
  }

  /**
   * 计算工作天数
   * @param {Object} work - 工作履历对象
   * @returns {number} 工作天数
   */
  calculateWorkDays(work) {
    const startDate = new Date(work.startDate)
    const endDate = work.endDate ? new Date(work.endDate) : new Date()
    
    const timeDiff = endDate.getTime() - startDate.getTime()
    return Math.ceil(timeDiff / (1000 * 3600 * 24))
  }

  /**
   * 判断是否在试用期
   * @param {Object} work - 工作履历对象
   * @param {Date} checkDate - 检查日期，默认为当前日期
   * @returns {boolean} 是否在试用期
   */
  isInProbation(work, checkDate = new Date()) {
    if (!work.probationEndDate) {
      return false
    }
    
    const startDate = new Date(work.startDate)
    const probationEndDate = new Date(work.probationEndDate)
    
    return checkDate >= startDate && checkDate <= probationEndDate
  }

  /**
   * 获取当前薪资（根据是否在试用期）
   * @param {Object} work - 工作履历对象
   * @param {Date} checkDate - 检查日期，默认为当前日期
   * @returns {number} 当前薪资
   */
  getCurrentSalary(work, checkDate = new Date()) {
    if (this.isInProbation(work, checkDate)) {
      return work.probationSalary || 0
    }
    return work.formalSalary || 0
  }

  /**
   * 格式化工作履历显示信息
   * @param {Object} work - 工作履历对象
   * @returns {Object} 格式化后的显示信息
   */
  formatWorkDisplay(work) {
    const workDays = this.calculateWorkDays(work)
    const currentSalary = this.getCurrentSalary(work)
    const isCurrentlyWorking = !work.endDate
    const isInProbation = this.isInProbation(work)

    return {
      id: work.id,
      title: `${work.company} - ${work.position}`,
      company: work.company,
      position: work.position,
      startDate: work.startDate,
      endDate: work.endDate,
      workDays: workDays,
      currentSalary: currentSalary,
      isCurrentlyWorking: isCurrentlyWorking,
      isInProbation: isInProbation,
      status: isCurrentlyWorking ? (isInProbation ? '试用期' : '在职') : '已离职'
    }
  }

  /**
   * 添加数据变化监听器
   * @param {Function} listener - 监听器函数
   */
  addChangeListener(listener) {
    if (typeof listener === 'function') {
      this.changeListeners.push(listener)
    }
  }

  /**
   * 移除数据变化监听器
   * @param {Function} listener - 要移除的监听器函数
   */
  removeChangeListener(listener) {
    const index = this.changeListeners.indexOf(listener)
    if (index > -1) {
      this.changeListeners.splice(index, 1)
    }
  }

  /**
   * 通知所有监听器数据已变化
   */
  notifyChange() {
    this.changeListeners.forEach(listener => {
      try {
        listener()
      } catch (error) {
        console.error('工作履历变化监听器执行失败:', error)
      }
    })
  }

  // ==================== 数据操作方法 ====================

  /**
   * 添加工作履历到数据中
   * @param {Function} getWorkHistoryCallback - 获取工作履历数据的回调函数
   * @param {Function} saveDataCallback - 保存数据的回调函数
   * @param {Function} notifyChangeCallback - 通知数据变化的回调函数
   * @param {Function} getCurrentWorkIdCallback - 获取当前工作ID的回调函数
   * @param {Function} setCurrentWorkIdCallback - 设置当前工作ID的回调函数
   * @param {Object} workData - 工作履历数据
   * @returns {string} 新工作履历的ID
   */
  addWorkToData(getWorkHistoryCallback, saveDataCallback, notifyChangeCallback, getCurrentWorkIdCallback, setCurrentWorkIdCallback, workData) {
    const newWork = this.createWork(workData)
    const workId = newWork.id
    const workHistory = getWorkHistoryCallback()

    workHistory[workId] = newWork

    // 如果是第一个工作履历，设置为当前工作
    if (Object.keys(workHistory).length === 1) {
      setCurrentWorkIdCallback(workId)
    }

    saveDataCallback()
    notifyChangeCallback()

    return workId
  }

  /**
   * 更新工作履历数据
   * @param {Function} getWorkHistoryCallback - 获取工作履历数据的回调函数
   * @param {Function} saveDataCallback - 保存数据的回调函数
   * @param {Function} notifyChangeCallback - 通知数据变化的回调函数
   * @param {string} workId - 工作履历ID
   * @param {Object} updateData - 更新的数据
   */
  updateWorkInData(getWorkHistoryCallback, saveDataCallback, notifyChangeCallback, workId, updateData) {
    const workHistory = getWorkHistoryCallback()

    if (!workHistory[workId]) {
      throw new Error('工作履历不存在')
    }

    const existingWork = workHistory[workId]
    const updatedWork = this.updateWork(existingWork, updateData)

    workHistory[workId] = updatedWork

    saveDataCallback()
    notifyChangeCallback()
  }

  /**
   * 删除工作履历数据
   * @param {Function} getWorkHistoryCallback - 获取工作履历数据的回调函数
   * @param {Function} saveDataCallback - 保存数据的回调函数
   * @param {Function} notifyChangeCallback - 通知数据变化的回调函数
   * @param {Function} getCurrentWorkIdCallback - 获取当前工作ID的回调函数
   * @param {Function} setCurrentWorkIdCallback - 设置当前工作ID的回调函数
   * @param {string} workId - 工作履历ID
   */
  deleteWorkFromData(getWorkHistoryCallback, saveDataCallback, notifyChangeCallback, getCurrentWorkIdCallback, setCurrentWorkIdCallback, workId) {
    const workHistory = getWorkHistoryCallback()

    if (!workHistory[workId]) {
      throw new Error('工作履历不存在')
    }

    delete workHistory[workId]

    // 如果删除的是当前工作，需要重新设置当前工作
    const currentWorkId = getCurrentWorkIdCallback()
    if (currentWorkId === workId) {
      const remainingWorkIds = Object.keys(workHistory)
      const newCurrentWorkId = remainingWorkIds.length > 0 ? remainingWorkIds[0] : null
      setCurrentWorkIdCallback(newCurrentWorkId)
    }

    saveDataCallback()
    notifyChangeCallback()
  }

  /**
   * 设置当前工作
   * @param {Function} getWorkHistoryCallback - 获取工作履历数据的回调函数
   * @param {Function} saveDataCallback - 保存数据的回调函数
   * @param {Function} notifyChangeCallback - 通知数据变化的回调函数
   * @param {Function} setCurrentWorkIdCallback - 设置当前工作ID的回调函数
   * @param {string} workId - 工作履历ID
   * @param {boolean} notify - 是否通知变化，默认为true
   */
  setCurrentWorkInData(getWorkHistoryCallback, saveDataCallback, notifyChangeCallback, setCurrentWorkIdCallback, workId, notify = true) {
    const workHistory = getWorkHistoryCallback()

    if (!workHistory[workId]) {
      throw new Error('工作履历不存在')
    }

    setCurrentWorkIdCallback(workId)
    saveDataCallback()

    if (notify) {
      notifyChangeCallback()
    }
  }
}

// 导出单例实例
let workManagerInstance = null

function getWorkManager() {
  if (!workManagerInstance) {
    workManagerInstance = new WorkManager()
  }
  return workManagerInstance
}

module.exports = {
  WorkManager,
  getWorkManager
}
