/**
 * 设置管理器
 * 负责用户设置的管理和配置
 * 
 * 功能特性：
 * - 用户设置的增删改查
 * - 仪表盘配置管理
 * - 隐私设置管理
 * - 应用配置管理
 */

/**
 * 设置管理器类
 */
class SettingsManager {
  constructor() {
    // 数据变化监听器数组
    this.changeListeners = []
  }

  /**
   * 初始化默认设置
   * @returns {Object} 默认设置对象
   */
  initializeSettings() {
    return {
      // 当前工作ID
      currentWorkId: null,
      
      // 收入显示设置
      income: {
        // 货币符号
        currencySymbol: '¥',
        // 货币代码
        currencyCode: 'CNY'
      },
      
      // 显示设置
      display: {
        // 隐私脱敏设置
        privacyMask: {
          todayIncome: false,
          monthIncome: false,
          currentIncome: false,
          currentHourlyRate: false
        }
      },
      
      // 应用设置
      app: {
        // 主题设置
        theme: 'default',
        // 语言设置
        language: 'zh-CN'
      },

      // 仪表盘设置
      dashboard: {
        // 当前选择的仪表盘ID
        currentDashboard: 'dashboard1',
        // 是否首次使用
        isFirstTime: false,

        // 各仪表盘的个性化配置
        configs: {
          dashboard1: {
            showRealTimeIncome: true,
            showQuickActions: true,
            chartHeight: 120,
            showCurrentWork: true,
            incomeDecimalPlaces: 3
          },
          dashboard2: {
            showCountdown: true,
            showAllStats: true,
            circularProgressSize: 400,
            showCurrentWork: true,
            incomeDecimalPlaces: 3
          }
        }
      }
    }
  }

  /**
   * 验证设置数据
   * @param {Object} settings - 设置数据
   * @returns {Object} 验证结果 {isValid: boolean, errors: string[]}
   */
  validateSettings(settings) {
    const errors = []

    // 验证仪表盘设置
    if (settings.dashboard) {
      const validDashboards = ['dashboard1', 'dashboard2']
      if (settings.dashboard.currentDashboard && 
          !validDashboards.includes(settings.dashboard.currentDashboard)) {
        errors.push('无效的仪表盘类型')
      }
    }

    return {
      isValid: errors.length === 0,
      errors: errors
    }
  }

  /**
   * 合并设置
   * @param {Object} currentSettings - 当前设置
   * @param {Object} newSettings - 新设置
   * @returns {Object} 合并后的设置
   */
  mergeSettings(currentSettings, newSettings) {
    // 深度合并设置对象
    const mergedSettings = JSON.parse(JSON.stringify(currentSettings))
    
    this.deepMerge(mergedSettings, newSettings)
    
    return mergedSettings
  }

  /**
   * 深度合并对象
   * @param {Object} target - 目标对象
   * @param {Object} source - 源对象
   */
  deepMerge(target, source) {
    for (const key in source) {
      if (source.hasOwnProperty(key)) {
        if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
          if (!target[key] || typeof target[key] !== 'object') {
            target[key] = {}
          }
          this.deepMerge(target[key], source[key])
        } else {
          target[key] = source[key]
        }
      }
    }
  }

  /**
   * 确保仪表盘设置存在
   * @param {Object} settings - 设置对象
   */
  ensureDashboardSettings(settings) {
    if (!settings.dashboard) {
      settings.dashboard = this.initializeSettings().dashboard
    }

    // 确保配置对象存在
    if (!settings.dashboard.configs) {
      settings.dashboard.configs = this.initializeSettings().dashboard.configs
    }
  }

  /**
   * 获取当前仪表盘ID
   * @param {Object} settings - 设置对象
   * @returns {string} 仪表盘ID
   */
  getCurrentDashboard(settings) {
    this.ensureDashboardSettings(settings)
    return settings.dashboard.currentDashboard
  }

  /**
   * 设置当前仪表盘
   * @param {Object} settings - 设置对象
   * @param {string} dashboardId - 仪表盘ID
   */
  setCurrentDashboard(settings, dashboardId) {
    this.ensureDashboardSettings(settings)
    settings.dashboard.currentDashboard = dashboardId
  }

  /**
   * 获取仪表盘配置
   * @param {Object} settings - 设置对象
   * @param {string} dashboardId - 仪表盘ID
   * @returns {Object} 仪表盘配置
   */
  getDashboardConfig(settings, dashboardId) {
    this.ensureDashboardSettings(settings)
    return settings.dashboard.configs[dashboardId] || {}
  }

  /**
   * 更新仪表盘配置
   * @param {Object} settings - 设置对象
   * @param {string} dashboardId - 仪表盘ID
   * @param {Object} config - 配置数据
   */
  updateDashboardConfig(settings, dashboardId, config) {
    this.ensureDashboardSettings(settings)

    if (!settings.dashboard.configs[dashboardId]) {
      settings.dashboard.configs[dashboardId] = {}
    }

    settings.dashboard.configs[dashboardId] = Object.assign(
      {},
      settings.dashboard.configs[dashboardId],
      config
    )
  }

  /**
   * 格式化货币显示
   * @param {Object} settings - 设置对象
   * @param {number} amount - 金额
   * @param {number} decimalPlaces - 小数位数（必须提供）
   * @returns {string} 格式化后的货币字符串
   */
  formatCurrency(settings, amount, decimalPlaces = 3) {
    const currencySymbol = settings.income.currencySymbol || '¥'
    return `${currencySymbol}${amount.toFixed(decimalPlaces)}`
  }

  /**
   * 检查是否需要隐私脱敏
   * @param {Object} settings - 设置对象
   * @param {string} field - 字段名
   * @returns {boolean} 是否需要脱敏
   */
  shouldMaskPrivacy(settings, field) {
    return settings.display && 
           settings.display.privacyMask && 
           settings.display.privacyMask[field] === true
  }

  /**
   * 应用隐私脱敏
   * @param {Object} settings - 设置对象
   * @param {string} field - 字段名
   * @param {string} value - 原始值
   * @returns {string} 脱敏后的值
   */
  applyPrivacyMask(settings, field, value) {
    if (this.shouldMaskPrivacy(settings, field)) {
      return '***'
    }
    return value
  }

  /**
   * 获取主题配置
   * @param {Object} settings - 设置对象
   * @returns {Object} 主题配置
   */
  getThemeConfig(settings) {
    const theme = settings.app.theme || 'default'
    
    // 这里可以根据主题返回不同的配置
    const themes = {
      default: {
        primaryColor: '#4F46E5',
        backgroundColor: '#F8FAFC',
        textColor: '#1F2937'
      },
      dark: {
        primaryColor: '#6366F1',
        backgroundColor: '#111827',
        textColor: '#F9FAFB'
      }
    }
    
    return themes[theme] || themes.default
  }

  /**
   * 添加数据变化监听器
   * @param {Function} listener - 监听器函数
   */
  addChangeListener(listener) {
    if (typeof listener === 'function') {
      this.changeListeners.push(listener)
    }
  }

  /**
   * 移除数据变化监听器
   * @param {Function} listener - 要移除的监听器函数
   */
  removeChangeListener(listener) {
    const index = this.changeListeners.indexOf(listener)
    if (index > -1) {
      this.changeListeners.splice(index, 1)
    }
  }

  /**
   * 通知所有监听器数据已变化
   */
  notifyChange() {
    this.changeListeners.forEach(listener => {
      try {
        listener()
      } catch (error) {
        console.error('设置变化监听器执行失败:', error)
      }
    })
  }

  // ==================== 数据操作方法 ====================

  /**
   * 更新用户设置数据
   * @param {Function} getSettingsCallback - 获取设置数据的回调函数
   * @param {Function} saveDataCallback - 保存数据的回调函数
   * @param {Function} notifyChangeCallback - 通知数据变化的回调函数
   * @param {Object} newSettings - 新的设置数据
   */
  updateSettingsData(getSettingsCallback, saveDataCallback, notifyChangeCallback, newSettings) {
    const currentSettings = getSettingsCallback()
    const mergedSettings = this.mergeSettings(currentSettings, newSettings)

    // 直接更新设置对象的属性
    Object.assign(currentSettings, mergedSettings)

    saveDataCallback()
    notifyChangeCallback()
  }

  /**
   * 设置当前仪表盘数据
   * @param {Function} getSettingsCallback - 获取设置数据的回调函数
   * @param {Function} saveDataCallback - 保存数据的回调函数
   * @param {Function} notifyChangeCallback - 通知数据变化的回调函数
   * @param {string} dashboardId - 仪表盘ID
   */
  setCurrentDashboardData(getSettingsCallback, saveDataCallback, notifyChangeCallback, dashboardId) {
    const settings = getSettingsCallback()
    this.setCurrentDashboard(settings, dashboardId)
    saveDataCallback()
    notifyChangeCallback()
  }

  /**
   * 更新仪表盘配置数据
   * @param {Function} getSettingsCallback - 获取设置数据的回调函数
   * @param {Function} saveDataCallback - 保存数据的回调函数
   * @param {Function} notifyChangeCallback - 通知数据变化的回调函数
   * @param {string} dashboardId - 仪表盘ID
   * @param {Object} config - 配置数据
   */
  updateDashboardConfigData(getSettingsCallback, saveDataCallback, notifyChangeCallback, dashboardId, config) {
    const settings = getSettingsCallback()
    this.updateDashboardConfig(settings, dashboardId, config)
    saveDataCallback()
    notifyChangeCallback()
  }

  /**
   * 获取仪表盘设置数据
   * @param {Function} getSettingsCallback - 获取设置数据的回调函数
   * @returns {Object} 仪表盘设置
   */
  getDashboardSettingsData(getSettingsCallback) {
    const settings = getSettingsCallback()
    this.ensureDashboardSettings(settings)
    return settings.dashboard
  }

  /**
   * 更新仪表盘设置数据
   * @param {Function} getSettingsCallback - 获取设置数据的回调函数
   * @param {Function} saveDataCallback - 保存数据的回调函数
   * @param {Function} notifyChangeCallback - 通知数据变化的回调函数
   * @param {Object} dashboardSettings - 仪表盘设置
   */
  updateDashboardSettingsData(getSettingsCallback, saveDataCallback, notifyChangeCallback, dashboardSettings) {
    const settings = getSettingsCallback()
    this.ensureDashboardSettings(settings)
    settings.dashboard = this.mergeSettings(settings.dashboard, dashboardSettings)
    saveDataCallback()
    notifyChangeCallback()
  }
}

// 导出单例实例
let settingsManagerInstance = null

function getSettingsManager() {
  if (!settingsManagerInstance) {
    settingsManagerInstance = new SettingsManager()
  }
  return settingsManagerInstance
}

module.exports = {
  SettingsManager,
  getSettingsManager
}
