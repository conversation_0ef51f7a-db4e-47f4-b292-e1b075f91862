/**
 * 货币工具类
 * 统一管理货币相关的功能，包括货币设置加载、货币单位映射等
 */

const { AppConfig } = require('../core/config/app.js')

/**
 * 货币工具类
 */
class CurrencyUtils {
  /**
   * 获取当前货币设置
   * @returns {Object} 货币设置对象 { currencyCode, currencySymbol, currencyConfig }
   */
  static getCurrentCurrencySettings() {
    try {
      const app = getApp()
      if (app && app.getDataManager) {
        const dataManager = app.getDataManager()
        const settings = dataManager.getSettings()
        const currencyCode = settings.income?.currencyCode || AppConfig.getDefaultCurrency().code
        const currencySymbol = settings.income?.currencySymbol || AppConfig.getDefaultCurrency().symbol
        const currencyConfig = AppConfig.getCurrencyConfig(currencyCode)
        
        return {
          currencyCode,
          currencySymbol,
          currencyConfig
        }
      }
    } catch (error) {
      console.error('获取货币设置失败:', error)
    }
    
    // 返回默认值
    const defaultCurrency = AppConfig.getDefaultCurrency()
    return {
      currencyCode: defaultCurrency.code,
      currencySymbol: defaultCurrency.symbol,
      currencyConfig: AppConfig.getCurrencyConfig(defaultCurrency.code)
    }
  }

  /**
   * 获取货币符号
   * @returns {string} 货币符号
   */
  static getCurrencySymbol() {
    const settings = this.getCurrentCurrencySettings()
    return settings.currencySymbol
  }

  /**
   * 获取货币单位
   * @returns {string} 货币单位（如：元、美元）
   */
  static getCurrencyUnit() {
    const settings = this.getCurrentCurrencySettings()
    return settings.currencyConfig.unit
  }

  /**
   * 获取时薪单位
   * @returns {string} 时薪单位（如：元/小时、美元/小时）
   */
  static getCurrencyHourlyUnit() {
    const settings = this.getCurrentCurrencySettings()
    return settings.currencyConfig.hourlyUnit
  }

  /**
   * 获取日薪单位
   * @returns {string} 日薪单位（如：元/天、美元/天）
   */
  static getCurrencyDailyUnit() {
    const settings = this.getCurrentCurrencySettings()
    return settings.currencyConfig.dailyUnit
  }

  /**
   * 获取月薪单位
   * @returns {string} 月薪单位（如：元/月、美元/月）
   */
  static getCurrencyMonthlyUnit() {
    const settings = this.getCurrentCurrencySettings()
    return settings.currencyConfig.monthlyUnit
  }

  /**
   * 获取完整的货币配置
   * @returns {Object} 完整的货币配置对象
   */
  static getCurrencyConfig() {
    const settings = this.getCurrentCurrencySettings()
    return settings.currencyConfig
  }

  /**
   * 为页面/组件加载货币设置
   * @param {Object} context - 页面或组件的this上下文
   * @param {Object} options - 配置选项
   * @param {boolean} options.includeSymbol - 是否包含货币符号
   * @param {boolean} options.includeUnit - 是否包含货币单位
   * @param {boolean} options.includeHourlyUnit - 是否包含时薪单位
   * @param {boolean} options.includeDailyUnit - 是否包含日薪单位
   * @param {boolean} options.includeMonthlyUnit - 是否包含月薪单位
   */
  static loadCurrencySettingsForContext(context, options = {}) {
    const {
      includeSymbol = true,
      includeUnit = false,
      includeHourlyUnit = false,
      includeDailyUnit = false,
      includeMonthlyUnit = false
    } = options

    const settings = this.getCurrentCurrencySettings()
    const updateData = {}

    if (includeSymbol) {
      updateData.currencySymbol = settings.currencySymbol
    }
    
    if (includeUnit) {
      updateData.currencyUnit = settings.currencyConfig.unit
    }
    
    if (includeHourlyUnit) {
      updateData.currencyHourlyUnit = settings.currencyConfig.hourlyUnit
    }
    
    if (includeDailyUnit) {
      updateData.currencyDailyUnit = settings.currencyConfig.dailyUnit
    }
    
    if (includeMonthlyUnit) {
      updateData.currencyMonthlyUnit = settings.currencyConfig.monthlyUnit
    }

    if (context && context.setData) {
      context.setData(updateData)
    }

    return updateData
  }

  /**
   * 格式化货币显示
   * @param {number} amount - 金额
   * @param {Object} options - 格式化选项
   * @param {number} options.decimals - 小数位数
   * @param {boolean} options.showSymbol - 是否显示货币符号
   * @param {string} options.customSymbol - 自定义货币符号
   * @returns {string} 格式化后的货币字符串
   */
  static formatCurrency(amount, options = {}) {
    const {
      decimals = 2,
      showSymbol = true,
      customSymbol = null
    } = options

    if (isNaN(Number(amount))) {
      return showSymbol ? `${customSymbol || this.getCurrencySymbol()}0.${'0'.repeat(decimals)}` : `0.${'0'.repeat(decimals)}`
    }

    const formattedAmount = Number(amount).toFixed(decimals)
    
    if (showSymbol) {
      const symbol = customSymbol || this.getCurrencySymbol()
      return `${symbol}${formattedAmount}`
    }
    
    return formattedAmount
  }

  /**
   * 获取所有支持的货币配置
   * @returns {Object} 所有货币配置
   */
  static getAllCurrencyConfigs() {
    return AppConfig.getAllCurrencyConfigs()
  }

  /**
   * 根据货币代码获取配置
   * @param {string} currencyCode - 货币代码
   * @returns {Object} 货币配置
   */
  static getCurrencyConfigByCode(currencyCode) {
    return AppConfig.getCurrencyConfig(currencyCode)
  }

  /**
   * 检查货币代码是否支持
   * @param {string} currencyCode - 货币代码
   * @returns {boolean} 是否支持
   */
  static isSupportedCurrency(currencyCode) {
    return AppConfig.isSupportedCurrency(currencyCode)
  }
}

module.exports = {
  CurrencyUtils
}
