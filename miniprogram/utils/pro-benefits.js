/**
 * Pro 用户权益管理工具
 * 基于用户信息中的 vip.status 进行权益判断
 */

/**
 * 检查是否为 Pro 用户
 * @returns {boolean} 是否为 Pro 用户
 */
function isProUser() {
  try {
    const app = getApp()
    const userManager = app.getUserManager()

    if (!userManager) {
      return false
    }

    const userInfo = userManager.getUserInfo()
    return userInfo?.vip?.status === true
  } catch (error) {
    console.error('检查 Pro 用户状态失败:', error)
    return false
  }
}

/**
 * 获取用户类型文本
 * @returns {string} 用户类型
 */
function getUserTypeText() {
  return isProUser() ? 'Pro 用户' : '普通用户'
}

/**
 * Pro 用户权益配置
 */
const PRO_BENEFITS = {
  // 数据同步 - 所有用户都有，但普通用户后续可能移除
  DATA_SYNC: {
    pro: true,
    free: true,
    note: '后续可能移除普通用户的自动同步权益'
  },
  
  // 历史数据加载 - Pro 用户 7 天，普通用户不可用
  HISTORY_DATA: {
    pro: 7, // 7天
    free: 0  // 不可用
  },
  
  // 主题切换 - Pro 用户所有主题，普通用户只能默认主题
  THEME_SWITCH: {
    pro: true,
    free: false
  },
  
  // 数据统计页面 - Pro 用户可访问，普通用户不可访问
  STATISTICS_PAGE: {
    pro: true,
    free: false
  },
  
  // 工作履历限制 - Pro 用户无限制，普通用户最多 2 个
  WORK_HISTORY_LIMIT: {
    pro: -1, // 无限制
    free: 2  // 最多 2 个
  },
  
  // 批量复制功能 - Pro 用户可用，普通用户不可用
  BATCH_COPY: {
    pro: true,
    free: false
  }
}

/**
 * 检查数据同步权益
 * @returns {Object} 权益信息
 */
function checkDataSyncBenefit() {
  const isPro = isProUser()
  const benefit = PRO_BENEFITS.DATA_SYNC
  
  return {
    hasAccess: isPro ? benefit.pro : benefit.free,
    note: benefit.note,
    userType: getUserTypeText()
  }
}

/**
 * 检查历史数据加载权益
 * @returns {Object} 权益信息
 */
function checkHistoryDataBenefit() {
  const isPro = isProUser()
  const benefit = PRO_BENEFITS.HISTORY_DATA
  
  return {
    hasAccess: isPro ? benefit.pro > 0 : benefit.free > 0,
    days: isPro ? benefit.pro : benefit.free,
    userType: getUserTypeText()
  }
}

/**
 * 检查主题切换权益
 * @returns {Object} 权益信息
 */
function checkThemeSwitchBenefit() {
  const isPro = isProUser()
  const benefit = PRO_BENEFITS.THEME_SWITCH
  
  return {
    hasAccess: isPro ? benefit.pro : benefit.free,
    userType: getUserTypeText()
  }
}

/**
 * 检查数据统计页面权益
 * @returns {Object} 权益信息
 */
function checkStatisticsPageBenefit() {
  const isPro = isProUser()
  const benefit = PRO_BENEFITS.STATISTICS_PAGE
  
  return {
    hasAccess: isPro ? benefit.pro : benefit.free,
    userType: getUserTypeText()
  }
}

/**
 * 检查工作履历数量限制
 * @param {number} currentCount 当前工作履历数量
 * @returns {Object} 权益信息
 */
function checkWorkHistoryLimit(currentCount = 0) {
  const isPro = isProUser()
  const benefit = PRO_BENEFITS.WORK_HISTORY_LIMIT
  const limit = isPro ? benefit.pro : benefit.free
  
  return {
    hasAccess: limit === -1 || currentCount < limit,
    canAdd: limit === -1 || currentCount < limit,
    limit: limit,
    currentCount: currentCount,
    userType: getUserTypeText()
  }
}

/**
 * 检查批量复制权益
 * @returns {Object} 权益信息
 */
function checkBatchCopyBenefit() {
  const isPro = isProUser()
  const benefit = PRO_BENEFITS.BATCH_COPY
  
  return {
    hasAccess: isPro ? benefit.pro : benefit.free,
    userType: getUserTypeText()
  }
}

/**
 * 获取所有权益状态
 * @returns {Object} 所有权益状态
 */
function getAllBenefits() {
  const isPro = isProUser()
  
  return {
    userType: getUserTypeText(),
    isPro: isPro,
    benefits: {
      dataSync: checkDataSyncBenefit(),
      historyData: checkHistoryDataBenefit(),
      themeSwitch: checkThemeSwitchBenefit(),
      statisticsPage: checkStatisticsPageBenefit(),
      workHistoryLimit: checkWorkHistoryLimit(),
      batchCopy: checkBatchCopyBenefit()
    }
  }
}

/**
 * 检查主题到期处理
 * 当用户从 Pro 降级为普通用户时，自动切换回默认主题
 */
function checkAndHandleThemeExpiry() {
  try {
    const themeAccess = checkThemeSwitchBenefit()
    
    if (!themeAccess.hasAccess) {
      // 普通用户只能使用默认主题，强制切换
      const app = getApp()
      const dataManager = app.getDataManager()
      
      if (dataManager) {
        const currentDashboard = dataManager.getCurrentDashboard()
        if (currentDashboard !== 'dashboard1') {
          console.log('用户权益到期，自动切换回默认主题')
          dataManager.setCurrentDashboard('dashboard1')
        }
      }
    }
  } catch (error) {
    console.error('检查主题到期处理失败:', error)
  }
}

module.exports = {
  isProUser,
  getUserTypeText,
  checkDataSyncBenefit,
  checkHistoryDataBenefit,
  checkThemeSwitchBenefit,
  checkStatisticsPageBenefit,
  checkWorkHistoryLimit,
  checkBatchCopyBenefit,
  getAllBenefits,
  checkAndHandleThemeExpiry,
  PRO_BENEFITS
}
