/**
 * Pro会员权益控制系统
 * 基于用户数据中的vip.status字段，提供响应式的权益控制
 */

/**
 * 检查是否为Pro会员
 * @returns {boolean} 是否为Pro会员
 */
function isProMember() {
  try {
    const app = getApp()
    const userManager = app.getUserManager()

    if (!userManager) {
      return false
    }

    const userInfo = userManager.getUserInfo()
    return userInfo?.vip?.status === true
  } catch (error) {
    console.error('检查Pro会员状态失败:', error)
    return false
  }
}

/**
 * 获取Pro会员权益配置
 * @returns {Object} 权益配置
 */
function getProMembershipConfig() {
  const isPro = isProMember()
  
  return {
    // 基础信息
    isPro: isPro,
    memberType: isPro ? 'Pro会员' : '普通用户',
    
    // 功能权益
    features: {
      // 数据同步 - 所有用户都有，但普通用户可能会被移除
      dataSync: true,
      
      // 历史数据加载 - Pro用户7天，普通用户不可用
      historyDataLoad: isPro,
      historyDataDays: isPro ? 7 : 0,
      
      // 主题切换 - Pro用户所有主题，普通用户仅默认主题
      themeSwitch: isPro,
      availableThemes: isPro ? 'all' : 'default',
      
      // 数据统计页面 - Pro用户可访问，普通用户不可访问
      dataStatistics: isPro,
      
      // 批量复制日期安排 - Pro用户可用，普通用户不可用
      batchCopySchedule: isPro
    },
    
    // 使用限制
    limits: {
      // 工作履历限制 - Pro用户无限制，普通用户最多2个
      workHistories: isPro ? -1 : 2
    }
  }
}

/**
 * 检查功能权限
 * @param {string} featureName - 功能名称
 * @returns {boolean} 是否有权限
 */
function checkFeaturePermission(featureName) {
  const config = getProMembershipConfig()
  
  switch (featureName) {
    case 'historyDataLoad':
      return config.features.historyDataLoad
    case 'themeSwitch':
      return config.features.themeSwitch
    case 'dataStatistics':
      return config.features.dataStatistics
    case 'batchCopySchedule':
      return config.features.batchCopySchedule
    case 'dataSync':
      return config.features.dataSync
    default:
      return true // 未知功能默认允许
  }
}

/**
 * 检查使用限制
 * @param {string} limitType - 限制类型
 * @param {number} currentValue - 当前值
 * @returns {boolean} 是否在限制内
 */
function checkUsageLimit(limitType, currentValue) {
  const config = getProMembershipConfig()
  
  switch (limitType) {
    case 'workHistories':
      const limit = config.limits.workHistories
      return limit === -1 || currentValue < limit
    default:
      return true // 未知限制类型默认允许
  }
}

/**
 * 获取功能限制信息
 * @param {string} limitType - 限制类型
 * @returns {Object} 限制信息
 */
function getLimitInfo(limitType) {
  const config = getProMembershipConfig()
  
  switch (limitType) {
    case 'workHistories':
      return {
        limit: config.limits.workHistories,
        unlimited: config.limits.workHistories === -1,
        displayText: config.limits.workHistories === -1 ? '无限制' : `最多${config.limits.workHistories}个`
      }
    default:
      return {
        limit: -1,
        unlimited: true,
        displayText: '无限制'
      }
  }
}

/**
 * 创建响应式权益监听器
 * @param {Function} callback - 权益变化回调函数
 * @returns {Function} 移除监听器的函数
 */
function createMembershipListener(callback) {
  try {
    const app = getApp()
    const userManager = app.getUserManager()
    
    if (!userManager) {
      console.warn('用户管理器未初始化，无法创建权益监听器')
      return () => {}
    }
    
    // 创建用户信息变化监听器
    const listener = (userInfo) => {
      const newConfig = getProMembershipConfig()
      callback(newConfig, userInfo)
    }
    
    // 添加监听器
    userManager.addChangeListener(listener)
    
    // 返回移除监听器的函数
    return () => {
      userManager.removeChangeListener(listener)
    }
  } catch (error) {
    console.error('创建权益监听器失败:', error)
    return () => {}
  }
}

/**
 * 获取权益对比数据（用于会员中心展示）
 * @returns {Object} 权益对比数据
 */
function getMembershipComparison() {
  return {
    features: [
      {
        name: '数据同步',
        icon: '☁️',
        free: '✓ 自动同步',
        pro: '✓ 自动同步',
        note: '* 普通用户后续可能因成本考虑移除此功能'
      },
      {
        name: '历史数据加载',
        icon: '📅',
        free: '✗ 不可用',
        pro: '✓ 过去7天数据',
        note: ''
      },
      {
        name: '主题切换',
        icon: '🎨',
        free: '仅默认主题',
        pro: '✓ 所有主题',
        note: '到期后自动切换回默认主题'
      },
      {
        name: '数据统计页面',
        icon: '📊',
        free: '✗ 不可访问',
        pro: '✓ 完整访问',
        note: ''
      },
      {
        name: '工作履历',
        icon: '💼',
        free: '最多2个',
        pro: '✓ 无限制',
        note: ''
      },
      {
        name: '批量复制日期安排',
        icon: '📋',
        free: '✗ 不可用',
        pro: '✓ 可用',
        note: ''
      }
    ]
  }
}

module.exports = {
  isProMember,
  getProMembershipConfig,
  checkFeaturePermission,
  checkUsageLimit,
  getLimitInfo,
  createMembershipListener,
  getMembershipComparison
}
