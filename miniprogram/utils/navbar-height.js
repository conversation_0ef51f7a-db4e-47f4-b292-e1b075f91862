/**
 * 导航栏高度计算工具
 * 提供统一的导航栏高度计算逻辑，确保在不同设备上的一致性
 */

/**
 * 计算导航栏高度
 * @param {Object} options - 配置选项
 * @param {boolean} options.enableLog - 是否启用日志输出，默认false
 * @param {string} options.logPrefix - 日志前缀，默认'NavbarHeight'
 * @returns {Object} 计算结果
 */
function calculateNavbarHeight(options = {}) {
  const { enableLog = false, logPrefix = 'NavbarHeight' } = options
  
  try {
    // 获取窗口信息
    const windowInfo = wx.getWindowInfo()
    const { safeArea, statusBarHeight, screenWidth } = windowInfo
    
    // 1. 获取更准确的状态栏高度（优先使用safeArea.top）
    const actualStatusBarHeight = (safeArea && safeArea.top) || statusBarHeight || 20
    
    // 2. 计算rpx到px的精确转换比例
    const rpxToPxRatio = screenWidth / 750
    
    // 3. 计算导航栏内容高度（88rpx转为px）
    const navbarContentHeight = Math.round(88 * rpxToPxRatio)
    
    // 4. 计算总的导航栏高度
    const totalNavbarHeight = actualStatusBarHeight + navbarContentHeight
    
    const result = {
      statusBarHeight: actualStatusBarHeight,
      navbarHeight: totalNavbarHeight,
      navbarContentHeight,
      rpxToPxRatio,
      screenWidth
    }
    
    // 可选的日志输出
    if (enableLog) {
      console.log(`[${logPrefix}] 导航栏高度计算:`, {
        actualStatusBarHeight,
        navbarContentHeight,
        totalNavbarHeight,
        screenWidth,
        rpxToPxRatio: rpxToPxRatio.toFixed(4),
        calculation: `${actualStatusBarHeight}px (状态栏) + ${navbarContentHeight}px (导航栏内容) = ${totalNavbarHeight}px`
      })
    }
    
    return result
    
  } catch (error) {
    console.error(`[${logPrefix}] 导航栏高度计算失败:`, error)
    
    // 返回默认值
    return {
      statusBarHeight: 20,
      navbarHeight: 64, // 20 + 44 (iPhone 6/7/8 默认值)
      navbarContentHeight: 44,
      rpxToPxRatio: 0.5,
      screenWidth: 375
    }
  }
}

/**
 * 为页面或组件设置导航栏高度数据
 * @param {Object} context - 页面或组件的this上下文
 * @param {Object} options - 配置选项
 * @param {boolean} options.enableLog - 是否启用日志输出
 * @param {string} options.logPrefix - 日志前缀
 */
function setNavbarHeightData(context, options = {}) {
  const result = calculateNavbarHeight(options)
  
  if (context && typeof context.setData === 'function') {
    context.setData({
      statusBarHeight: result.statusBarHeight,
      navbarHeight: result.navbarHeight
    })
  }
  
  return result
}

/**
 * 获取导航栏样式对象
 * @param {Object} options - 配置选项
 * @param {string} options.type - 样式类型: 'marginTop' | 'paddingTop' | 'height'
 * @param {number} options.extraHeight - 额外高度，单位px
 * @returns {string} 样式字符串
 */
function getNavbarStyle(options = {}) {
  const { type = 'marginTop', extraHeight = 0 } = options
  const { navbarHeight } = calculateNavbarHeight()
  const totalHeight = navbarHeight + extraHeight
  
  switch (type) {
    case 'marginTop':
      return `margin-top: ${totalHeight}px;`
    case 'paddingTop':
      return `padding-top: ${totalHeight}px;`
    case 'height':
      return `height: calc(100vh - ${totalHeight}px);`
    case 'minHeight':
      return `min-height: calc(100vh - ${totalHeight}px);`
    case 'marginTopAndHeight':
      return `margin-top: ${totalHeight}px; height: calc(100vh - ${totalHeight}px);`
    default:
      return `margin-top: ${totalHeight}px;`
  }
}

module.exports = {
  calculateNavbarHeight,
  setNavbarHeightData,
  getNavbarStyle
}
