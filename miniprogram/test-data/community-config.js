/**
 * 摸鱼社群配置数据
 * 用于测试社群模态框功能
 */

// 测试配置数据
const communityConfig = {
  key: 'community_group_info',
  value: `# 🐟 加入摸鱼社群

欢迎加入我们的官方用户交流群！在这里你可以：

- **交流使用心得**：分享时间管理技巧
- **获取最新资讯**：第一时间了解功能更新
- **反馈问题建议**：直接与开发者沟通
- **结识志同道合的朋友**：一起提升工作效率

![群聊二维码](cloud://cloud1-0gh84voi27a21ce3.636c-cloud1-0gh84voi27a21ce3-1370781280/images/wechat_community_group_qrcode.webp )

## 如何加入

1. 长按上方二维码图片
2. 选择"识别图中二维码"
3. 点击加入群聊

> 💡 **提示**：群聊二维码会定期更新，请及时保存。如果二维码失效，请通过意见反馈联系我们获取最新二维码。

---

期待与你在群里相遇！ 🎉`,
  enable: true,
  description: '摸鱼社群信息配置'
}

/**
 * 添加配置到数据库的云函数调用示例
 * 注意：这需要在微信开发者工具的云开发控制台中执行
 */
function addCommunityConfig() {
  // 在云开发控制台的数据库中手动添加，或者通过云函数添加
  console.log('请将以下配置数据添加到 config 集合中：')
  console.log(JSON.stringify(communityConfig, null, 2))
}

/**
 * 更新配置的云函数调用示例
 */
function updateCommunityConfig(newValue) {
  // 可以通过云函数更新配置
  const updateData = {
    ...communityConfig,
    value: newValue,
    updatedAt: new Date()
  }
  console.log('更新配置数据：')
  console.log(JSON.stringify(updateData, null, 2))
}

module.exports = {
  communityConfig,
  addCommunityConfig,
  updateCommunityConfig
}

/**
 * 使用说明：
 * 
 * 1. 手动添加配置数据：
 *    - 打开微信开发者工具
 *    - 进入云开发控制台
 *    - 选择数据库 > config 集合
 *    - 点击"添加记录"
 *    - 复制上面的 communityConfig 对象内容
 *    - 粘贴并保存
 * 
 * 2. 通过云函数添加：
 *    - 创建一个临时云函数
 *    - 调用 configDB.createConfig(communityConfig)
 *    - 部署并调用云函数
 * 
 * 3. 测试功能：
 *    - 重启小程序确保配置加载
 *    - 进入个人页面
 *    - 点击"加入摸鱼社群"按钮
 *    - 验证模态框显示和markdown解析
 * 
 * 4. 自定义配置：
 *    - 替换示例图片URL为真实的群聊二维码
 *    - 根据需要调整文案内容
 *    - 可以添加更多markdown元素测试解析功能
 */
