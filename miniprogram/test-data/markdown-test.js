/**
 * Markdown解析器测试
 * 用于验证图片解析功能
 */

import { parseMarkdown } from '../utils/markdown-parser.js'

// 测试用的markdown内容
const testMarkdown = `# 测试标题

这是一个段落，包含**粗体**和*斜体*文本。

![测试图片](https://example.com/test.png)

## 列表测试

- 第一项
- 第二项包含![内联图片](https://example.com/inline.png)
- 第三项

> 这是一个引用，包含![引用中的图片](https://example.com/quote.png)

---

最后一个段落。`

/**
 * 测试markdown解析功能
 */
function testMarkdownParser() {
  console.log('开始测试Markdown解析器...')
  
  const result = parseMarkdown(testMarkdown)
  
  console.log('解析结果：', result)
  
  // 验证图片解析
  const imageBlocks = result.filter(block => block.type === 'image')
  console.log('独立图片块：', imageBlocks)
  
  // 验证内联图片解析
  const paragraphsWithImages = result.filter(block => 
    block.type === 'paragraph' && 
    block.text.some(span => span.type === 'image')
  )
  console.log('包含内联图片的段落：', paragraphsWithImages)
  
  return result
}

/**
 * 在页面中使用测试
 * 可以在任何页面的onLoad中调用此函数进行测试
 */
function runTest() {
  try {
    const result = testMarkdownParser()
    
    // 检查是否正确解析了图片
    const hasImages = result.some(block => 
      block.type === 'image' || 
      (block.type === 'paragraph' && block.text.some(span => span.type === 'image'))
    )
    
    if (hasImages) {
      console.log('✅ 图片解析功能正常')
      wx.showToast({
        title: '图片解析正常',
        icon: 'success'
      })
    } else {
      console.log('❌ 图片解析功能异常')
      wx.showToast({
        title: '图片解析异常',
        icon: 'error'
      })
    }
    
    return result
  } catch (error) {
    console.error('测试失败：', error)
    wx.showToast({
      title: '测试失败',
      icon: 'error'
    })
  }
}

module.exports = {
  testMarkdown,
  testMarkdownParser,
  runTest
}

/**
 * 使用方法：
 * 
 * 1. 在任何页面的js文件中导入：
 *    const { runTest } = require('../../test-data/markdown-test.js')
 * 
 * 2. 在页面的onLoad方法中调用：
 *    onLoad() {
 *      // 其他代码...
 *      runTest() // 测试markdown解析
 *    }
 * 
 * 3. 查看控制台输出验证解析结果
 * 
 * 4. 或者在社群模态框中直接使用testMarkdown作为测试内容
 */
